import {ActivityIndicator, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {COLORS} from '../../themes/themes';
import ButtonwithIcon from '../Buttons/ButtonwithIcon';
import {Location, Star, Verticaldot} from '../../assets/svgIcons';
import TextButtonwithIcon from '../Buttons/TextButtonwithIcon';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from 'react-native-popup-menu';
import FastImage from 'react-native-fast-image';
import {styles} from './styles';
import {placeholder} from '../../assets/images';
import {useTranslation} from 'react-i18next';

interface Props {
  onEditPress?: () => void;
  onDeletePress: () => void;
  isTopRating?: boolean;
  petName: string;
  city: string;
  imageURI: string;
  petPicture: String;
  bookingDate?: string;
  onProfilePress?: () => void;
  onPetPress?: () => void;
}

const MyPetCard: React.FC<Props> = ({
  onEditPress,
  onDeletePress,
  isTopRating,
  petName,
  city,
  imageURI,
  petPicture,
  bookingDate,
  onProfilePress,
  onPetPress,
}) => {
  const [opened, setOpened] = React.useState<boolean>(false);
  const [imageLoading, setImageLoading] = React.useState<boolean>(true);

  const {t} = useTranslation();

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPetPress}
      activeOpacity={onPetPress ? 0.7 : 1}>
      <FastImage
        source={petPicture ? {uri: petPicture} : placeholder}
        style={[
          styles.imageContainer,
          {justifyContent: isTopRating ? 'space-between' : 'flex-end'},
        ]}
        onLoad={() => {
          setImageLoading(true);
        }}
        onLoadEnd={() => {
          setImageLoading(false);
        }}>
        {imageLoading && (
          <ActivityIndicator
            size="small"
            color={COLORS.primary}
            style={styles.loadingIndicator}
          />
        )}
        <View />

        <View>
          <Menu opened={opened} onBackdropPress={() => setOpened(false)}>
            <MenuTrigger />
            <MenuOptions
              customStyles={{
                optionsContainer: styles.popUpMenuContainer,
              }}>
              {onEditPress ? (
                <>
                  <MenuOption
                    onSelect={() => {
                      onEditPress();
                      setOpened(false);
                    }}>
                    <Text style={styles.menuLabel}>{t('Edit')}</Text>
                  </MenuOption>
                  <View style={styles.divier} />
                </>
              ) : null}
              <MenuOption
                onSelect={() => {
                  onDeletePress();
                  setOpened(false);
                }}>
                <Text style={styles.menuLabel}>{t('Delete')}</Text>
              </MenuOption>
            </MenuOptions>
          </Menu>
          <ButtonwithIcon
            onPress={() => {
              setOpened(true);
            }}
            icon={<Verticaldot />}
            containerStyle={{padding: 12}}
          />
        </View>
      </FastImage>
      <View style={styles.innerContainer}>
        <TouchableOpacity onPress={onProfilePress}>
          <FastImage source={{uri: imageURI}} style={styles.profileImage} />
        </TouchableOpacity>
        <View style={styles.userContainer}>
          <View>
            <Text style={styles.name}>{petName}</Text>
            <TextButtonwithIcon
              label={city}
              labelStyle={styles.label2}
              leftIcon={<Location />}
              disabled
            />
          </View>
          {isTopRating ? (
            <Text style={styles.dateText}>{bookingDate}</Text>
          ) : null}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default MyPetCard;
