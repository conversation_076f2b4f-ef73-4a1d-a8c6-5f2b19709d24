import {Dimensions} from 'react-native';

export const COLORS = {
  primary: '#FF8C0E',
  white: '#FFFFFF',
  black: '#000000',
  whiteSmoke: '#F4F4F4',
  darkGray: '#A0A0A0',
  placeHolder: '#BEBEBE',
  lightGray: '#E2E2E2',
  Philippine_Gray: '#8E8E93',
  Lavender: '#E5E5EA',
  spanishgray: '#989898',
  Bastille: '#2C2C2E',
  gray85: '#D9D9D9',
};

export const SIZES = {
  DeviceHeight: Dimensions.get('window').height,
  DeviceWidth: Dimensions.get('window').width,
};
