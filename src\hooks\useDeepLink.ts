import {useEffect} from 'react';
import {Linking} from 'react-native';
import {useNavigation} from '@react-navigation/native';

export const useDeepLink = () => {
  const navigation = useNavigation();

  useEffect(() => {
    // Handle deep link when app is already open
    const handleDeepLink = (url: string) => {
      console.log('Deep link received:', url);

      // Parse the URL and navigate accordingly
      if (url) {
        // Remove the scheme prefix
        const route = url.replace('petsit://', '');
        console.log('Parsed route:', route);

        // Handle different routes
        handleNavigation(route);
      }
    };

    // Listen for deep links when app is already running
    const linkingListener = Linking.addEventListener('url', event => {
      handleDeepLink(event.url);
    });

    // Handle deep link when app is launched from closed state
    Linking.getInitialURL().then(url => {
      if (url) {
        handleDeepLink(url);
      }
    });

    return () => {
      linkingListener?.remove();
    };
  }, [navigation]);

  const handleNavigation = (route: string) => {
    try {
      // Split route into segments
      const segments = route.split('/');
      const mainRoute = segments[0];

      if (mainRoute === 'payment-onboarding') {
        const uid = segments[1]; // Get uid parameter if present

        (navigation as any).navigate('PaymentProfile', {
          uri: uid
            ? `petsit://payment-onboarding/${uid}`
            : 'petsit://payment-onboarding',
          uid: uid || undefined,
        });
      } else {
        console.log('Unsupported deep link route:', mainRoute);
      }
    } catch (error) {
      console.error('Error handling deep link navigation:', error);
    }
  };

  return {handleNavigation};
};
