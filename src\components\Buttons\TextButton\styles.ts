import {StyleSheet} from 'react-native';
import {verticalScale} from 'react-native-size-matters';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    height: verticalScale(40),
    backgroundColor: COLORS.primary,
    width: '100%',
    borderRadius: 5,
  },
  label: {
    fontSize: verticalScale(16),
    color: COLORS.white,
    fontFamily: FONTS.SemiBold,
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
});
