import {Image, Text, View, TouchableOpacity} from 'react-native';
import React, {useEffect, useState} from 'react';
import firestore from '@react-native-firebase/firestore';
import {IChat} from '../../../interfaces/IChat';
import {useAppSelector} from '../../../store/Store';
import {placeholderImage} from '../../../assets/images';
import {IUser} from '../../../interfaces';
import {styles} from './styles';

interface Props {
  handleChatPress: (user?: IUser, item?: IChat) => void;
  message: string;
  time?: string;
  item: IChat;
}

const ChatCard: React.FC<Props> = ({item, handleChatPress, message, time}) => {
  const [recipientData, setRecipientData] = useState<IUser>();
  const user = useAppSelector(state => state.user);

  useEffect(() => {
    const recipient = item.members.find(id => id !== user.uid);
    if (recipient) {
      fetchRecipient(recipient);
    }
  }, []);

  const fetchRecipient = async (id: string) => {
    let user = await firestore().collection('Users').doc(id).get();

    if (user.exists) {
      setRecipientData({...user.data(), uid: user.id} as IUser);
    }
  };

  return (
    <TouchableOpacity
      disabled={!recipientData}
      onPress={() => {
        handleChatPress(recipientData, item);
      }}>
      <View style={styles.chatCardContainer}>
        <Image
          source={
            recipientData?.profilePicture
              ? {uri: recipientData?.profilePicture}
              : placeholderImage
          }
          style={styles.profileImage}
          loadingIndicatorSource={placeholderImage}
          resizeMode="contain"
        />
        <View style={styles.chatCardInnerContainer}>
          <View style={styles.messageContainer}>
            <Text style={styles.recipientName}>
              {recipientData?.firstName
                ? recipientData?.firstName + ' ' + recipientData.surName
                : ''}
            </Text>

            <Text numberOfLines={2} style={[styles.messageText]}>
              {message}
            </Text>
          </View>
          <View style={styles.timeContainer}>
            <Text style={styles.messageText}>{time}</Text>
            {item.unReadMessages && item.unReadMessages > 0 ? (
              <View style={styles.unreadMessageBadge}>
                <Text style={styles.unreadMessageText}>
                  {item.unReadMessages}
                </Text>
              </View>
            ) : null}
          </View>
        </View>
      </View>
      <View style={styles.dividerLine} />
    </TouchableOpacity>
  );
};

export default ChatCard;
