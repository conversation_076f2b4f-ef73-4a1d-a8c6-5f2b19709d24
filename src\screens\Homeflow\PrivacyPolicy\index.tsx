import {StyleSheet, View} from 'react-native';
import React, {useEffect} from 'react';
import {ButtonwithIcon} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import {COLORS} from '../../../themes/themes';
import appStyles from '../../../themes/appStyles';
import {FONTS} from '../../../themes/fonts';
import {verticalScale} from 'react-native-size-matters';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import {WebView} from 'react-native-webview';

type Props = NativeStackScreenProps<PetOwnerParamList, 'PrivacyPolicy'>;

const PrivacyPolicy: React.FC<Props> = ({navigation}) => {
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);

  return (
    <View style={styles.container}>
      <WebView
        source={{uri: 'https://mypetsit.be/privacybeleid'}}
        style={{flex: 1}}
      />
    </View>
  );
};

export default PrivacyPolicy;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: verticalScale(8),
    paddingHorizontal: 24,
  },
  imageContainer: {
    width: verticalScale(90),
    height: verticalScale(90),
    alignSelf: 'center',
    marginTop: 14,
  },
  paragraph: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
    paddingHorizontal: 10,
    paddingTop: verticalScale(16),
  },
  button: {
    paddingHorizontal: 10,
    paddingTop: 16,
  },
});
