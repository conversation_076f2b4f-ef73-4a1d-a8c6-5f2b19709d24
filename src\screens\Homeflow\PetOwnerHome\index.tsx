import {
  SafeAreaView,
  TouchableWithoutFeedback,
  View,
  Keyboard,
  FlatList,
  Text,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {ButtonwithIcon, Filter, PetCard, SeeAll} from '../../../components';
import {EmptyState, Filter2} from '../../../assets/svgIcons';
import Header from './Header';
import firestore from '@react-native-firebase/firestore';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {OwnerBottomStackParamList} from '../../../navigation/PetOwnerBottomTabs';
import {styles} from './styles';
import {IFilters, IUser} from '../../../interfaces';
import {useAppSelector} from '../../../store/Store';
import {useTranslation} from 'react-i18next';
import {COLORS} from '../../../themes/themes';
import * as geofirestore from 'geofirestore';
import {getGeoQuery} from '../../../helpers/query-helper';
import {
  BannerAdComponent,
  InterstitialAdComponent,
} from '../../../components/Admob';

type Props = NativeStackScreenProps<OwnerBottomStackParamList, 'PetOwnerHome'>;
const ITEMS_PER_PAGE = 10;

const PetOwnerHome: React.FC<Props> = ({navigation}) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [petSitters, setPetSitters] = useState<IUser[]>([]);
  const [filterApplied, setFilterApplied] = useState(false);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  const user = useAppSelector(state => state.user);
  const {t} = useTranslation();

  useEffect(() => {
    getPetSitters();
  }, []);

  const getPetSitters = () => {
    const firestoreApp = firestore();
    const GeoFirestore = geofirestore.initializeApp(firestoreApp);
    const geocollection = GeoFirestore.collection('Users');

    const query = getGeoQuery(
      geocollection,
      user.coordinates?.latitude || 0,
      user.coordinates?.longitude || 0,
      100,
    );

    // Short
    // const query = firestore()
    //   .collection('Users')
    //   .where('userType', '==', 'petSitter');

    query.onSnapshot((querySnapshot: any) => {
      const _nearByPetsitters: IUser[] = querySnapshot.docs.map((doc: any) => ({
        ...doc.data(),
        uid: doc.id,
      })) as IUser[];
      const result = _nearByPetsitters.filter(
        item =>
          item.userType == 'petSitter' &&
          item.profileStatus === 'approved' &&
          !item.isBlocked,
      );

      setPetSitters(result);
      setCurrentPage(1);
      setLoading(false);
      setLoadingMore(false);
      // setDisplayedPetSitters(result.slice(0, ITEMS_PER_PAGE));
    });
  };

  // const fetchMorePetSitters = () => {
  //   if (loadingMore) return;
  //   setLoadingMore(true);
  //   const nextPage = currentPage + 1;
  //   const nextItems = petSitters.slice(0, nextPage * ITEMS_PER_PAGE);
  //   setDisplayedPetSitters(nextItems);
  //   setCurrentPage(nextPage);
  //   setLoadingMore(false);
  // };

  const handleApplyFilter = (filters: IFilters) => {
    setModalVisible(false);
    setFilterApplied(true);
    const firestoreApp = firestore();
    const GeoFirestore = geofirestore.initializeApp(firestoreApp);
    const geocollection = GeoFirestore.collection('Users');
    const query = getGeoQuery(
      geocollection,
      filters.location?.latitude || 0,
      filters.location?.longitude || 0,
      filters.radius || 0,
    );
    try {
      query.onSnapshot((querySnapshot: any) => {
        const _nearByPetsitters: IUser[] = querySnapshot.docs.map(
          (doc: any) => ({
            ...doc.data(),
            uid: doc.id,
          }),
        ) as IUser[];

        const result = _nearByPetsitters.filter(
          item =>
            item.userType == 'petSitter' &&
            item.profileStatus === 'approved' &&
            !item.isBlocked,
        );

        setPetSitters(result);
        setCurrentPage(1);
        // setDisplayedPetSitters(result.slice(0, ITEMS_PER_PAGE));
      });
    } catch (error) {
      console.log('Error filtering data', error);
    }
  };

  const handleCancelFilter = () => {
    setFilterApplied(false);
    getPetSitters();
    setCurrentPage(1);
  };

  const filterLabel = filterApplied ? t('Filtered results') : t('Near to you');
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView style={styles.container}>
        <BannerAdComponent />
        <View style={styles.innerContainer}>
          <Header onPress={() => navigation.navigate('Notification')} />
          <InterstitialAdComponent />

          <View style={styles.searchBarContainer}>
            <View />
            <ButtonwithIcon
              icon={<Filter2 />}
              containerStyle={styles.filterButton}
              onPress={() => setModalVisible(true)}
            />
          </View>

          <View style={styles.PetContainer}>
            <FlatList
              showsVerticalScrollIndicator={false}
              columnWrapperStyle={{justifyContent: 'space-between'}}
              data={petSitters}
              numColumns={2}
              contentContainerStyle={{paddingBottom: 80}}
              ListHeaderComponent={() => (
                <View style={{marginBottom: 16}}>
                  {filterApplied ? (
                    <TouchableOpacity
                      style={styles.cancelFilterContainer}
                      hitSlop={5}
                      onPress={handleCancelFilter}>
                      <Text style={styles.cancelFilter}>
                        {t('Cancel filter')}
                      </Text>
                    </TouchableOpacity>
                  ) : null}

                  <SeeAll
                    heading={filterLabel}
                    onPress={() => {
                      navigation.navigate('PetOwnerSeeAll');
                    }}
                  />
                </View>
              )}
              renderItem={({item}) => {
                return (
                  <PetCard
                    image={item.profilePicture}
                    name={
                      item.firstName ? `${item.firstName} ${item.surName}` : ''
                    }
                    location={item.city}
                    experience={item.experience}
                    rating={item.rating}
                    onPress={() =>
                      navigation.navigate('PetSitterDetails', {
                        petSitter: item,
                      })
                    }
                    containerStyle={{marginBottom: 16}}
                  />
                );
              }}
              ListEmptyComponent={() => (
                <View style={{alignItems: 'center', marginTop: 50}}>
                  <EmptyState />
                  <Text style={styles.emptyText}>
                    {t('No pet sitters near you')}
                  </Text>
                  <Text style={styles.emptyDesc}>
                    {t('It seems there are no pet sitters in your area')}
                  </Text>
                </View>
              )}
              // onEndReached={fetchMorePetSitters}
              // onEndReachedThreshold={0.1}
              ListFooterComponent={
                !loading && loadingMore ? (
                  <ActivityIndicator size="small" color={COLORS.primary} />
                ) : null
              }
            />
          </View>
        </View>
        <Filter
          isModalVisible={isModalVisible}
          handleApply={handleApplyFilter}
          onBackdropPress={() => setModalVisible(false)}
          onCrossPress={() => setModalVisible(false)}
        />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

export default PetOwnerHome;
