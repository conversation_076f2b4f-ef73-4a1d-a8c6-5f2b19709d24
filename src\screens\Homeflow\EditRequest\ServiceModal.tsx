import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {useTranslation} from 'react-i18next';
import {IService} from '../../../interfaces/IPet';

interface Props {
  isModalVisible: boolean;
  setModalVisible: (isModalVisible: boolean) => void;
  data: IService[];
  setServiceName: (serviceName: string) => void;
}

const ServiceModal: React.FC<Props> = ({
  setModalVisible,
  isModalVisible,
  data,
  setServiceName,
}) => {
  const {t} = useTranslation();
  return (
    <Modal
      isVisible={isModalVisible}
      onBackdropPress={() => setModalVisible(false)}>
      <View style={styles.container}>
        {data?.map((item, index) => {
          return (
            <TouchableOpacity
              key={item.id}
              style={styles.innerContainer}
              onPress={() => {
                setServiceName(item.name);
                setModalVisible(false);
              }}>
              <View style={styles.serviceContainer}>
                <Text style={styles.text}>{t(item.name)}</Text>
              </View>
              {index != 5 && <View style={styles.divider} />}
            </TouchableOpacity>
          );
        })}
      </View>
    </Modal>
  );
};

export default ServiceModal;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingVertical: 12,
  },
  innerContainer: {
    paddingHorizontal: 12,
  },
  divider: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginVertical: 12,
  },
  text: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
  },
  serviceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});
