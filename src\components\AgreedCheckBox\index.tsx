import {Text, TouchableOpacity, View, ViewStyle} from 'react-native';
import React from 'react';
import {Check2} from '../../assets/svgIcons';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';

interface Props {
  label: string;
  containerStyle?: ViewStyle;
  selected: boolean | undefined;
  setSelected: (selected: boolean) => void;
  handleOnPress?: () => void;
}
const AgreedCheckBox: React.FC<Props> = ({
  label,
  containerStyle,
  selected,
  setSelected,
  handleOnPress,
}) => {
  const toggleButton = () => setSelected(!selected);
  const {t} = useTranslation();

  return (
    <View
      style={[{flexDirection: 'row', alignItems: 'center'}, containerStyle]}>
      <TouchableOpacity style={styles.container} onPress={toggleButton}>
        <View>{selected ? <Check2 /> : <View style={styles.emptyView} />}</View>
      </TouchableOpacity>
      <Text style={styles.text}>
        {t('I agree to')}{' '}
        <Text style={styles.text2} onPress={handleOnPress}>
          {label}
        </Text>{' '}
        {t('of My Pet Sit app.')}
      </Text>
    </View>
  );
};

export default AgreedCheckBox;
