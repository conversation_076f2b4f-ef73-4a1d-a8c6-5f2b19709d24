import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import {FONTS} from '../../../themes/fonts';
import {COLORS, SIZES} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';

interface Props {
  icon: ImageSourcePropType;
  label: string;
  onPress: () => void;
}

const SettingAction: React.FC<Props> = ({icon, label, onPress}) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Image resizeMode="contain" source={icon} style={styles.icon} />
      <Text style={styles.label}>{label}</Text>
    </TouchableOpacity>
  );
};

export default SettingAction;

const styles = StyleSheet.create({
  container: {
    marginTop: SIZES.DeviceHeight > 760 ? verticalScale(24) : verticalScale(18),
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
    marginLeft: 8,
  },
  icon: {
    width: 20,
    height: 20,
  },
});
