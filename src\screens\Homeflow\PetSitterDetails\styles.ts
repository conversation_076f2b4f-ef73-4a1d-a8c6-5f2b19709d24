import {Platform, StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  imageStyle: {
    width: '100%',
    height: verticalScale(300),
  },
  icon: {
    alignSelf: 'flex-start',
    paddingLeft: 26,
    paddingTop:
      Platform.OS === 'android' ? verticalScale(32) : verticalScale(52),
  },
  innerContainer: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
    paddingHorizontal: 24,
  },
  label2: {
    fontSize: 10,
    fontFamily: FONTS.Medium,
    color: '#989898',
    marginLeft: 4,
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingTop: 10,
  },
  line: {
    color: COLORS.lightGray,
    paddingHorizontal: 6,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.primary,
    marginHorizontal: 25,
    width: '90%',
  },
  interestedButton: {
    marginVertical: 20,
    marginHorizontal: 25,
    width: '90%',
  },
  serviceModalContainer: {
    backgroundColor: COLORS.white,
    alignItems: 'center',
    padding: 14,
    borderRadius: 12,
  },
  serviceInnerContainer: {
    height: 100,
    backgroundColor: COLORS.white,
    borderColor: COLORS.lightGray,
    borderWidth: 1,
    marginTop: 8,
    padding: 12,
    width: '100%',
  },
  serviceLabel: {
    fontSize: 14,
    color: COLORS.Bastille,
    textAlign: 'left',
    fontFamily: FONTS.Medium,
  },
  serviceModalButton: {
    width: '48%',
  },
  serviceModalButtonContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 18,
  },
  serviceStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  serviceSeparator: {
    alignSelf: 'center',
    width: '95%',
    height: 1,
    backgroundColor: '#EEEEEE',
    marginTop: 12,
  },
  divider: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginVertical: 12,
    borderWidth: 1,
  },
  text: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
  },
  modalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: 26,
  },
  petName: {
    height: 40,
    width: 40,
    borderRadius: 100,
    marginRight: 14,
  },
  petTitleText: {
    textAlign: 'center',
    fontFamily: FONTS.Bold,
    fontSize: 18,
    color: COLORS.Bastille,
  },
  petModalContainer: {
    backgroundColor: COLORS.white,
    paddingVertical: 12,
    borderRadius: 12,
  },
});
