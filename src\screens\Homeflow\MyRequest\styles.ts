import {Platform, StyleSheet} from 'react-native';
import {COLORS, SIZES} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? verticalScale(20) : verticalScale(20),
    justifyContent: 'space-between',
  },
  innerContainer: {
    flex: 1,
    justifyContent: 'space-between',
    marginTop: 8,
  },
  buttonContainer: {
    height: verticalScale(50),
    backgroundColor: '#FFF1E2',
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 5,
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: SIZES.DeviceHeight > 760 ? 120 : 100,
    marginHorizontal: 4,
  },
  labelStyle: {
    fontSize: 16,
    fontFamily: FONTS.SemiBold,
    color: COLORS.primary,
  },
  emptyText: {
    fontFamily: FONTS.SemiBold,
    marginTop: 30,
    alignSelf: 'center',
    color: COLORS.Philippine_Gray,
  },
  mainContainer: {flex: 1, backgroundColor: COLORS.white},
});
