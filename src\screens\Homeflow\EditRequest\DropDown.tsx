import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {ArrowDown} from '../../../assets/svgIcons';
import {FONTS} from '../../../themes/fonts';
import {COLORS} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';

interface Props {
  label: string;
  placeholder: string;
  color?: string;
  onPress: () => void;
}

const DropDown: React.FC<Props> = ({label, placeholder, color, onPress}) => {
  return (
    <View>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity
        activeOpacity={0.6}
        style={styles.dropDownContainer}
        onPress={onPress}>
        <Text style={[styles.placeholder, {color: color}]}>{placeholder}</Text>
        <ArrowDown />
      </TouchableOpacity>
    </View>
  );
};

export default DropDown;

const styles = StyleSheet.create({
  dropDownContainer: {
    height: verticalScale(48),
    borderWidth: 1,
    borderColor: '#E2E2E2',
    borderRadius: 5,
    paddingHorizontal: 10,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(6),
  },
  placeholder: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.placeHolder,
  },
  label: {
    fontSize: 14,
    color: COLORS.Bastille,
    fontFamily: FONTS.Medium,
    marginTop: 14,
  },
});
