import {
  Keyboard,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
  ScrollView,
  Platform,
} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {
  AppText,
  ButtonwithIcon,
  Divider,
  FormInput,
  LoginSignUpButtons,
  TextButton,
  TextButtonwithIcon,
} from '../../../components';
import {BackArrow, Facebook, Google} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {useTogglePasswordVisibility} from '../../../hooks';
import {AuthContext} from '../../../../App';
import {verticalScale} from 'react-native-size-matters';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {ILoginForm, IUser} from '../../../interfaces';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {styles} from './styles';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import Toast from 'react-native-toast-message';
import {useDispatch} from 'react-redux';
import {setUser} from '../../../slices/userSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import {
//   GoogleSignin,
//   statusCodes,
// } from '@react-native-google-signin/google-signin';
// import {LoginManager, AccessToken} from 'react-native-fbsdk-next';
import {useTranslation} from 'react-i18next';
import useAuth from '../../../hooks/useAuth';

type Props = NativeStackScreenProps<AuthStackParamList, 'Login'>;

const Login: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);

  const {t} = useTranslation();
  const {onVerifiedLogin} = useAuth();

  const validationSchema = Yup.object().shape({
    email: Yup.string()
      .required(t('Enter an email'))
      .email(t('Email must be a valid email'))
      .label(''),
    password: Yup.string()
      .required(t('Password is required'))
      .min(8, t('The password should contain at least 8 characters'))
      .label(''),
  });

  // useEffect(() => {
  //   GoogleSignin.configure({
  //     webClientId:
  //       '351590109713-m8m8edqdtek1b9ctkd5c594lk1qp4ikn.apps.googleusercontent.com',
  //   });
  // }, []);

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  // async function onFacebookButtonPress() {
  //   try {
  //     const result = await LoginManager.logInWithPermissions([
  //       'public_profile',
  //       'email',
  //     ]);

  //     if (result.isCancelled) {
  //       throw 'User cancelled the login process';
  //     }

  //     const data = await AccessToken.getCurrentAccessToken();

  //     if (!data) {
  //       throw 'Something went wrong obtaining access token';
  //     }

  //     const facebookCredential = auth.FacebookAuthProvider.credential(
  //       data.accessToken,
  //     );

  //     return auth()
  //       .signInWithCredential(facebookCredential)
  //       .then(async response => {
  //         const userDocRef = firestore()
  //           .collection('Users')
  //           .doc(response.user.uid);
  //         const userDocSnapshot = await userDocRef.get();
  //         if (userDocSnapshot.exists) {
  //           const fcm_Token = await AsyncStorage.getItem('fcmToken');

  //           const user = userDocSnapshot.data();
  //           const payload: IUser = {
  //             firstName: user?.firstName,
  //             surName: user?.surName,
  //             email: user?.email,
  //             phoneNumber: user?.phoneNumber,
  //             address: user?.address,
  //             city: user?.city,
  //             country: user?.country,
  //             dateOfBirth: user?.dateOfBirth,
  //             about: user?.about || '',
  //             idFrontSide: user?.idFrontSide,
  //             idBackSide: user?.idBackSide,
  //             userType: isOwner ? 'petOwner' : 'petSitter',
  //             countryCode: user?.countryCode,
  //             uid: userDocSnapshot.id,
  //             profilePicture: user?.profilePicture,
  //             coordinates: user?.coordinates,
  //             fcmToken: fcm_Token || '',
  //             profileStatus: user?.profileStatus,
  //             // customerId: user?.customerId,
  //           };

  //           dispatch(setUser(payload));
  //           const fcmToken = await AsyncStorage.getItem('fcmToken');
  //           await firestore()
  //             .collection('Users')
  //             .doc(userDocSnapshot.id)
  //             .update({
  //               fcmToken: fcmToken,
  //             });
  //           const userData = user?.userType;

  //           const usersPet = await firestore()
  //             .collection('Pets')
  //             .where('userId', '==', response.user.uid)
  //             .get();
  //           if (user?.isBlocked) {
  //             Toast.show({
  //               type: 'error',
  //               text1: t('Blocked Access'),
  //               text2: t('<NAME_EMAIL>'),
  //             });
  //           } else if (!user?.firstName) {
  //             navigation.navigate('ProfileVerification', {
  //               email: response.user.email || '',
  //               uid: response?.user?.uid,
  //               firstName: '',
  //               surName: '',
  //             });
  //           } else if (!user.isVerified) {
  //             navigation.navigate('CodeVerification', {
  //               uid: response.user.uid,
  //               phoneNumber: user.phoneNumber,
  //             });
  //           } else if (userData == 'petOwner' && isOwner) {
  //             if (!usersPet) {
  //               navigation.navigate('PetProfile', {
  //                 isAuthFlow: true,
  //                 uid: response.user.uid,
  //               });
  //             }
  //             // else if (!user.isPremiumCustomer) {
  //             //   navigation.navigate('Subscription', {uid: response.user.uid});
  //             // }
  //             else {
  //               if (userData == 'petOwner' && isOwner) {
  //                 setUserId('petOwner');
  //               } else if (userData == 'petSitter' && !isOwner) {
  //                 setUserId('petSitter');
  //               } else {
  //                 Toast.show({
  //                   type: 'error',
  //                   text1: 'Error',
  //                   text2: 'Login with correct credentials',
  //                 });
  //               }
  //             }
  //           } else {
  //             if (userData == 'petOwner' && isOwner) {
  //               setUserId('petOwner');
  //             } else if (userData == 'petSitter' && !isOwner) {
  //               setUserId('petSitter');
  //             } else {
  //               Toast.show({
  //                 type: 'error',
  //                 text1: 'Error',
  //                 text2: 'Login with correct credentials',
  //               });
  //             }
  //           }
  //         } else {
  //           navigation.navigate('ProfileVerification', {
  //             email: response.user.email || '',
  //             uid: response.user.uid,
  //             firstName: response.user.displayName || '',
  //             surName: '',
  //           });
  //         }
  //       });
  //   } catch (error) {
  //     console.log('error logging in', error);
  //     Toast.show({
  //       text1: 'Error',
  //       text2: 'Sorry could not login right now try again later',
  //       type: 'error',
  //     });
  //   }
  // }

  // const handleGoogleLogin = async () => {
  //   try {
  //     await GoogleSignin.hasPlayServices({
  //       showPlayServicesUpdateDialog: true,
  //     });
  //     const {idToken} = await GoogleSignin.signIn();

  //     const googleCredential = auth.GoogleAuthProvider.credential(idToken);

  //     return auth()
  //       .signInWithCredential(googleCredential)
  //       .then(async response => {
  //         await AsyncStorage.setItem('uid', response.user.uid);
  //         const userDocRef = firestore()
  //           .collection('Users')
  //           .doc(response.user.uid);
  //         const userDocSnapshot = await userDocRef.get();
  //         if (userDocSnapshot.exists) {
  //           const fcm_Token = await AsyncStorage.getItem('fcmToken');

  //           const user = userDocSnapshot.data();
  //           const payload: IUser = {
  //             firstName: response.user.displayName || '',
  //             surName: '',
  //             email: response.user?.email || '',
  //             phoneNumber: user?.phoneNumber,
  //             address: user?.address,
  //             city: user?.city,
  //             country: user?.country,
  //             dateOfBirth: user?.dateOfBirth,
  //             about: user?.about || '',
  //             idFrontSide: user?.idFrontSide,
  //             idBackSide: user?.idBackSide,
  //             userType: isOwner ? 'petOwner' : 'petSitter',
  //             countryCode: user?.countryCode,
  //             uid: response.user.uid,
  //             profilePicture: user?.profilePicture,
  //             coordinates: user?.coordinates,
  //             fcmToken: fcm_Token || '',
  //             profileStatus: user?.profileStatus,

  //             // customerId: user?.customerId,
  //           };

  //           dispatch(setUser(payload));
  //           const fcmToken = await AsyncStorage.getItem('fcmToken');
  //           await firestore()
  //             .collection('Users')
  //             .doc(response.user.uid)
  //             .update({
  //               fcmToken: fcmToken,
  //             });
  //           const userData = user?.userType;

  //           const usersPet = await firestore()
  //             .collection('Pets')
  //             .where('userId', '==', response.user.uid)
  //             .get();
  //           if (user?.isBlocked) {
  //             Toast.show({
  //               type: 'error',
  //               text1: t('Blocked Access'),
  //               text2: t('<NAME_EMAIL>'),
  //             });
  //           } else if (!user?.firstName) {
  //             navigation.navigate('ProfileVerification', {
  //               email: response.user.email || '',
  //               uid: response?.user?.uid,
  //               firstName: response.user.displayName || '',
  //               surName: '',
  //             });
  //           } else if (!user.isVerified) {
  //             // await functions()
  //             //   .httpsCallable('sendOtpCode')({
  //             //     phoneNumber: user.phoneNumber,
  //             //   })
  //             //   .then(() => {
  //             navigation.navigate('CodeVerification', {
  //               uid: response.user.uid,
  //               phoneNumber: user.phoneNumber,
  //             });
  //             // });
  //           } else if (userData == 'petOwner' && isOwner) {
  //             if (!usersPet) {
  //               navigation.navigate('PetProfile', {
  //                 isAuthFlow: true,
  //                 uid: response.user.uid,
  //               });
  //             }
  //             // else if (!user.isPremiumCustomer) {
  //             //   navigation.navigate('Subscription', {uid: response.user.uid});
  //             // }
  //             else {
  //               if (userData == 'petOwner' && isOwner) {
  //                 setUserId('petOwner');
  //               } else if (userData == 'petSitter' && !isOwner) {
  //                 setUserId('petSitter');
  //               } else {
  //                 Toast.show({
  //                   type: 'error',
  //                   text1: t('Error'),
  //                   text2: t('Login with correct credentials'),
  //                 });
  //               }
  //             }
  //           } else {
  //             if (userData == 'petOwner' && isOwner) {
  //               setUserId('petOwner');
  //             } else if (userData == 'petSitter' && !isOwner) {
  //               setUserId('petSitter');
  //             } else {
  //               Toast.show({
  //                 type: 'error',
  //                 text1: t('Error'),
  //                 text2: t('Login with correct credentials'),
  //               });
  //             }
  //           }
  //         } else {
  //           navigation.navigate('ProfileVerification', {
  //             email: response.user.email || '',
  //             uid: response.user.uid,
  //             firstName: response.user.displayName || '',
  //             surName: '',
  //           });
  //         }
  //       });
  //   } catch (error: any) {
  //     console.log('error', error);
  //     if (error.code === statusCodes.SIGN_IN_CANCELLED) {
  //       Toast.show({
  //         type: 'error',
  //         text1: t('Error'),
  //         text2: t('Sign in cancelled by user'),
  //       });
  //     } else if (error.code === statusCodes.IN_PROGRESS) {
  //       Toast.show({
  //         type: 'error',
  //         text1: t('Error'),
  //         text2: t('Sign in in progress'),
  //       });
  //     } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
  //       Toast.show({
  //         type: 'error',
  //         text1: t('Error'),
  //         text2: t('Google Play services not available'),
  //       });
  //     } else {
  //       Toast.show({
  //         type: 'error',
  //         text1: t('Error'),
  //         text2: t('Sorry, could not login at the moment'),
  //       });
  //     }
  //   }
  // };

  const onSubmitHandler = async () => {
    setIsLoading(true);
    try {
      await auth()
        .signInWithEmailAndPassword(formik.values.email, formik.values.password)
        .then(async response => {
          if (!response.user.emailVerified) {
            navigation.navigate('VerifyEmail');
            return;
          }

          await onVerifiedLogin(response.user);
        });
    } catch (error) {
      console.log('Error signing in:', error);
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Login with correct credentials'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<ILoginForm>({
    initialValues: {email: '', password: ''},
    onSubmit: onSubmitHandler,
    validateOnMount: true,
    validationSchema: validationSchema,
  });

  const {passwordVisibility, rightIcon, handlePasswordVisibility} =
    useTogglePasswordVisibility();
  return (
    <View style={{backgroundColor: COLORS.white, flex: 1}}>
      <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollViewContent}>
          <View style={styles.container}>
            <LoginSignUpButtons
              onSignUpPress={() => navigation.navigate('SignUp')}
              isLogin={true}
            />
            <Text style={[appStyles.title, styles.title]}>{t('Sign in')}</Text>
            <Text style={[appStyles.paragraph, {marginTop: 8}]}>
              {t('Welcome back!')}
            </Text>
            <FormInput
              label={t('Email Address')}
              placeholder={t('<EMAIL>')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.emailInputFeild}
              onChangeText={formik.handleChange('email')}
              onBlur={() => formik.setFieldTouched('email')}
              keyboardType="email-address"
            />
            <AppText
              error={formik.errors.email}
              visible={!!formik.touched.email}
            />
            <FormInput
              label={t('Password')}
              placeholder={t('Enter password')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.passwordInputFeild}
              secureTextEntry={passwordVisibility}
              rightIcon={rightIcon}
              onRightIconPress={handlePasswordVisibility}
              isPassword
              onChangeText={formik.handleChange('password')}
              onBlur={() => formik.setFieldTouched('password')}
            />
            <AppText
              error={formik.errors.password}
              visible={!!formik.touched.password}
            />
            <TouchableOpacity
              hitSlop={{left: 20, right: 20, bottom: 20, top: 4}}
              style={styles.forgotPassword}
              onPress={() => navigation.navigate('EmailVerification')}>
              <Text style={styles.forgotPasswordLabel}>
                {t('Forgot Password')}
              </Text>
            </TouchableOpacity>
            <View style={{marginTop: verticalScale(24)}}>
              <TextButton
                label={t('Sign in')}
                isShadow
                onPress={formik.handleSubmit}
                isLoading={isLoading}
                disabled={isLoading || !formik.isValid}
              />
              {/*{Platform.OS === 'android' ? (
                <>
                  <Divider />
                  <View>
                    <TextButtonwithIcon
                      label={t('Continue with Google')}
                      labelStyle={{marginLeft: 12}}
                      leftIcon={<Google />}
                      containerStyle={appStyles.socialButtonContainer}
                      onPress={handleGoogleLogin}
                    />
                     <TextButtonwithIcon
                  label={t('Continue with Facebook')}
                  labelStyle={{marginLeft: 12}}
                  leftIcon={<Facebook />}
                  containerStyle={
                    [
                      appStyles.socialButtonContainer,
                      {marginTop: verticalScale(14)},
                    ] as ViewStyle
                  }
                  onPress={onFacebookButtonPress}
                /> 
                  </View>
                </>
              ) : null}*/}
            </View>
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </View>
  );
};

export default Login;
