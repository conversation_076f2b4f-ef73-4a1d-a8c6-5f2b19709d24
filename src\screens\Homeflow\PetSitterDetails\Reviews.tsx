import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import appStyles from '../../../themes/appStyles';
import {COLORS} from '../../../themes/themes';
import {ReviewCard} from '../../../components';
import {FONTS} from '../../../themes/fonts';
import firestore from '@react-native-firebase/firestore';
import moment from 'moment';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import {IReview} from '../MyReviews';

interface ReviewsProps {
  handleOnpress: (rating: number) => void;
  isLoading: boolean;
  userId: string;
}
const Reviews: React.FC<ReviewsProps> = ({
  handleOnpress,
  isLoading,
  userId,
}) => {
  const [reviews, setReviews] = useState<IReview[]>([]);
  const [isReviewsLoading, setIsReviewsLoading] = useState(true);
  const {t} = useTranslation();

  const getReviews = async () => {
    try {
      const querySnapshot = await firestore()
        .collection('Reviews')
        .where('sentTo', '==', userId)
        .get();

      const reviewData = querySnapshot.docs.map(
        doc =>
          ({
            ...doc.data(),
            key: doc.id,
          } as IReview),
      );

      setReviews(reviewData);
      const totalRatings = reviewData.reduce(
        (acc, review) => acc + review.rating,
        0,
      );
      const averageRating =
        reviewData.length > 0 ? totalRatings / reviewData.length : 0;

      await firestore()
        .collection('Users')
        .doc(userId)
        .update({
          rating: averageRating.toFixed(1),
        });
    } catch (error) {
      console.log('Error fetching reviews:', error);
    } finally {
      setIsReviewsLoading(false);
    }
  };

  useEffect(() => {
    getReviews();
  }, [reviews]);
  return (
    <View style={styles.container}>
      <View>
        <Text style={appStyles.title2}>{t('Reviews')}</Text>

        <View style={{paddingTop: 16}}>
          {isReviewsLoading ? (
            <ActivityIndicator
              size="large"
              color={COLORS.primary}
              style={{marginTop: 40}}
            />
          ) : (
            <FlatList
              data={reviews}
              renderItem={({item}) => {
                return (
                  <ReviewCard
                    name={item?.user?.name}
                    email={item?.user?.email}
                    rating={item?.rating}
                    date={moment(item?.reviewDate.toDate()).format(
                      'DD/MM/YYYY',
                    )}
                    picture={item.user?.profilePicture || ''}
                  />
                );
              }}
              ListEmptyComponent={
                <Text style={styles.emptyComp}>{t('No reviews yet')}</Text>
              }
            />
          )}
        </View>
      </View>
    </View>
  );
};

export default Reviews;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    marginTop: 24,
    justifyContent: 'space-between',
  },
  emptyComp: {
    fontFamily: FONTS.SemiBold,
    color: COLORS.Philippine_Gray,
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 30,
  },
});
