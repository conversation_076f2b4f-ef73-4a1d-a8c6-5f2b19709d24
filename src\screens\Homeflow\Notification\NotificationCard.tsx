import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';

interface Props {
  title: string;
  paragraph: string;
  imageSource: ImageSourcePropType;
  onPress: () => void;
}

const NotificationCard: React.FC<Props> = ({
  title,
  paragraph,
  imageSource,
  onPress,
}) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Image resizeMode="contain" source={imageSource} style={styles.icon} />
      <View style={styles.card}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.paragraph} numberOfLines={1}>
          {paragraph}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default NotificationCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
    borderRadius: 5,
    margin: 2,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  card: {
    marginLeft: 12,
  },
  title: {
    fontSize: 14,
    color: COLORS.Bastille,
    fontFamily: FONTS.SemiBold,
  },
  paragraph: {
    fontSize: 12,
    color: '#9F9F9F',
    fontFamily: FONTS.Medium,
    paddingTop: 4,
    marginRight: 30,
  },
  icon: {
    width: 42,
    height: 42,
  },
});
