import {StyleSheet} from 'react-native';
import {COLORS, SIZES} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {scale, verticalScale} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  innerContainer: {
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  title: {
    fontSize: 24,
    color: COLORS.Bastille,
    fontFamily: FONTS.Bold,
  },
  divier: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginTop: verticalScale(24),
  },
  signOutButton: {
    flexDirection: 'row',
    marginTop: SIZES.DeviceHeight > 760 ? verticalScale(30) : verticalScale(40),
    backgroundColor: '#FFF3F3',
    width: scale(120),
    height: verticalScale(45),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
  },
  signoutText: {
    color: '#EB1212',
    marginLeft: 8,
    fontFamily: FONTS.Medium,
    fontSize: 14,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceText: {color: COLORS.primary, fontFamily: FONTS.SemiBold},
});
