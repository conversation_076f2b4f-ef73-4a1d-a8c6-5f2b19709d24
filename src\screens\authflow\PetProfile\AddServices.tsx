import {StyleSheet, Text, TouchableOpacity} from 'react-native';
import React from 'react';
import {FONTS} from '../../../themes/fonts';
import {useTranslation} from 'react-i18next';

interface Props {
  onPress: () => void;
}

const AddServices: React.FC<Props> = ({onPress}) => {
  const {t} = useTranslation();
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Text style={styles.text}>{t('Add Services')}</Text>
    </TouchableOpacity>
  );
};

export default AddServices;

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  text: {
    fontSize: 14,
    color: '#FF8C0E',
    textDecorationLine: 'underline',
    fontFamily: FONTS.Medium,
  },
});
