export interface PaymentPayload {
  amount: number;
  currency: 'eur';
  customer: string;
  paymentMethod: string;
}

export interface NextAction {
  use_stripe_sdk: {
    type: 'three_d_secure_redirect';
    source: 'src_1OA7lCC9wIlazAbkU2BKJaBy';
    stripe_js: 'https://hooks.stripe.com/redirect/authenticate/src_1OA7lCC9wIlazAbkU2BKJaBy?client_secret=src_client_secret_2RXYgguqyq0sJFzE0v75J6oM&source_redirect_slug=test_YWNjdF8xRlVJWGdDOXdJbGF6QWJrLF9PeTNzZDhUUmFMZE9nb3lWRkt0eldFMlYxNWxxdWJh0100Uy7Gne7k';
  };
  type: 'use_stripe_sdk';
}

export interface PaymentResult {
  data: {
    status: string; // e.g requires_action
    application_fee_amount: number | null;
    processing: null;
    next_action: NextAction;
    last_payment_error: null;
    receipt_email: null;
    application: null;
    cancellation_reason: null;
    customer: string;
    setup_future_usage: null;
    payment_method_types: string[]; // e.g ['card', 'link']
    invoice: null;
    amount: number; // In cents if currency is usd
    capture_method: string; // e.g automatic
    transfer_data: null;
    created: number;
    object: 'payment_intent';
    payment_method: string;
    payment_method_configuration_details: {
      id: string;
      parent: null;
    };
    canceled_at: null;
    payment_method_options: {
      card: {
        network: null;
        mandate_options: null;
        request_three_d_secure: 'automatic';
        installments: null;
      };
      link: {
        persistent_token: null;
      };
    };
    on_behalf_of: null;
    currency: string; // usd
    statement_descriptor_suffix: null;
    automatic_payment_methods: {
      enabled: boolean;
      allow_redirects: string; // 'never'
    };
    transfer_group: null;
    livemode: boolean;
    confirmation_method: 'automatic';
    statement_descriptor: null;
    latest_charge: null;
    review: null;
    amount_received: 0;
    source: null;
    client_secret: string;
    metadata: {};
    shipping: null;
    id: string;
    amount_capturable: 0;
    description: null;
    amount_details: {
      tip: {};
    };
  };
}
