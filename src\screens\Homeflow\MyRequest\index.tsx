import React, {useEffect, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  SafeAreaView,
  Text,
  View,
} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {
  AlertModal,
  ButtonwithIcon,
  MyPetCard,
  TextButtonwithIcon,
} from '../../../components';
import {BackArrow, Plus} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import Buttons from './Buttons';
import {verticalScale} from 'react-native-size-matters';
import {OwnerBottomStackParamList} from '../../../navigation/PetOwnerBottomTabs';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import firestore from '@react-native-firebase/firestore';
import moment from 'moment';
import {IBooking} from '../../../interfaces/IBooking';
import {useAppSelector} from '../../../store/Store';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';
import {BannerAdComponent} from '../../../components/Admob';

type Props = NativeStackScreenProps<OwnerBottomStackParamList, 'MyRequests'>;

const MyRequest: React.FC<Props> = ({navigation}) => {
  const [isModalVisible, setModalVisible] = useState<boolean>(false);
  const [selectedButton, setSelectedButton] = useState<string>('New');
  const [bookings, setBookings] = useState<Array<IBooking>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [bookingId, setBookingId] = useState('');
  const user = useAppSelector(state => state.user);
  const {t} = useTranslation();

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);

  useEffect(() => {
    const fetchBookings = async () => {
      setIsLoading(true);
      try {
        const unsubscribe = firestore()
          .collection('Bookings')
          .where('userId', '==', user.uid)
          .orderBy('updatedAt', 'desc')
          .onSnapshot(
            async querySnapshot => {
              const bookingsPromises = querySnapshot.docs.map(async doc => {
                const userData = await firestore()
                  .collection('Users')
                  .doc(doc.data().userId)
                  .get();
                const petDoc = await firestore()
                  .collection('Pets')
                  .doc(doc.data().petId)
                  .get();
                return {
                  ...doc.data(),
                  key: doc.id,
                  petData: petDoc.data(),
                  userData: userData.data(),
                } as IBooking;
              });

              const bookingsData = await Promise.all(bookingsPromises);

              setBookings(bookingsData);
              setIsLoading(false);
            },
            error => {
              console.log('Error fetching bookings:', error);
              setIsLoading(false);
            },
          );

        return () => unsubscribe();
      } catch (error) {
        console.log('Error setting up bookings listener:', error);
        setIsLoading(false);
      }
    };

    fetchBookings();
  }, [user.uid]);

  const handleDeletePress = async () => {
    try {
      await firestore()
        .collection('Bookings')
        .doc(bookingId)
        .delete()
        .then(() => {
          setModalVisible(false);
          setBookingId('');
        });
      const requestsSnapshot = await firestore()
        .collection('Requests')
        .where('bookingId', '==', bookingId)
        .get();

      const batch = firestore().batch();
      requestsSnapshot.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
    } catch (error) {
      console.log('Error deleting', error);
    }
  };

  const filteredBookings = useMemo(() => {
    return selectedButton === 'New'
      ? bookings.filter(b => b.status == 'pending')
      : bookings.filter(b => b.status !== 'pending');
  }, [bookings, selectedButton]);

  return (
    <SafeAreaView style={styles.mainContainer}>
      <BannerAdComponent />
      <View style={styles.container}>
        <Text style={[appStyles.title, {fontSize: 18}]}>
          {t('My Bookings')}
        </Text>
        <Buttons
          onNewPress={() => setSelectedButton('New')}
          onHistoryPress={() => setSelectedButton('History')}
          selectedButton={selectedButton}
        />
        <View style={styles.innerContainer}>
          <View>
            <>
              {isLoading ? (
                <ActivityIndicator color={COLORS.primary} size={'large'} />
              ) : (
                <FlatList
                  data={filteredBookings}
                  contentContainerStyle={{paddingBottom: verticalScale(170)}}
                  showsVerticalScrollIndicator={false}
                  renderItem={({item}) => (
                    <MyPetCard
                      onDeletePress={() => {
                        setModalVisible(true);
                        setBookingId(item?.key);
                      }}
                      isTopRating={true}
                      petName={item.petData?.name}
                      imageURI={item?.userData?.profilePicture || ''}
                      petPicture={item?.petData?.picture}
                      bookingDate={moment(
                        item?.bookingDate?.startDate,
                        'YYYY-MM-DD',
                      ).format('DD/MM/YYYY')}
                      city={item?.userData?.city}
                      onProfilePress={() => navigation.navigate('EditProfile')}
                    />
                  )}
                  ListEmptyComponent={
                    <Text style={styles.emptyText}>
                      {t('No bookings history found')}
                    </Text>
                  }
                />
              )}
            </>
          </View>
          {user.profileStatus == 'approved' ? (
            <TextButtonwithIcon
              leftIcon={<Plus />}
              label={t('Create New Booking')}
              containerStyle={styles.buttonContainer}
              labelStyle={styles.labelStyle}
              onPress={() =>
                navigation.navigate('EditRequest', {isEdit: false})
              }
            />
          ) : null}
        </View>

        <AlertModal
          isModalVisible={isModalVisible}
          onBackdropPress={() => setModalVisible(false)}
          onCancelPress={() => setModalVisible(false)}
          handleConfirm={handleDeletePress}
          redLabel={t('Delete')}
          title={t('Delete booking')}
          paragraph={t('You are attempting to delete your booking.')}
        />
      </View>
    </SafeAreaView>
  );
};

export default MyRequest;
