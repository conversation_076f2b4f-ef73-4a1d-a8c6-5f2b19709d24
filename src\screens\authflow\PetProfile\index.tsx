import {Image, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import {
  ButtonwithIcon,
  FormInput,
  TextButton,
  Picker,
  AppText,
} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import {COLORS} from '../../../themes/themes';
import appStyles from '../../../themes/appStyles';
import {FONTS} from '../../../themes/fonts';
import {Camera, ImagePlaceholder2, SuccesState2} from '../../../assets/images';
import ImagePicker from 'react-native-image-crop-picker';
import AddServices from './AddServices';
import {AuthContext} from '../../../../App';
import ServiceCard from './ServiceCard';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import storage from '@react-native-firebase/storage';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import firestore from '@react-native-firebase/firestore';
import {styles} from './styles';
import {useDispatch} from 'react-redux';
import {addPet} from '../../../slices/petSlice';
import {useAppSelector} from '../../../store/Store';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import * as geofirestore from 'geofirestore';
import functions from '@react-native-firebase/functions';

type Props = NativeStackScreenProps<
  AuthStackParamList & PetOwnerParamList,
  'PetProfile'
>;

const PetProfile: React.FC<Props> = ({navigation, route}) => {
  const {uid, petData} = route.params;
  const {setUserId, userId} = useContext(AuthContext);
  const [showTick, setShowTick] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [services, setServices] = useState<string[]>(
    petData ? petData.services : [],
  );
  const user = useAppSelector(state => state.user);

  const {isAuthFlow} = route.params;

  const dispatch = useDispatch();

  const {t} = useTranslation();
  const validationSchema = Yup.object().shape({
    petProfile: Yup.array().of(
      Yup.object().shape({
        name: Yup.string().required(t('Pet name is required')),
        age: Yup.number()
          .required(t('Pet age is required'))
          .min(1, t('Pet age should be greater than 0')),
        picture: Yup.string().required(t('Pet picture is required')),
        ageUnit: Yup.string().required(t('Age unit is required')),
      }),
    ),
  });

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  const handleSelectImage = (index: number) => {
    ImagePicker.openPicker({
      width: 300,
      height: 400,
      cropping: false,
      compressImageQuality: 0.25,
    }).then(image => {
      formik.setFieldValue(`petProfile.${index}.picture`, image.path);
    });
  };

  const handleUpload = async (selected: string, name: string) => {
    const reference = storage().ref(`petPicture/${user.uid}` + name);
    const pathToFile = selected && selected;
    pathToFile && (await reference.putFile(pathToFile));
    const url = await storage()
      .ref(`petPicture/${user.uid}` + name)
      .getDownloadURL();
    return url;
  };

  const handleContinue = async () => {
    setIsLoading(true);
    try {
      const randomString = (Math.random() + 1).toString(36).substring(7);
      const firestoreApp = firestore();
      const GeoFirestore = geofirestore.initializeApp(firestoreApp);
      const geocollection = GeoFirestore.collection('Pets');

      if (petData) {
        let imageUrl = petData.picture;

        if (formik.values.petProfile[0].picture !== petData.picture) {
          imageUrl = await handleUpload(
            formik.values.petProfile[0].picture,
            `petPicture_${user.uid}_${randomString}_index=1`,
          );
        }
        const payload = {
          name: formik.values.petProfile[0].name,
          age: formik.values.petProfile[0].age,
          ageUnit: formik.values.petProfile[0].ageUnit,
          picture: imageUrl,
          services: services,
          userId: uid,
          firstPet: isAuthFlow ? true : false,
          createdAt: new Date(),
          updatedAt: new Date(),
          coordinates: new firestore.GeoPoint(
            user.coordinates.latitude,
            user.coordinates.longitude,
          ),
        };
        if (isAuthFlow) {
          functions().httpsCallable('sendPetNotification')({
            userId: uid,
            fcmToken: user.fcmToken,
            isAuthFlow: isAuthFlow,
          });
        }

        geocollection
          .doc(petData.id)
          .update(payload)
          .then(() => {
            dispatch(addPet(payload));
          });
      } else {
        const uploadPromises = formik.values.petProfile.map(
          async (petProfile, index) => {
            const pictureUrl = await handleUpload(
              petProfile.picture,
              `petPicture_${user.uid}_${randomString}_index=${index}`,
            );

            const payload = {
              name: petProfile.name,
              age: petProfile.age,
              ageUnit: petProfile.ageUnit,
              picture: pictureUrl,
              services: services,
              userId: uid,
              firstPet: isAuthFlow ? true : false,
              createdAt: new Date(),
              updatedAt: new Date(),
              coordinates: new firestore.GeoPoint(
                user.coordinates.latitude,
                user.coordinates.longitude,
              ),
            };

            const docRef = await geocollection.add(payload);
            return docRef.id;
          },
        );

        await Promise.all(uploadPromises);
      }

      if (isAuthFlow) {
        setShowTick(true);

        setTimeout(() => {
          setShowTick(false);

          if (!userId) {
            setUserId('petOwner');
          } else {
            navigation.goBack();
          }
        }, 1000);
      } else {
        navigation.goBack();
      }
    } catch (error) {
      console.log('Error in handle continue:', error);
      Toast.show({
        type: 'error',
        text2: t('Error creating pet profiles. try again later'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addservice = (service: string) => {
    setServices([...services, service]);
  };

  const handleRemoveService = (index: number) => {
    const filteredServices = services.splice(0, index);
    setServices(filteredServices);
  };

  const item = [
    {label: t('Month'), value: 'Month'},
    {label: t('Year'), value: 'Year'},
  ];

  const petProfileInitalState = petData
    ? {
        name: petData.name,
        age: petData.age,
        picture: petData.picture,
        ageUnit: petData.ageUnit || 'Month',
      }
    : {
        name: '',
        age: undefined,
        picture: '',
        ageUnit: 'Month',
      };
  const formik = useFormik({
    initialValues: {
      petProfile: [petProfileInitalState],
    },
    onSubmit: handleContinue,
    validateOnMount: true,
    validationSchema: validationSchema,
    enableReinitialize: true,
  });

  const titleText = petData ? 'Edit Pet Profile' : 'Add Pet Profile';
  const descText = petData ? 'Edit your pet information' : 'Add your pet.';
  const buttonName = petData ? 'Update Pet Profile' : 'Add your Pet';

  const handlePressAddPet = () => {
    const profiles = formik.values.petProfile;
    profiles.push(petProfileInitalState);
    formik.setFieldValue('petProfile', [...profiles]);
  };
  const handleRemovePet = (index: number) => {
    const profiles = formik.values.petProfile;
    profiles.splice(index, index);
    formik.setFieldValue('petProfile', profiles);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={{flexGrow: 1}}
        showsVerticalScrollIndicator={false}>
        <View style={{flex: 1, justifyContent: 'space-between'}}>
          <View>
            <Text style={appStyles.title}>{t(titleText)}</Text>

            <Text style={[appStyles.paragraph, {paddingTop: 10}]}>
              {t(descText)}
            </Text>

            {formik.values.petProfile.map((_, index) => (
              <>
                <View style={styles.crossContainer} key={index}>
                  <Text style={styles.paragraph} key={index}>
                    {t('Pet Profile Picture')}
                  </Text>
                  {index !== 0 && (
                    <TouchableOpacity
                      onPress={() => handleRemovePet(index)}
                      key={index}>
                      <Text style={styles.crossText}>X</Text>
                    </TouchableOpacity>
                  )}
                </View>
                <View style={styles.imageContainer}>
                  <Image
                    source={
                      formik.values.petProfile[index]?.picture
                        ? {uri: formik.values.petProfile[index].picture}
                        : ImagePlaceholder2
                    }
                    style={styles.imageStyles}
                  />
                  <TouchableOpacity
                    style={styles.cameraIcon}
                    onPress={() => {
                      handleSelectImage(index);
                      formik.setFieldTouched(`petProfile.${index}.picture`);
                    }}>
                    <Image source={Camera} style={styles.icon} />
                  </TouchableOpacity>
                </View>
                <AppText
                  error={formik.errors.petProfile?.[index]?.picture}
                  visible={formik.touched.petProfile?.[index]?.picture}
                />
                <View style={{paddingTop: 12}}>
                  <FormInput
                    placeholder={t('Pet name')}
                    placeholderTextColor={COLORS.placeHolder}
                    label={t('Pet Name')}
                    onChangeText={value =>
                      formik.setFieldValue(`petProfile.${index}.name`, value)
                    }
                    onBlur={() =>
                      formik.setFieldTouched(`petProfile.${index}.name`)
                    }
                    value={formik.values.petProfile[index]?.name || ''}
                  />
                  <AppText
                    error={formik.errors.petProfile?.[index]?.name}
                    visible={formik.touched.petProfile?.[index]?.name}
                  />

                  <View style={styles.innerContainer}>
                    <FormInput
                      placeholder={t('Pet Age')}
                      placeholderTextColor={COLORS.placeHolder}
                      label={t('Age')}
                      containerStyle={{width: '48%'}}
                      onChangeText={value =>
                        formik.setFieldValue(`petProfile.${index}.age`, value)
                      }
                      onBlur={() =>
                        formik.setFieldTouched(`petProfile.${index}.age`)
                      }
                      keyboardType="number-pad"
                      value={formik.values.petProfile[index]?.age?.toString()}
                    />
                    <View style={{width: '4%'}} />
                    <Picker
                      item={item}
                      value={formik.values.petProfile[index]?.ageUnit}
                      onValueChange={value => {
                        formik.setFieldValue(
                          `petProfile.${index}.ageUnit`,
                          value,
                        );
                      }}
                      placeholder={t('Select age unit')}
                      containerStyle={{width: '48%', marginTop: 0}}
                    />
                  </View>
                  <AppText
                    error={formik.errors.petProfile?.[index]?.age}
                    visible={formik.touched.petProfile?.[index]?.age}
                  />
                  <AppText
                    error={formik.errors.petProfile?.[index]?.ageUnit}
                    visible={formik.touched.petProfile?.[index]?.ageUnit}
                  />
                </View>
                <AddServices
                  onPress={() =>
                    isAuthFlow
                      ? navigation.navigate('AddAuthService', {
                          addservice: addservice,
                        })
                      : navigation.navigate('AddService', {
                          addservice: addservice,
                        })
                  }
                />
                {services.map((item, index) => (
                  <ServiceCard
                    key={index}
                    onPress={() => handleRemoveService(index)}
                    serviceName={item}
                  />
                ))}
                {!userId && <View style={styles.divider} />}
              </>
            ))}
            {!userId && (
              <TouchableOpacity
                style={{marginTop: 10}}
                onPress={handlePressAddPet}>
                <Text style={styles.addPet}>{t('Add another pet')} +</Text>
              </TouchableOpacity>
            )}
          </View>
          <TextButton
            label={t(buttonName)}
            onPress={formik.handleSubmit}
            isLoading={isLoading}
            disabled={isLoading || !formik.isValid}
          />
        </View>

        {showTick && (
          <View style={styles.tickImageContainer}>
            <Image
              source={SuccesState2}
              style={styles.tickImage}
              resizeMode="contain"
            />
            <Text style={{fontSize: 20, fontFamily: FONTS.Bold, marginTop: 60}}>
              {t('Account Created')}
            </Text>
            <Text style={styles.paragraph}>
              {t('Your account is created successfully.')}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default PetProfile;
