import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {IUser} from '../interfaces';

const initialState = {
  petOwnerData: [] as Array<IUser>,
};

export const petOwnerSlice = createSlice({
  name: 'pets',
  initialState,
  reducers: {
    savePetOwners: (state, {payload}) => {
      state = {
        petOwnerData: payload,
      };
      return state;
    },
    updatePetOwner: (state, {payload}: PayloadAction<Partial<IUser>>) => {
      const index = state.petOwnerData.findIndex(v => v.uid === payload.uid);
      state.petOwnerData[index] = {...state.petOwnerData[index], ...payload};
      return state;
    },
  },
});

export const {savePetOwners, updatePetOwner} = petOwnerSlice.actions;

export default petOwnerSlice;
