import {StyleSheet} from 'react-native';
import {scale, verticalScale} from 'react-native-size-matters';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingHorizontal: scale(24),
  },
  logoContainer: {
    width: verticalScale(120),
    height: verticalScale(120),
    marginTop: verticalScale(50),
    alignSelf: 'center',
  },
  imageContainer: {
    width: '100%',
    height: verticalScale(300),
    marginTop: 30,
    alignSelf: 'center',
  },
  title: {
    fontSize: scale(18),
    alignSelf: 'flex-start',
    marginBottom: verticalScale(10),
    fontFamily: FONTS.SemiBold,
  },
  buttonContainer: {
    marginTop: verticalScale(16),
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  label: {
    color: COLORS.primary,
  },
});
