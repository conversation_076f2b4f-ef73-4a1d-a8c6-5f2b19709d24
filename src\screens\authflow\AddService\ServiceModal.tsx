import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {COLORS} from '../../../themes/themes';
import {useTranslation} from 'react-i18next';

interface PetData {
  id: number;
  name: string;
}

interface Props {
  isModalVisible: boolean;
  setModalVisible: (isModalVisible: boolean) => void;
  data: PetData[];
  setServiceName: (serviceName: string) => void;
}

const ServiceModal: React.FC<Props> = ({
  setModalVisible,
  isModalVisible,
  data,
  setServiceName,
}) => {
  const {t} = useTranslation();
  return (
    <Modal
      style={{zIndex: 20}}
      isVisible={isModalVisible}
      onBackdropPress={() => setModalVisible(false)}>
      <View style={styles.container}>
        {data?.map((item, index) => {
          return (
            <TouchableOpacity
              key={item.id}
              style={styles.innerContainer}
              onPress={() => {
                setServiceName(item.name);
                setModalVisible(false);
              }}>
              <View style={styles.rowContainer}>
                <Text>{t(item.name)}</Text>
              </View>
              {index != 5 && <View style={styles.divider} />}
            </TouchableOpacity>
          );
        })}
      </View>
    </Modal>
  );
};

export default ServiceModal;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingVertical: 12,
    zIndex: 18,
  },
  innerContainer: {
    paddingHorizontal: 12,
  },
  divider: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginVertical: 12,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});
