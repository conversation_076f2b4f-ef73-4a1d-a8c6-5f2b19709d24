import {
  Image,
  ImageBackground,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {
  Alert,
  ButtonwithIcon,
  TextButton,
  TextButtonwithIcon,
} from '../../../components';
import {BackArrow, Location, Report, Star} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import Buttons from './Buttons';
import About from './About';
import Reviews from './Reviews';
import {verticalScale} from 'react-native-size-matters';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import firestore from '@react-native-firebase/firestore';
import {styles} from './styles';
import {useAppSelector} from '../../../store/Store';
import functions from '@react-native-firebase/functions';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import {COLORS} from '../../../themes/themes';
import Modal from 'react-native-modal';
import {IPet} from '../../../interfaces';
import {
  ImagePlaceholder,
  ImagePlaceholder2,
  ImagePlaceholder3,
} from '../../../assets/images';

type Props = NativeStackScreenProps<PetOwnerParamList, 'PetSitterDetails'>;

const PetSitterDetails: React.FC<Props> = ({navigation, route}) => {
  const [selectedButton, setSelectedButton] = useState<string>('About');
  const [isButtonLoading, setButtonIsLoading] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [isReportModalVisible, setReportModalVisible] = useState(false);
  const [isReporting, setIsReporting] = useState(false);
  const [reportReason, setReportReason] = useState('');
  const [pets, setPets] = useState<IPet[]>();

  const user = useAppSelector(state => state.user);
  const {petSitter} = route.params;
  const {t} = useTranslation();

  const renderModal = () => {
    return (
      <Modal
        isVisible={isReportModalVisible}
        onBackdropPress={() => setReportModalVisible(false)}>
        <View style={styles.serviceModalContainer}>
          <Report />
          <Text style={[appStyles.title2, {fontSize: 18, paddingTop: 10}]}>
            {t('Report User')}
          </Text>
          <View style={{width: '100%'}}>
            <Text style={styles.serviceLabel}>{t('Reason')}</Text>
            <View style={styles.serviceInnerContainer}>
              <TextInput
                placeholder={t('Reason for reporting...')}
                placeholderTextColor={COLORS.placeHolder}
                multiline={true}
                value={reportReason}
                onChangeText={text => setReportReason(text)}
              />
            </View>
          </View>
          <View style={styles.serviceModalButtonContainer}>
            <TextButton
              label={t('Cancel')}
              labelStyle={{color: COLORS.Bastille}}
              containerStyle={
                [
                  styles.serviceModalButton,
                  {backgroundColor: COLORS.white},
                ] as ViewStyle
              }
              onPress={() => setReportModalVisible(false)}
            />
            <TextButton
              label={t('Report')}
              containerStyle={styles.serviceModalButton}
              onPress={() => {
                handleReport(reportReason);
              }}
              disabled={!reportReason || isReporting}
              isLoading={isReporting}
            />
          </View>
        </View>
      </Modal>
    );
  };

  const handleReport = async (reportReason: string) => {
    try {
      setIsReporting(true);
      const payload = {
        userId: user?.uid,
        name: user?.firstName,
        reason: reportReason,
        reportedUserId: petSitter.uid,
      };
      await functions()
        .httpsCallable('sendEmail')(payload)
        .then(() => {
          navigation.goBack();
          setReportModalVisible(false);
        });
    } catch (error) {
      console.log('Error reporting pet:', error);
      Toast.show({
        type: 'error',
        text1: t('Sorry could not report right now'),
        position: 'top',
        autoHide: true,
      });
    } finally {
      setIsReporting(false);
    }
  };

  const handleAddReview = async (rating: number) => {
    try {
      setButtonIsLoading(true);
      const existingReviewSnapshot = await firestore()
        .collection('Reviews')
        .where('sentTo', '==', petSitter.uid)
        .where('sentBy', '==', user.uid)
        .get();

      if (!existingReviewSnapshot.empty) {
        const existingReviewId = existingReviewSnapshot.docs[0].id;
        await firestore().collection('Reviews').doc(existingReviewId).update({
          rating: rating,
          reviewDate: new Date(),
        });
      } else {
        const payload = {
          user: user,
          sentTo: petSitter.uid,
          sentBy: user.uid,
          rating: rating,
          reviewDate: new Date(),
        };
        await firestore().collection('Reviews').doc().set(payload);
      }
      Toast.show({
        type: 'success',
        text1: t('Success'),
        text2: t('Rating added'),
      });
      setButtonIsLoading(false);
    } catch (error) {
      console.log('Error adding review:', error);
      setButtonIsLoading(false);
    }
  };
  const handleInterested = async () => {
    try {
      const payload = {
        senderId: user.uid,
        senderName: user.firstName,
        receiverId: petSitter!.uid,
        receiverToken: petSitter?.fcmToken,
        timeStamp: new Date(),
      };

      setModalVisible(true);
    } catch (error) {
      console.log('An error occurred while handling interest:', error);
    }
  };
  useEffect(() => {
    fetchPets();
  }, []);
  const fetchPets = () => {
    const petData = firestore()
      .collection('Pets')
      .where('userId', '==', user.uid);
    petData.onSnapshot(snapshot => {
      const _petData: IPet[] = [];
      snapshot.forEach(result => {
        _petData.push({
          ...result.data(),
          id: result.id,
        } as IPet);
      });
      setPets(_petData);
    });
  };
  const handlePetSelect = (item: IPet) => {
    try {
      navigation.navigate('SelectPetServices', {item, sitter: petSitter});
    } catch (error) {
      console.log('Error selecting pet');
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 40}}>
        <ImageBackground
          source={
            petSitter?.profilePicture
              ? {uri: petSitter?.profilePicture}
              : ImagePlaceholder3
          }
          style={{height: verticalScale(282)}}
          imageStyle={styles.imageStyle}>
          <ButtonwithIcon
            hitSlop={{left: 20, right: 20, top: 20, bottom: 20}}
            icon={<BackArrow />}
            containerStyle={styles.icon}
            onPress={() => navigation.goBack()}
          />
        </ImageBackground>
        <View style={styles.innerContainer}>
          <Text style={[appStyles.title, {textAlign: 'center'}]}>
            {petSitter?.firstName} {petSitter?.surName}
          </Text>
          <View style={styles.infoContainer}>
            <TextButtonwithIcon
              label={petSitter?.rating?.toString() || ''}
              labelStyle={styles.label2}
              leftIcon={<Star />}
              disabled
            />
            <Text style={styles.line}>|</Text>
            <TextButtonwithIcon
              label={petSitter?.city}
              labelStyle={styles.label2}
              leftIcon={<Location />}
              disabled
            />
            {petSitter?.experience && petSitter?.experience > 0 ? (
              <>
                <Text style={styles.line}>|</Text>
                <TextButtonwithIcon
                  label={`${petSitter?.experience} yrs`}
                  labelStyle={styles.label2}
                  leftIcon={<Location />}
                  disabled
                />
              </>
            ) : null}
          </View>
          <Buttons
            onAboutPress={() => setSelectedButton('About')}
            onReviewPress={() => setSelectedButton('Reviews')}
            isReviewsVisible={true}
            selectedButton={selectedButton}
          />
          {selectedButton === 'About' ? (
            <About paragraph={petSitter?.about} />
          ) : (
            <Reviews
              handleOnpress={rating => handleAddReview(rating)}
              isLoading={isButtonLoading}
              userId={petSitter.uid}
            />
          )}
        </View>

        <TextButton
          label={t('Interested')}
          onPress={handleInterested}
          containerStyle={styles.interestedButton}
          disabled={user.profileStatus === 'pending'}
        />
        <TextButton
          label={t('Report')}
          labelStyle={{color: COLORS.primary}}
          containerStyle={styles.button}
          onPress={() => setReportModalVisible(true)}
          disabled={user.profileStatus === 'pending'}
        />
      </ScrollView>

      <Modal
        isVisible={isModalVisible}
        onBackdropPress={() => setModalVisible(false)}>
        <View style={styles.petModalContainer}>
          <Text style={styles.petTitleText}>{t('Choose your pet')}</Text>
          {pets?.map((item, index) => {
            return (
              <View key={item.id}>
                <TouchableOpacity
                  key={item.id}
                  style={styles.modalContainer}
                  onPress={() => {
                    setModalVisible(false);
                    handlePetSelect(item);
                  }}>
                  <Image source={{uri: item.picture}} style={styles.petName} />
                  <Text style={styles.text}>{item.name}</Text>
                </TouchableOpacity>
                {index != pets.length - 1 && (
                  <View style={styles.serviceSeparator} />
                )}
              </View>
            );
          })}
        </View>
      </Modal>
      {renderModal()}
    </View>
  );
};

export default PetSitterDetails;
