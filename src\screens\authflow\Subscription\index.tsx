import {
  Image,
  Platform,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {GreenCheck} from '../../../assets/images';
import {TextButton} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
// import {usePaymentSheet} from '@stripe/stripe-react-native';
import functions from '@react-native-firebase/functions';
import {useAppDispatch, useAppSelector} from '../../../store/Store';
import firestore from '@react-native-firebase/firestore';
import {updateUser} from '../../../slices/userSlice';
import {styles} from './styles';
import {useTranslation} from 'react-i18next';
import Toast from 'react-native-toast-message';
// import useStripeApi from '../../../hooks/useStripeApi';

type Props = NativeStackScreenProps<AuthStackParamList, 'Subscription'>;

const Subscriptions: React.FC<Props> = ({navigation, route}) => {
  // const [isLoading, setIsLoading] = useState(false);
  // const {uid} = route.params;
  // const dispatch = useAppDispatch();

  // const {initPaymentSheet, presentPaymentSheet, loading} = usePaymentSheet();
  // const {t} = useTranslation();
  // const user = useAppSelector(state => state.user);
  // const {createCustomerOnStripe} = useStripeApi();

  // useEffect(() => {
  //   initializePaymentSheet();
  // }, []);

  // const initializePaymentSheet = async () => {
  //   const {paymentIntent, ephemeralKey, customer} =
  //     await fetchPaymentSheetParams();

  //   const {error} = await initPaymentSheet({
  //     customerId: customer,
  //     customerEphemeralKeySecret: ephemeralKey,
  //     paymentIntentClientSecret: paymentIntent,
  //     merchantDisplayName: `${user.firstName} ${user.surName}`,
  //     allowsDelayedPaymentMethods: true,
  //     appearance: customAppearance,
  //     // returnURL: '' // Callback DeepLink of the app
  //   });
  //   setIsLoading(false);

  //   if (error) {
  //     console.log('Error in initializing payment sheet', error);
  //     Toast.show({
  //       type: 'error',
  //       text2: 'Error initializing payment sheet. try again later',
  //     });
  //   }
  // };

  // const fetchPaymentSheetParams = async () => {
  //   try {
  //     setIsLoading(true);
  //     if (!user.customerId) {
  //       await createCustomerOnStripe(
  //         uid,
  //         user.email,
  //         `${user.firstName} ${user.surName}`,
  //         user.phoneNumber,
  //       );
  //       throw new Error('Customer not created');
  //     }
  //     const res = await functions().httpsCallable('paymentSheet')({
  //       customerId: user.customerId,
  //       amount: 3999,
  //       currency: 'usd',
  //     });

  //     const {paymentIntent, ephemeralKey, customer} = res.data;

  //     return {
  //       paymentIntent,
  //       ephemeralKey,
  //       customer,
  //     };
  //   } catch (error) {
  //     console.log('Error creating Payment Intent:', error);
  //     Toast.show({
  //       type: 'error',
  //       text2: t('No customer found'),
  //     });
  //     throw error;
  //   }
  // };

  // const handlesheet = async () => {
  //   try {
  //     const {error} = await presentPaymentSheet();
  //     if (error) {
  //       console.error(`Error code: ${error.code}`, error.message);
  //       throw error;
  //     } else {
  //       navigation.navigate('PetProfile', {isAuthFlow: true, uid: uid});
  //       await firestore().collection('Users').doc(uid).update({
  //         isPremiumCustomer: true,
  //       });
  //       await functions().httpsCallable('sendPaymentNotification')({
  //         user: user,
  //         uid: uid,
  //       });
  //       dispatch(updateUser({isPremiumCustomer: true}));
  //     }
  //   } catch (error) {
  //     console.error('Error opening Payment Sheet:', error);
  //   }
  // };

  // const customAppearance = {
  //   shapes: {
  //     borderRadius: 12,
  //     borderWidth: 0.5,
  //   },
  //   primaryButton: {
  //     shapes: {
  //       borderRadius: 20,
  //     },
  //   },
  //   colors: {
  //     primary: COLORS.primary,
  //     background: COLORS.white,
  //     componentBackground: '#f3f8fa',
  //     componentBorder: '#f3f8fa',
  //     componentDivider: '#000000',
  //     primaryText: '#000000',
  //     secondaryText: '#000000',
  //     componentText: '#000000',
  //     placeholderText: '#73757b',
  //   },
  // };

  return null;
  // <SafeAreaView style={styles.container}>
  //   <ScrollView style={styles.innerContainer}>
  //     <Text style={styles.title}>{t('Subscription')}</Text>
  //     <View style={styles.card}>
  //       <View style={styles.innerCard}>
  //         <Text style={styles.subTitle}>{t('My Pet Sit')}</Text>
  //       </View>
  //       <View style={{marginHorizontal: 20}}>
  //         <View style={styles.checkCard}>
  //           <Image source={GreenCheck} style={styles.imageContainer} />
  //           <Text style={styles.text}>
  //             {t('Access to a network of Pet Sitters')}
  //           </Text>
  //         </View>
  //         <View style={styles.checkCard}>
  //           <Image source={GreenCheck} style={styles.imageContainer} />
  //           <Text style={styles.text}>{t('Convenient booking')}</Text>
  //         </View>
  //         <View style={styles.checkCard}>
  //           <Image source={GreenCheck} style={styles.imageContainer} />
  //           <Text style={styles.text}>{t('Affordable subscription')}</Text>
  //         </View>
  //         <View style={styles.checkCard}>
  //           <Image source={GreenCheck} style={styles.imageContainer} />
  //           <Text style={styles.text}>{t('Secure payments')}</Text>
  //         </View>
  //         <View style={styles.checkCard}>
  //           <Image source={GreenCheck} style={styles.imageContainer} />
  //           <Text style={styles.text}>{t('Feedback and reviews')}</Text>
  //         </View>
  //         <View style={styles.checkCard}>
  //           <Image source={GreenCheck} style={styles.imageContainer} />
  //           <Text style={styles.text}>{t('Flexible options')}</Text>
  //         </View>
  //         <View style={styles.checkCard}>
  //           <Image source={GreenCheck} style={styles.imageContainer} />
  //           <Text style={styles.text}>
  //             {t(
  //               'We will donate €2 for every subscriber to an animal welfare organisation',
  //             )}
  //           </Text>
  //         </View>

  //         <View style={styles.checkCard}>
  //           <Image source={GreenCheck} style={styles.imageContainer} />
  //           <Text style={styles.text}>
  //             {t('Next to Pet Sitting also other services')}
  //           </Text>
  //         </View>

  //         <Text style={styles.priceText}> €39.99 incl. VAT</Text>
  //         <Text style={styles.perYearText}>{t('Per Year')}</Text>
  //         <TextButton
  //           label={t('Continue')}
  //           containerStyle={styles.button}
  //           onPress={handlesheet}
  //           disabled={isLoading}
  //         />
  //       </View>
  //     </View>
  //   </ScrollView>
  // </SafeAreaView>
};

export default Subscriptions;
