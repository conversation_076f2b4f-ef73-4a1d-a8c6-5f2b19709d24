import {Image, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {COLORS} from '../../themes/themes';
import {SuccesState2} from '../../assets/images';
import appStyles from '../../themes/appStyles';
import {FONTS} from '../../themes/fonts';
import {moderateScale, verticalScale} from 'react-native-size-matters';

interface Props {
  isModalVisible: boolean;
  title: string;
  paragraph: string;
  onBackdropPress?: () => void;
}

const Alert: React.FC<Props> = ({
  isModalVisible,
  title,
  paragraph,
  onBackdropPress,
}) => {
  return (
    <Modal isVisible={isModalVisible} onBackdropPress={onBackdropPress}>
      <View style={styles.container}>
        <Image
          resizeMode="contain"
          source={SuccesState2}
          style={styles.imageContainer}
        />
        <Text style={styles.title}>{title}</Text>
        <Text
          style={[appStyles.paragraph, {textAlign: 'center', marginTop: 18}]}>
          {paragraph}
        </Text>
      </View>
    </Modal>
  );
};

export default Alert;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    alignItems: 'center',
    paddingVertical: verticalScale(26),
    paddingHorizontal: 14,
  },
  imageContainer: {
    width: verticalScale(55),
    height: verticalScale(55),
  },
  title: {
    fontSize: moderateScale(18),
    fontFamily: FONTS.SemiBold,
    color: COLORS.Bastille,
    paddingTop: verticalScale(21),
  },
});
