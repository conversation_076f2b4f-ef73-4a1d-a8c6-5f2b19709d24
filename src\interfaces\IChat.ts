import {FirebaseFirestoreTypes} from '@react-native-firebase/firestore';
import {IConversationBooking} from './IBooking';

export interface IChat {
  name: string;
  profilePicture: string;
  key: string;
  lastMessage: {
    _id: string;
    createdAt: FirebaseFirestoreTypes.Timestamp;
    read: boolean;
    sentBy: string;
    sentTo: string;
    text: string;
    user: {_id: string};
    bookingId: string;
  };
  unReadMessages?: number;
  members: Array<string>;
  serviceDone?: boolean;
  price: string;
  booking?: IConversationBooking;
}
