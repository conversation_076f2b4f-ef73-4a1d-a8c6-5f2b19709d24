import {ActivityIndicator, Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {PetSitterParamList} from '../../../navigation/PetSitterHomeStack';
import {ButtonwithIcon} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {useAppSelector} from '../../../store/Store';
import functions from '@react-native-firebase/functions';
import {COLORS} from '../../../themes/themes';
import WebView from 'react-native-webview';
import useSession from '../../../hooks/useSession';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';

type Props = NativeStackScreenProps<PetSitterParamList, 'SellerProfile'>;
const SellerProfile: React.FC<Props> = ({navigation}) => {
  // Payment Sheet would be used where we need to do payment
  const [accountId, setAccountId] = useState('');
  const [accountLink, setAccountLink] = useState('');
  const [isAccountIdLoading, setIsAccountIDLoading] = useState(false);
  const [accountLinkLoading, setAccountLinkLoading] = useState(false);
  const [isOnboarded, setOnboarded] = useState(false);
  const [accountData, setAccountData] = useState<any>();
  const [error, setError] = useState('');
  const [currentBalance, setCurrentBalance] = useState(0);
  const [isPriceLoading, setIsPriceLoading] = useState(false);
  const {t} = useTranslation();

  const user = useAppSelector(state => state.user);
  const {updateReduxData} = useSession();

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);

  useEffect(() => {
    if (user.accountId) {
      retreiveAccountInfo();
    } else {
      generateOnboarding();
    }
  }, []);

  const retreiveAccountInfo = async () => {
    const response = await functions().httpsCallable('retrieveAccount')({
      accountId: user.accountId,
    });
    if (
      response.data.capabilities.transfers === 'inactive' ||
      response.data.capabilities.payouts === 'inactive' ||
      response.data.requirements.currently_due.length > 0
    ) {
      generateOnboarding();
    } else {
      setAccountData(response.data);
      getBalance();
      setOnboarded(true);
    }
  };

  const getBalance = async () => {
    try {
      setIsPriceLoading(true);
      if (user.accountId) {
        const balance = await functions().httpsCallable('GetCurrenttBalance')({
          accountId: user.accountId,
        });
        console.log('balance', balance);

        setCurrentBalance(balance.data.currentBalance.pending[0].amount);
      }
    } catch (error) {
      console.log('Error fetching current balance', error);
    } finally {
      setIsPriceLoading(false);
    }
  };
  const generateOnboarding = async () => {
    try {
      setAccountLinkLoading(true);
      const response = await functions().httpsCallable(
        'generateOnboardingLink',
      )({accountId: user.accountId, uid: user.uid, email: user.email});

      console.log('Onboarding response', response);
      if (response.data.status === 400) {
        throw response.data;
      }

      updateReduxData(user.uid);

      if (response.data.message) {
        setOnboarded(true);
        // handleServiceDone();
      } else {
        setAccountLink(response.data.url);
      }
    } catch (error: any) {
      console.log('Error creating onboarding', error.error);
      setError(
        'Something not correct! please hang back. This will be fixed soon',
      );
      // Toast.show({type: 'error', text1: 'Error', text2: error.error});
    } finally {
      setAccountLinkLoading(false);
    }
  };

  // ************************ Below code would be used to send payment to pet sitter ************************* //
  // const initialPaymentSheet = async () => {
  //   const {paymentIntent, ephemeralKey, customer} =
  //     await fetchPaymentSheetParams();

  //   const {error} = await initPaymentSheet({
  //     merchantDisplayName: 'Restaurant',
  //     customerId: customer,
  //     customerEphemeralKeySecret: ephemeralKey,
  //     paymentIntentClientSecret: paymentIntent,
  //     returnURL: 'https://www.mypetsit.be/',
  //     allowsDelayedPaymentMethods: true,
  //     defaultBillingDetails: {
  //       name: 'Ali Waqar',
  //     },
  //     applePay: {
  //       merchantCountryCode: 'US',
  //     },
  //     googlePay: {
  //       merchantCountryCode: 'US',
  //       testEnv: true,
  //     },
  //   });

  //   console.log('Error on intial payment sheet', error);
  // };

  // const fetchPaymentSheetParams = async () => {
  //   try {
  //     // setIsLoading(true);

  //     const res = await functions().httpsCallable('payToSeller')({
  //       customerId: user.customerId,
  //       currency: 'eur',
  //     });

  //     const {paymentIntent, ephemeralKey, customer} = res.data;

  //     console.log('Payment Intent>>>>>>>', paymentIntent);

  //     return {
  //       paymentIntent,
  //       ephemeralKey,
  //       customer,
  //     };
  //   } catch (error) {
  //     console.log('Error creating Payment Intent:', error);
  //     Toast.show({
  //       type: 'error',
  //       text2: 'No customer found',
  //     });
  //     throw error;
  //   }
  // };
  // const handleServiceDone = async () => {
  //   const {error} = await presentPaymentSheet();
  //   if (error) {
  //     console.error(`Error code: ${error.code}`, error.message);
  //     throw error;
  //   } else {
  //     // setReviewModalVisible(true);
  //   }
  // };

  // ************************ Above code would be used to send payment to pet sitter ************************* //

  // const createPaymentMethod = async () => {
  //   try {
  //     const resp = await functions().httpsCallable('CreatePayoutStripe')({});
  //     console.log(resp);
  //   } catch (error) {
  //     console.log('eoor', error);
  //   }
  // };

  // const createAccountId = async () => {
  //   try {
  //     const createAccount = await functions().httpsCallable(
  //       'CreateAccountStripe',
  //     )({});
  //     console.log(createAccount);
  //     setAccountId(createAccount.data.id);
  //   } catch (error) {
  //     console.log('Error creating account', error);
  //   }
  // };

  // const createOnboardingLink = async () => {
  //   try {
  //     const createAccountLink = await functions().httpsCallable(
  //       'stripeOnboardingLink',
  //     )({
  //       accountId: accountId,
  //     });
  //     console.log('Account link', createAccountLink);
  //     setAccountLink(createAccountLink.data.url);
  //   } catch (error) {
  //     console.log('Error creating link', error);
  //   }
  // };

  // const handlerSellerPayment = async () => {
  //   try {
  //     const createAccountLink = await functions().httpsCallable(
  //       'stripeOnboardingLink',
  //     )({
  //       accountId: accountId,
  //     });
  //     console.log('Account link', createAccountLink);
  //     setAccountLink(createAccountLink.data.url);
  //   } catch (error) {
  //     console.log('Error creating link', error);
  //   }
  // };

  return (
    <View style={styles.container}>
      {isOnboarded && (
        <>
          <View style={styles.priceContainer}>
            {isPriceLoading ? (
              <ActivityIndicator color={COLORS.primary} size={'large'} />
            ) : (
              <Text style={styles.balanceText}>{currentBalance / 100} €</Text>
            )}
          </View>
          <Text style={[styles.headingText, {marginTop: 64}]}>
            {t('Personal Information')}
          </Text>
          <View style={styles.descContainer}>
            <Text style={styles.descText}>
              {t('Name:')}{' '}
              <Text style={styles.text}>
                {user.firstName} {user.surName}
              </Text>
            </Text>
            <Text style={styles.descText}>
              {'Email:'} <Text style={styles.text}>{user.email}</Text>
            </Text>
            <Text style={styles.descText}>
              {'Phone:'} <Text style={styles.text}>{user.phoneNumber}</Text>
            </Text>
          </View>
          <Text style={styles.headingText}>{t('Business')}</Text>
          <View style={styles.descContainer}>
            <Text style={styles.descText}>
              {t('Business type:')}{' '}
              <Text style={styles.text}>{accountData?.business_type}</Text>
            </Text>
            <Text style={styles.descText}>
              {t('Country:')}{' '}
              <Text style={styles.text}>{accountData?.country}</Text>
            </Text>
          </View>
          <Text style={styles.headingText}>{t('Account info')}</Text>
          <View style={styles.descContainer}>
            <Text style={styles.descText}>
              {t('Bank name:')}{' '}
              <Text style={styles.text}>
                {accountData?.external_accounts?.data[0].bank_name}
              </Text>
            </Text>
            <Text style={styles.descText}>
              {t('Account number:')}
              <Text style={styles.text}>
                ***********{accountData?.external_accounts?.data[0].last4}
              </Text>
            </Text>
            <Text style={styles.descText}>
              {t('Currency:')}{' '}
              <Text style={styles.text}>
                {accountData?.external_accounts?.data[0].currency}
              </Text>
            </Text>
          </View>
        </>
      )}

      {accountLinkLoading && (
        <ActivityIndicator
          size={'large'}
          color={COLORS.primary}
          style={{alignSelf: 'center', marginTop: 100}}
        />
      )}
      {isOnboarded && (
        <>
          <Text style={styles.onboardComplete}>
            {t('You have already completed the onboarding process.')}
          </Text>
          {/* <TextButton label="payment" onPress={handleServiceDone}  containerStyle={}/> */}
        </>
      )}
      {!isOnboarded && !isAccountIdLoading && error && (
        <Text style={styles.onboardComplete}>{error}</Text>
      )}
      {accountLink && (
        <WebView
          source={{uri: accountLink}}
          style={{flex: 1}}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        />
      )}
    </View>
  );
};

export default SellerProfile;
