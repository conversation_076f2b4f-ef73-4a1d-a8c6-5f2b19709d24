import {
  Keyboard,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {
  Alert,
  ButtonwithIcon,
  FormInput,
  TextButton,
} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import {COLORS} from '../../../themes/themes';
import appStyles from '../../../themes/appStyles';
import {FONTS} from '../../../themes/fonts';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
// import {
//   CardField,
//   useStripe,
//   SetupIntent,
//   BillingDetails,
//   CardFieldInput,
// } from '@stripe/stripe-react-native';
import functions from '@react-native-firebase/functions';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import firestore from '@react-native-firebase/firestore';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<AuthStackParamList, 'AddNewCard'>;

const AddNewCard: React.FC<Props> = ({navigation}) => {
  // const [isModalVisible, setModalVisible] = useState(false);
  // const [cardDetails, setCardDetails] = useState<CardFieldInput.Details>();
  // const [paymentIntent, setPaymentIntent] = useState<SetupIntent.Result>();
  // const [isLoading, setLoading] = useState<boolean>(false);

  // const {confirmSetupIntent} = useStripe();
  // const uid = 'ar06dmWRybYurcHoaSVxKRI3SYm2';
  // const cid = 'cus_Q6Yju1xnYvLhXY';

  // const {t} = useTranslation();
  // useEffect(() => {
  //   navigation.setOptions({
  //     headerLeft: () => (
  //       <ButtonwithIcon
  //         icon={<BackArrow />}
  //         onPress={() => navigation.goBack()}
  //       />
  //     ),
  //   });
  // }, []);

  // const storeCard = async () => {
  //   try {
  //     const userDocRef = firestore().collection('Users').doc(uid);
  //     const userData = await userDocRef.get();
  //     const customerId = userData?.data()?.customer;
  //     console.log('Customer Id:', cardDetails?.number);

  //     const paymentMethodResponse = await functions().httpsCallable(
  //       'createPaymentMethod',
  //     )({
  //       card: cardDetails,
  //     });

  //     console.log(
  //       'PaymentMethod REsponse:',
  //       JSON.stringify(paymentMethodResponse.data.paymentMethod, null, 2),
  //     );

  //     const response = await functions().httpsCallable('attachPaymentMethod')({
  //       customer: customerId,
  //       paymentMethod: paymentMethodResponse.data.paymentMethod.id,
  //     });

  //     console.log(
  //       'PaymentMethod Attachment Response:',
  //       JSON.stringify(response.data, null, 2),
  //     );
  //   } catch (error) {
  //     console.log('Error in saving carding', error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // const handlePayPress = async () => {
  //   setLoading(true);
  //   // Gather the customer's billing information (for example, email)
  //   const billingDetails: BillingDetails = {
  //     email: '<EMAIL>',
  //   };
  //   // cardDetails.

  //   // Create a setup intent on the backend
  //   const cardResposne = await storeCard();

  //   console.log('Card Response', cardResposne);
  //   navigation.navigate('Subscription');
  // };

  // const validationSchema = Yup.object().shape({
  //   cardName: Yup.string(),
  //   cardNumber: Yup.string()
  //     .required('Card Number is required')
  //     .min(19, 'Card Number must be 14 characters long'),
  //   expiryDate: Yup.string().required('Expiry Date is required'),
  //   cvc: Yup.string().required('CVC is required'),
  // });

  // const formik = useFormik({
  //   initialValues: {
  //     cardName: '',
  //     cardNumber: '',
  //     expiryDate: '',
  //     cvc: '',
  //     billingInformation: {
  //       firstName: '',
  //       lastName: '',
  //       address: '',
  //       state: '',
  //       zipCode: '',
  //     },
  //   },
  //   validationSchema,
  //   onSubmit: values => {
  //     console.log('Submitted vcalues', values);
  //     // createCard();
  //   },
  // });
  return null;
  // <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
  //   <View style={styles.container}>
  //     <CardField
  //       postalCodeEnabled={true}
  //       placeholders={{
  //         number: '4242 4242 4242 4242',
  //       }}
  //       cardStyle={{
  //         backgroundColor: '#FFFFFF',
  //         textColor: '#000000',
  //       }}
  //       style={{
  //         width: '100%',
  //         height: 50,
  //         marginVertical: 30,
  //       }}
  //       onCardChange={cardDetails => {
  //         console.log('cardDetails', JSON.stringify(cardDetails, null, 2));
  //         setCardDetails(cardDetails);
  //       }}
  //       onFocus={focusedField => {
  //         console.log('focusField', focusedField);
  //       }}
  //       dangerouslyGetFullCardDetails
  //     />

  //     <TextButton
  //       label="Confirm Payment"
  //       containerStyle={{marginTop: 24}}
  //       onPress={handlePayPress}
  //     />
  //   </View>
  // </TouchableWithoutFeedback>
};

export default AddNewCard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
  },
  cardContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginTop: 18,
  },
  labelStyle: {
    fontFamily: FONTS.SemiBold,
  },
  labelStyle2: {
    fontFamily: FONTS.Bold,
  },
});
