import {
  Image,
  Text,
  View,
  ImageSourcePropType,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import {COLORS, SIZES} from '../../themes/themes';
import appStyles from '../../themes/appStyles';
import TextButton from '../Buttons/TextButton';
import {verticalScale} from 'react-native-size-matters';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';

interface Props {
  onPress?: () => void;
  paragraph: string;
  title: string;
  showActionButton?: boolean;
  buttonTitle?: string;
  image: ImageSourcePropType;
  showSkipButton?: boolean;
  isWhite?: boolean;
  onSkipPress?: () => void;
}

const SuccesModal: React.FC<Props> = ({
  onPress,
  title,
  paragraph,
  showActionButton,
  buttonTitle,
  image,
  isWhite,
  showSkipButton,
  onSkipPress,
}) => {
  const {t} = useTranslation();
  return (
    <View style={[styles.container]}>
      <View style={styles.innerContainer}>
        {/* IMAGE */}
        <Image
          resizeMode="contain"
          source={image}
          style={styles.imageContainer}
        />
        {/* TITLE */}
        <Text
          style={[
            appStyles.title,
            {
              color: isWhite ? COLORS.Bastille : COLORS.white,
              paddingTop:
                SIZES.DeviceHeight > 760
                  ? verticalScale(60)
                  : verticalScale(40),
            },
          ]}>
          {title}
        </Text>
        {/* PARAGRAPH */}
        <Text
          style={[
            appStyles.paragraph,
            {
              marginTop: verticalScale(6),
              textAlign: 'center',
              color: isWhite ? COLORS.spanishgray : COLORS.white,
            },
          ]}>
          {paragraph}
        </Text>
      </View>
      <View style={styles.buttonsContainer}>
        {showActionButton ? (
          <TextButton
            label={buttonTitle}
            containerStyle={{
              backgroundColor: isWhite ? COLORS.primary : COLORS.white,
            }}
            labelStyle={{color: isWhite ? COLORS.white : COLORS.primary}}
            onPress={onPress}
          />
        ) : null}
        {showSkipButton ? (
          <TouchableOpacity
            onPress={onSkipPress}
            style={styles.skipButton}
            hitSlop={{left: 40, right: 40, top: 10, bottom: 20}}>
            <Text style={styles.buttonLabel}>{t('Skip')}</Text>
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default SuccesModal;
