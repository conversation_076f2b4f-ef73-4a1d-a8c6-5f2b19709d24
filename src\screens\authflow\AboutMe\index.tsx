import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import About from '../ProfileVerification/AboutMe';
import {useFormik} from 'formik';
import {useTranslation} from 'react-i18next';
import {AppText, ButtonwithIcon, TextButton} from '../../../components';
import {COLORS} from '../../../themes/themes';
import * as Yup from 'yup';
import firestore from '@react-native-firebase/firestore';
import {BackArrow} from '../../../assets/svgIcons';

type Props = NativeStackScreenProps<AuthStackParamList, 'AboutMe'>;

const validationSchema = Yup.object().shape({
  about: Yup.string().required('About me is required'),
});

const AboutMe: React.FC<Props> = ({navigation, route}) => {
  const [isLoading, setIsLoading] = useState(false);
  const {phoneNumber, uid} = route.params;
  const {t} = useTranslation();

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);
  const formik = useFormik({
    initialValues: {
      about: '',
    },
    validationSchema,
    validateOnMount: true,
    onSubmit: async values => {
      try {
        setIsLoading(true);
        await firestore()
          .collection('Users')
          .doc(uid)
          .update({about: values.about})
          .then(() => {
            navigation.navigate('CodeVerification', {
              uid: uid,
              phoneNumber: phoneNumber,
            });
          });
      } catch (error) {
        console.log('Error updating data', error);
      } finally {
        setIsLoading(false);
      }
    },
  });

  return (
    <View style={styles.container}>
      <About
        label={t('Tell us something about yourself')}
        placeholder={t('Type here...')}
        placeholderTextColor={COLORS.placeHolder}
        value={formik.values.about}
        onChangeText={formik.handleChange('about')}
        onBlur={() => formik.setFieldTouched('about')}
        showError={!!formik.touched.about}
        errorText={formik.errors.about}
      />
      <TextButton
        label={t('Continue')}
        onPress={formik.handleSubmit}
        containerStyle={{marginTop: 24, marginBottom: 50}}
        disabled={!formik.isValid || isLoading}
        isLoading={isLoading}
      />
    </View>
  );
};

export default AboutMe;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: COLORS.white,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
});
