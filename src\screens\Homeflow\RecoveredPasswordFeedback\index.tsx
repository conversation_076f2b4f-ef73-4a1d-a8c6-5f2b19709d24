import {StyleSheet, View} from 'react-native';
import React from 'react';

import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ActionFeedBack} from '../../../components';
import {SuccesState} from '../../../assets/images';
import {COLORS} from '../../../themes/themes';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import {useTranslation} from 'react-i18next';

type Props = NativeStackScreenProps<
  PetOwnerParamList,
  'RecoveredPasswordFeedback'
>;

const RecoveredPasswordFeedback: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <ActionFeedBack
        image={SuccesState}
        title={t('Password Recovered')}
        paragraph={t('The password has been successfully recovered,')}
        buttonTitle={t('Back to Settings')}
        showActionButton
        onPress={() => navigation.pop(2)}
      />
    </View>
  );
};

export default RecoveredPasswordFeedback;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.primary,
    paddingBottom: 80,
  },
});
