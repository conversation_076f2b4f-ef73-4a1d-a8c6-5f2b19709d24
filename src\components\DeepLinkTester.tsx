import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Linking,
  Alert,
} from 'react-native';
import {COLORS} from '../themes/themes';
import {FONTS} from '../themes/fonts';

const DeepLinkTester = () => {
  const testLinks = [
    {label: 'Payment Profile (No UID)', url: 'petsit://payment-onboarding'},
    {
      label: 'Payment Profile (With UID)',
      url: 'petsit://payment-onboarding/user123',
    },
    {
      label: 'Payment Profile (Different UID)',
      url: 'petsit://payment-onboarding/user456',
    },
  ];

  const handleTestLink = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', `Cannot open URL: ${url}`);
      }
    } catch (error) {
      Alert.alert('Error', `Failed to open URL: ${error}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Deep Link Tester</Text>
      <Text style={styles.subtitle}>Tap any link to test deep linking:</Text>

      {testLinks.map((link, index) => (
        <TouchableOpacity
          key={index}
          style={styles.linkButton}
          onPress={() => handleTestLink(link.url)}>
          <Text style={styles.linkText}>{link.label}</Text>
          <Text style={styles.urlText}>{link.url}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: COLORS.white,
  },
  title: {
    fontSize: 24,
    fontFamily: FONTS.Bold,
    color: COLORS.primary,
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: FONTS.Medium,
    color: COLORS.gray85,
    textAlign: 'center',
    marginBottom: 20,
  },
  linkButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  linkText: {
    fontSize: 16,
    fontFamily: FONTS.SemiBold,
    color: COLORS.white,
    marginBottom: 5,
  },
  urlText: {
    fontSize: 12,
    fontFamily: FONTS.Medium,
    color: COLORS.white,
    opacity: 0.8,
  },
});

export default DeepLinkTester;
