import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.whiteSmoke,
    height: verticalScale(46),
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: 8,
  },
  loginButton: {
    height: verticalScale(38),
    width: '48.5%',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
  },
});
