import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import { verticalScale, scale } from 'react-native-size-matters';

interface Props {
  image: ImageSourcePropType;
  selectedImage?: string;
  setSelectedImage: (uri: string) => void;
  handleImage: () => void
}

const IDCard: React.FC<Props> = ({ image, selectedImage, setSelectedImage, handleImage }) => {


  console.log('------selectedImage', selectedImage);

  return (
    <TouchableOpacity onPress={handleImage}>
      <Image
        resizeMode="contain"
        source={selectedImage ? { uri: selectedImage } : image}
        style={styles.image}
      />
    </TouchableOpacity>
  );
};

export default IDCard;

const styles = StyleSheet.create({
  image: {
    width: scale(150),
    height: verticalScale(130),
  },
});
