import {StyleSheet} from 'react-native';
import {COLORS} from '../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    marginTop: verticalScale(16),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    marginHorizontal: 4,
  },
  imageContainer: {
    width: '100%',
    height: verticalScale(145),
    borderRadius: 10,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  profileImage: {
    width: verticalScale(30),
    height: verticalScale(30),
    borderRadius: verticalScale(30 / 2),
    marginRight: 6,
  },
  innerContainer: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  name: {
    fontSize: 14,
    fontFamily: FONTS.SemiBold,
    color: COLORS.Bastille,
  },
  label2: {
    fontSize: 10,
    fontFamily: FONTS.Medium,
    color: '#989898',
    marginLeft: 4,
  },
  userContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-start',
  },
  label: {
    color: COLORS.Bastille,
    marginLeft: 6,
  },
  menuLabel: {
    fontSize: 11,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
  },
  divier: {
    height: 1.5,
    backgroundColor: '#EEEEEE',
  },
  popUpMenuContainer: {
    borderRadius: 5,
    width: 80,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 30,
    marginLeft: -40,
  },
  ratingContainer: {
    backgroundColor: COLORS.white,
    margin: 12,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  dateText: {
    fontSize: 14,
    color: COLORS.Bastille,
    fontFamily: FONTS.Medium,
  },
  loadingIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -12,
    marginTop: -12,
  },
});
