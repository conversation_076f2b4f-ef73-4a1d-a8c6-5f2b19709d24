import {StyleSheet} from 'react-native';
import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {
  Notification,
  PetSitterDetails,
  ChatDetails,
  TermofUse,
  PrivacyPolicy,
  MyPets,
  MyReviews,
  ContactUs,
  DeleteAccount,
  RecoveredPasswordFeedback,
  ResetPassword,
  EditRequest,
} from '../../screens/Homeflow';

import PetOwnerBottomTabs from '../PetOwnerBottomTabs';

import {OwnerBottomStackParamList} from '../PetOwnerBottomTabs';
import {PetProfile, AddService} from '../../screens/authflow';
import RequestScreen from '../../screens/Homeflow/Request';
import EditProfile from '../../screens/Homeflow/EditProfile';
import PetOwnerSeeAll from '../../screens/Homeflow/SeeAllPetsitters';
import {IUser} from '../../interfaces';
import SelectLanguage from '../../screens/authflow/SelectLanguage';
import PetProfileView from '../../screens/Homeflow/PetProfileView';
import {IPet, IService} from '../../interfaces/IPet';
import {IConversationBooking} from '../../interfaces/IBooking';
import SelectPetServices from '../../screens/Homeflow/SelectPetServices/SelectPetServices';
import IdVerification from '../../screens/Homeflow/IdVerification/IdVerification';
import FAQ from '../../screens/Homeflow/FAQ';

export type PetOwnerParamList = {
  PetOwnerBottomTabs: OwnerBottomStackParamList;
  Notification: undefined;
  PetSitterDetails: {petSitter: IUser};
  ChatDetails: {
    recipientId: string;
    serviceDone: boolean;
    booking?: IConversationBooking;
  };
  TermofUse: undefined;
  PrivacyPolicy: undefined;
  FAQ: undefined;
  MyPets: undefined;
  AddService: {addservice: (service: string) => void};
  PetProfile2: {uid: string; petData?: IPet};
  MyReviews: undefined;
  EmailVerification: undefined;
  CodeVerification: {screen: string};
  IdVerification: undefined;
  ResetPassword: undefined;
  ContactUs: undefined;
  DeleteAccount: undefined;
  RecoveredPasswordFeedback: undefined;
  RequestScreen: undefined;
  EditRequest: {isEdit: boolean; bookingData: any};
  EditProfile: undefined;
  PetOwnerSeeAll: undefined;
  SelectLanguage1: undefined;
  PetProfileView: {item: IPet};
  SelectPetServices: {item: IPet; sitter: IUser};
};
const PetHomeStack = createNativeStackNavigator<PetOwnerParamList>();
const PetOwnerHomeStack = () => {
  return (
    <PetHomeStack.Navigator
      screenOptions={{headerShown: false, headerShadowVisible: false}}>
      <PetHomeStack.Screen
        name="PetOwnerBottomTabs"
        component={PetOwnerBottomTabs}
      />
      <PetHomeStack.Screen
        name="Notification"
        component={Notification}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name="PetSitterDetails"
        component={PetSitterDetails}
        options={{headerShown: false}}
      />
      <PetHomeStack.Screen
        name="ChatDetails"
        component={ChatDetails}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name="TermofUse"
        component={TermofUse}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name="PrivacyPolicy"
        component={PrivacyPolicy}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name="FAQ"
        component={FAQ}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name="MyPets"
        component={MyPets}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name="AddService"
        component={AddService}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name="PetProfile2"
        component={PetProfile}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name="IdVerification"
        component={IdVerification}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name="MyReviews"
        component={MyReviews}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name={'ResetPassword'}
        component={ResetPassword}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name={'RecoveredPasswordFeedback'}
        component={RecoveredPasswordFeedback}
        options={{headerShown: false}}
      />
      <PetHomeStack.Screen
        name={'ContactUs'}
        component={ContactUs}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name={'DeleteAccount'}
        component={DeleteAccount}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name={'RequestScreen'}
        component={RequestScreen}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name={'EditRequest'}
        component={EditRequest}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name={'EditProfile'}
        component={EditProfile}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name={'PetOwnerSeeAll'}
        component={PetOwnerSeeAll}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetHomeStack.Screen
        name={'SelectLanguage1'}
        component={SelectLanguage}
        options={{headerShown: false}}
      />

      <PetHomeStack.Screen
        name={'PetProfileView'}
        component={PetProfileView}
        options={{headerShown: false}}
      />
      <PetHomeStack.Screen
        name={'SelectPetServices'}
        component={SelectPetServices}
        options={{headerShown: false}}
      />
    </PetHomeStack.Navigator>
  );
};

export default PetOwnerHomeStack;

const styles = StyleSheet.create({});
