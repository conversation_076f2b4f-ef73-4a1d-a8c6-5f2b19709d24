import {
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {useEffect, useLayoutEffect, useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {
  AppText,
  ButtonwithIcon,
  FormInput,
  TextButton,
} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import firestore from '@react-native-firebase/firestore';
import {styles} from '../../authflow/ProfileVerification/styles';
import {useDispatch} from 'react-redux';
import {setUser} from '../../../slices/userSlice';
import Toast from 'react-native-toast-message';
import PhoneNumber from '../../authflow/ProfileVerification/PhoneNumber';
import AboutMe from '../../authflow/ProfileVerification/AboutMe';
import {useAppSelector} from '../../../store/Store';
import appStyles from '../../../themes/appStyles';
import {BackArrow} from '../../../assets/svgIcons';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import {Camera, ImagePlaceholder} from '../../../assets/images';
import ImagePicker from 'react-native-image-crop-picker';
import storage from '@react-native-firebase/storage';
import {GooglePlaceDetail} from 'react-native-google-places-autocomplete';
import Config from 'react-native-config';

import {useTranslation} from 'react-i18next';
import {useLanguage} from '../../../contexts/LanguageContext';
import {getPlaceComponents} from '../../../helpers/place-details';
import * as geofirestore from 'geofirestore';
import {Keyboard} from 'react-native';
import PlacesAutocomplete from '../../../components/PlacesAutocomplete';

type Props = NativeStackScreenProps<PetOwnerParamList, 'EditProfile'>;

const EditProfile: React.FC<Props> = ({navigation}) => {
  const user = useAppSelector(state => state.user);
  const [isLoading, setIsLoading] = useState(false);
  const [countryCode, setCountryCode] = useState<string>('');
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [location, setLocation] = useState({
    latitude: user?.coordinates.latitude || 0,
    longitude: user?.coordinates.longitude || 0,
  });
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {selectedLanguage} = useLanguage();
  useLayoutEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          hitSlop={appStyles.hitSlop}
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, [navigation]);

  const validationSchema = Yup.object().shape({
    firstName: Yup.string().required(t('Enter your first name')),
    surName: Yup.string().required(t('Enter your sur name')),
    email: Yup.string()
      .required(t('Enter an email'))
      .email(t('Email must be a valid email')),
    phoneNumber: Yup.string().required(t('Enter a phone number')),
    address: Yup.string().required(t('Enter your complete address')),
    city: Yup.string().required(t('Enter your city')),
    country: Yup.string().required(t('Enter your country')),
    about: Yup.string(),
    profilePicture: Yup.string(),
  });

  const setLocationDetail = (placeDetail: GooglePlaceDetail) => {
    const {country, formattedAddress, city} = getPlaceComponents(placeDetail);

    setLocation({
      longitude: placeDetail?.geometry.location.lng,
      latitude: placeDetail?.geometry.location.lat,
    });

    formik.setFieldValue('city', city);
    formik.setFieldValue('country', country);
    formik.setFieldValue('address', formattedAddress);
    formik.validateForm();
  };

  const handleContinue = async () => {
    setIsLoading(true);
    let imageURL;
    if (selectedImage) {
      imageURL = await handleUpload();
    }
    const code = countryCode ? countryCode : user.countryCode;

    const firestoreApp = firestore();
    const GeoFirestore = geofirestore.initializeApp(firestoreApp);
    const geocollection = GeoFirestore.collection('Users');

    try {
      const payload = {
        firstName: formik.values.firstName,
        surName: formik.values.surName,
        email: formik.values.email,
        phoneNumber: `+${code}${formik.values.phoneNumber}`,
        address: formik.values.address,
        city: formik.values.city,
        country: formik.values.country,
        about: formik.values.about || '',
        countryCode: code,
        profilePicture: selectedImage ? imageURL : user.profilePicture,
        coordinates: location,
      };

      const updatedUserData = {...user, ...payload};

      await geocollection
        .doc(user.uid)
        .update(payload)
        .then(async () => {
          const petsQuerySnapshot = await firestore()
            .collection('Pets')
            .where('userId', '==', user.uid)
            .get();

          const petUpdatePromises = petsQuerySnapshot.docs.map(async petDoc => {
            await firestore()
              .collection('Pets')
              .doc(petDoc.id)
              .update({user: updatedUserData});
          });

          await Promise.all(petUpdatePromises);

          dispatch(setUser(updatedUserData));
          setIsLoading(false);
          Toast.show({
            type: 'success',
            text1: t('Success'),
            text2: t('Profile updated successfully'),
          });
          navigation.goBack();
        });
    } catch (error) {
      console.log(
        'An error occurred while handling the continue action:',
        error,
      );
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Sorry couldnot save information'),
      });
      setIsLoading(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      firstName: user.firstName,
      surName: user.surName,
      email: user.email,
      phoneNumber: user.phoneNumber
        .toString()
        .replace(`+${user.countryCode}`, ''),
      address: user.address,
      city: user.city,
      country: user.country,
      about: user.about,
      profilePicture: user.profilePicture,
    },
    onSubmit: handleContinue,
    validateOnMount: true,
    validationSchema: validationSchema,
    enableReinitialize: true,
  });
  const handleSelectImage = () => {
    ImagePicker.openPicker({
      width: 300,
      height: 400,
      cropping: false,
      compressImageQuality: 0.25,
    }).then(image => {
      setSelectedImage(image.path);
      formik.setFieldValue('profilePicture', image.path);
    });
  };
  const handleUpload = async () => {
    const reference = storage().ref('profilePicture/' + `${user.uid}_img`);
    const pathToFile = selectedImage && selectedImage;
    pathToFile && (await reference.putFile(pathToFile));
    const url = await storage()
      .ref('profilePicture/' + `${user.uid}_img`)
      .getDownloadURL();
    return url;
  };

  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS == 'android' ? 'height' : 'padding'}>
      <SafeAreaView style={styles.container}>
        <View style={styles.innerContainer}>
          <Text style={styles.title}>{t('Edit profile')}</Text>
          <ScrollView
            contentContainerStyle={{paddingBottom: 200}}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="always">
            <View>
              <View style={styles.imageContainer}>
                <Image
                  source={
                    formik.values.profilePicture
                      ? {uri: formik.values.profilePicture}
                      : ImagePlaceholder
                  }
                  style={styles.imageStyles}
                  loadingIndicatorSource={ImagePlaceholder}
                />
                <TouchableOpacity
                  style={styles.cameraIcon}
                  onPress={handleSelectImage}>
                  <Image source={Camera} style={styles.icon} />
                </TouchableOpacity>
              </View>
              <FormInput
                label={t('First Name')}
                placeholder={t('First Name')}
                placeholderTextColor={COLORS.placeHolder}
                onChangeText={formik.handleChange('firstName')}
                onBlur={() => formik.setFieldTouched('firstName')}
                value={formik.values.firstName}
                containerStyle={styles.margin}
              />
              <AppText
                error={formik.errors.firstName}
                visible={!!formik.touched.firstName}
              />
              <FormInput
                label={t('Surname')}
                placeholder={t('Surname')}
                placeholderTextColor={COLORS.placeHolder}
                onChangeText={formik.handleChange('surName')}
                onBlur={() => formik.setFieldTouched('surName')}
                value={formik.values.surName}
                containerStyle={styles.margin}
              />
              <AppText
                error={formik.errors.surName}
                visible={!!formik.touched.surName}
              />
              <FormInput
                label={t('Email Address')}
                placeholder={t('<EMAIL>')}
                placeholderTextColor={COLORS.placeHolder}
                containerStyle={styles.margin}
                onChangeText={formik.handleChange('email')}
                onBlur={() => formik.setFieldTouched('email')}
                value={formik.values.email}
                editable={false}
              />
              <AppText
                error={formik.errors.email}
                visible={!!formik.touched.email}
              />
              <PhoneNumber
                label={t('Phone Number')}
                placeholder={t('Phone no.')}
                placeholderTextColor={COLORS.placeHolder}
                onChangeText={formik.handleChange('phoneNumber')}
                onBlur={() => formik.setFieldTouched('phoneNumber')}
                onCountryCodeChange={countryCode => setCountryCode(countryCode)}
                value={formik.values.phoneNumber}
                userCountryCode={user.countryCode}
                selectedCode={user.countryCode}
              />
              <AppText
                error={formik.errors.phoneNumber}
                visible={!!formik.touched.phoneNumber}
              />

              <PlacesAutocomplete
                apiKey={Config.GOOGLE_MAPS_API_KEY}
                onPlaceSelected={(place, details) => {
                  console.log('Selected place:', place);
                  if (details) {
                    setLocationDetail(details);
                  }
                }}
                fetchDetails={true}
                countryCode="be"
                textInputProps={{placeholder: t('Search')}}
                containerStyle={{marginTop: 10}}
                label={t('Search location')}
              />
              {/* <Text style={styles.labelText}>{t('Search location')}</Text> */}
              {/* <GooglePlacesAutocomplete
                minLength={1}
                fetchDetails
                placeholder={t('Search')}
                onPress={(data, details = null) => {
                  if (details) {
                    setLocationDetail(details);
                  }
                }}
                onFail={error => {
                  console.log('Error:', error);
                }}
                query={{
                  key: Config.GOOGLE_MAPS_API_KEY,
                  language: selectedLanguage,
                }}
                styles={{
                  container: styles.searchContainer,
                }}
                textInputProps={{}}
                enablePoweredByContainer={false}
                predefinedPlaces={[]}
                autoFillOnNotFound={false}
                currentLocation={false}
                currentLocationLabel="Current location"
                debounce={0}
                disableScroll={false}
                enableHighAccuracyLocation={true}
                timeout={20000}
                isNewPlacesAPI={false}
                fields="*"
              /> */}
              <FormInput
                label={t('Address')}
                placeholder={t('Full Address')}
                placeholderTextColor={COLORS.placeHolder}
                containerStyle={styles.margin}
                onChangeText={formik.handleChange('address')}
                onBlur={() => formik.setFieldTouched('address')}
                onFocus={() => formik.setFieldTouched('address')}
                keyboardType="email-address"
                value={formik.values.address}
                editable={false}
              />
              <AppText
                error={formik.errors.address}
                visible={!!formik.touched.address}
              />
              <FormInput
                label={t('City')}
                placeholder={t('City name')}
                placeholderTextColor={COLORS.placeHolder}
                containerStyle={styles.margin}
                onChangeText={formik.handleChange('city')}
                onBlur={() => formik.setFieldTouched('city')}
                value={formik.values.city}
                editable={false}
              />
              <AppText
                error={formik.errors.city}
                visible={!!formik.touched.city}
              />
              <FormInput
                label={t('Country')}
                placeholder={t('Country name')}
                placeholderTextColor={COLORS.placeHolder}
                containerStyle={styles.margin}
                onChangeText={formik.handleChange('country')}
                onBlur={() => formik.setFieldTouched('country')}
                value={formik.values.country}
                editable={false}
              />
              <AppText
                error={formik.errors.country}
                visible={!!formik.touched.country}
              />

              <AboutMe
                label={t('About me')}
                placeholder={t('Type here...')}
                placeholderTextColor={COLORS.placeHolder}
                onChangeText={formik.handleChange('about')}
                onBlur={() => formik.setFieldTouched('about')}
                value={formik.values.about}
                onSubmitEditing={() => Keyboard.dismiss()}
                blurOnSubmit
              />
              <AppText
                error={formik.errors.about}
                visible={!!formik.touched.about}
              />
              <TextButton
                label={t('Continue')}
                onPress={formik.handleSubmit}
                containerStyle={{marginTop: 24}}
                disabled={isLoading || !formik.isValid}
                isLoading={isLoading}
              />
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default EditProfile;
