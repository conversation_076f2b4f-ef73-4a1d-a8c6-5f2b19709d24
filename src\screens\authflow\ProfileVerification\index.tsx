import {
  <PERSON><PERSON>rea<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>View,
  Text,
  View,
  TouchableOpacity,
  Image,
} from 'react-native';
import React, {useContext, useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {
  AppText,
  FormInput,
  ImageUploadModal,
  TextButton,
} from '../../../components';
import AboutMe from './AboutMe';
import PhoneNumber from './PhoneNumber';
import DateofBirthPicker from './DateofBirthPicker';
import Modal from 'react-native-modal';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {gallary, Camera, upload} from '../../../assets/images';
import ImagePicker from 'react-native-image-crop-picker';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {IProfileVerification, IUser} from '../../../interfaces';
import firestore from '@react-native-firebase/firestore';
import {styles} from './styles';
import storage from '@react-native-firebase/storage';
import {AuthContext} from '../../../../App';
import {useDispatch} from 'react-redux';
import {setUser} from '../../../slices/userSlice';
import Toast from 'react-native-toast-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useStripeApi from '../../../hooks/useStripeApi';
import {GooglePlaceDetail} from 'react-native-google-places-autocomplete';
import Config from 'react-native-config';

import {useTranslation} from 'react-i18next';
import {useLanguage} from '../../../contexts/LanguageContext';
import {getPlaceComponents} from '../../../helpers/place-details';
import * as geofirestore from 'geofirestore';
import PlacesAutocomplete from '../../../components/PlacesAutocomplete';

type Props = NativeStackScreenProps<AuthStackParamList, 'ProfileVerification'>;

const ProfileVerification: React.FC<Props> = ({navigation, route}) => {
  const {firstName, surName, email, uid} = route.params;
  const [isModalVisible, setModalVisible] = useState(false);
  const [selectedImageField, setSelectedImageField] = useState<string>('');
  const [selectedIDFrontSide, setSelectedIDFrontSide] = useState<string>();
  const [selectedIDBackSide, setSelectedIDBackSide] = useState<string>();
  const {isOwner} = useContext(AuthContext);
  const [isLoading, setIsLoading] = useState(false);
  const [countryCode, setCountryCode] = useState<string>('1');
  const [location, setLocation] = useState({
    latitude: 0,
    longitude: 0,
  });
  const dispatch = useDispatch();
  const {t} = useTranslation();
  const {selectedLanguage} = useLanguage();
  // const {createCustomerOnStripe} = useStripeApi();

  const validationSchema = Yup.object().shape({
    firstName: Yup.string().required(t('Enter your first name')),
    surName: Yup.string().required(t('Enter your sur name')),
    email: Yup.string()
      .required(t('Enter an email'))
      .email(t('Email must be a valid email')),
    phoneNumber: Yup.string().required(t('Enter a phone number')),
    address: Yup.string().required(t('Enter your complete address')),
    city: Yup.string().required(t('Enter your city')),
    country: Yup.string().required(t('Enter your country')),
    dateOfBirth: Yup.date().required(t('Enter your date of birth')),
    about: Yup.string(),
    idFrontSide: Yup.string().required(t('ID card front image is required')),
    idBackSide: Yup.string().required(t('ID card back image is required')),
    experience: Yup.number(),
  });

  const setLocationDetail = (placeDetail: GooglePlaceDetail) => {
    const {country, formattedAddress, city} = getPlaceComponents(placeDetail);

    setLocation({
      longitude: placeDetail?.geometry.location.lng,
      latitude: placeDetail?.geometry.location.lat,
    });

    formik.setFieldValue('city', city);
    formik.setFieldValue('country', country);
    formik.setFieldValue('address', formattedAddress);
    formik.validateForm();
  };

  const handleContinue = async () => {
    setIsLoading(true);
    const idFrontSide = await handleFrontSideUploadImage();
    const idBackSide = await handleBackSideUploadImage();
    const fcmToken = (await AsyncStorage.getItem('fcmToken')) || '';

    // Create a Firestore reference
    const firestoreApp = firestore();
    // Create a GeoFirestore reference
    const GeoFirestore = geofirestore.initializeApp(firestoreApp);
    // Create a GeoCollection reference
    const geocollection = GeoFirestore.collection('Users');
    // await createCustomerOnStripe(
    //   uid,
    //   email,
    //   `${firstName} ${surName}`,
    //   formik.values.phoneNumber,
    // );

    try {
      const phoneNumber = `+${countryCode}${formik.values.phoneNumber}`;
      const userData: Partial<IUser> = {
        firstName: formik.values.firstName,
        surName: formik.values.surName,
        email: formik.values.email,
        phoneNumber: phoneNumber,
        address: formik.values.address,
        city: formik.values.city,
        country: formik.values.country,
        dateOfBirth: new Date(formik.values.dateOfBirth),
        about: formik.values.about || '',
        idFrontSide: idFrontSide,
        idBackSide: idBackSide,
        countryCode: countryCode,
        fcmToken: fcmToken,
        uid,
        profileStatus: 'pending',
        coordinates: new firestore.GeoPoint(
          location.latitude,
          location.longitude,
        ),
        ...(isOwner ? {} : {experience: formik.values.experience || 0}),
        isBlocked: false,
      };

      await geocollection
        .doc(uid)
        .set(userData, {merge: true})
        .then(async () => {
          const userResponse = await geocollection.doc(uid).get();
          dispatch(setUser({...userResponse.data(), uid: uid} as IUser));
          await AsyncStorage.setItem('uid', uid);
          navigation.navigate('AboutMe', {
            uid: uid,
            phoneNumber: phoneNumber,
          });
        });
    } catch (error) {
      console.log(
        'An error occurred while handling the continue action:',
        error,
      );
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Sorry couldnot save information'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<IProfileVerification>({
    initialValues: {
      firstName: firstName,
      surName: surName,
      email: email,
      phoneNumber: '',
      address: '',
      city: '',
      country: '',
      dateOfBirth: new Date(2000, 0, 1),
      experience: 0,
      about: '',
      idFrontSide: '',
      idBackSide: '',
    },
    onSubmit: handleContinue,
    validateOnMount: true,
    validationSchema: validationSchema,
  });

  const {dateOfBirth} = formik.values;
  const handlePickImage = () => {
    ImagePicker.openPicker({
      width: 300,
      height: 400,
      cropping: false,
      maxFiles: 1,
      mediaType: 'photo',
      compressImageQuality: 0.25,
    }).then(image => {
      setModalVisible(false);

      if (selectedImageField === 'idFrontSide') {
        setSelectedIDFrontSide(image.path);
        formik.setFieldValue('idFrontSide', image.path);

        handleFrontSideUploadImage();
        formik.setFieldTouched('idFrontSide', true);
      } else if (selectedImageField === 'idBackSide') {
        setSelectedIDBackSide(image.path);
        handleBackSideUploadImage();
        formik.setFieldTouched('idBackSide', true);
        formik.setFieldValue('idBackSide', image.path);
      }
    });
  };

  const handleCameraImage = () => {
    ImagePicker.openCamera({
      width: 300,
      height: 400,
      cropping: false,
      mediaType: 'photo',
      compressImageQuality: 0.25,
    }).then(image => {
      setModalVisible(false);

      if (selectedImageField === 'idFrontSide') {
        setSelectedIDFrontSide(image.path);
        handleFrontSideUploadImage();
        formik.setFieldTouched('idFrontSide', true);
        formik.setFieldValue('idFrontSide', image.path);
      } else if (selectedImageField === 'idBackSide') {
        setSelectedIDBackSide(image.path);
        handleBackSideUploadImage();
        formik.setFieldTouched('idBackSide', true);
        formik.setFieldValue('idBackSide', image.path);
      }
    });
  };

  const handleFrontSideUploadImage = async () => {
    const reference = storage().ref('idCard/' + `${uid}frontSide_img`);
    const pathToFile = selectedIDFrontSide && selectedIDFrontSide;
    pathToFile && (await reference.putFile(pathToFile));
    const url = await storage()
      .ref('idCard/' + `${uid}frontSide_img`)
      .getDownloadURL();
    return url;
  };
  const handleBackSideUploadImage = async () => {
    const reference = storage().ref('idCard/' + `${uid}BackSide_img`);
    const pathToFile = selectedIDBackSide && selectedIDBackSide;
    pathToFile && (await reference.putFile(pathToFile));
    const url = await storage()
      .ref('idCard/' + `${uid}BackSide_img`)
      .getDownloadURL();
    return url;
  };

  const handleModal = (fieldName: string) => {
    setSelectedImageField(fieldName);
    setModalVisible(true);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.innerContainer}>
        <Text style={styles.title}>{t('Complete your profile')}</Text>
        <ScrollView
          contentContainerStyle={{paddingBottom: 80}}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="always">
          <View>
            <FormInput
              label={t('First Name')}
              placeholder={t('First Name')}
              placeholderTextColor={COLORS.placeHolder}
              onChangeText={formik.handleChange('firstName')}
              onBlur={() => formik.setFieldTouched('firstName')}
              value={formik.values.firstName}
            />
            <AppText
              error={formik.errors.firstName}
              visible={!!formik.touched.firstName}
            />
            <FormInput
              label={t('Surname')}
              placeholder={t('Surname')}
              placeholderTextColor={COLORS.placeHolder}
              onChangeText={formik.handleChange('surName')}
              onBlur={() => formik.setFieldTouched('surName')}
              value={formik.values.surName}
              containerStyle={styles.margin}
            />
            <AppText
              error={formik.errors.surName}
              visible={!!formik.touched.surName}
            />
            <FormInput
              label={t('Email Address')}
              placeholder={t('<EMAIL>')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              onChangeText={formik.handleChange('email')}
              onBlur={() => formik.setFieldTouched('email')}
              value={formik.values.email}
              editable={false}
            />
            <AppText
              error={formik.errors.email}
              visible={!!formik.touched.email}
            />
            <PhoneNumber
              label={t('Phone Number')}
              placeholder={t('Phone no.')}
              placeholderTextColor={COLORS.placeHolder}
              onChangeText={formik.handleChange('phoneNumber')}
              onBlur={() => formik.setFieldTouched('phoneNumber')}
              onCountryCodeChange={countryCode => {
                setCountryCode(countryCode);
              }}
            />
            <AppText
              error={formik.errors.phoneNumber}
              visible={!!formik.touched.phoneNumber}
            />

            <PlacesAutocomplete
              apiKey={Config.GOOGLE_MAPS_API_KEY}
              onPlaceSelected={(place, details) => {
                console.log('Selected place:', place);
                if (details) {
                  setLocationDetail(details);
                }
              }}
              fetchDetails={true}
              countryCode="be"
              textInputProps={{placeholder: t('Search')}}
              containerStyle={{marginTop: 10}}
              label={t('Search location')}
            />

            <FormInput
              label={t('City')}
              placeholder={t('City name')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              onChangeText={formik.handleChange('city')}
              onBlur={() => formik.setFieldTouched('city')}
              value={formik.values.city}
              editable={false}
            />
            <AppText
              error={formik.errors.city}
              visible={!!formik.touched.city}
            />
            <FormInput
              label={t('Country')}
              placeholder={t('Country name')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              onChangeText={formik.handleChange('country')}
              onBlur={() => formik.setFieldTouched('country')}
              value={formik.values.country}
              editable={false}
            />
            <AppText
              error={formik.errors.country}
              visible={!!formik.touched.country}
            />
            <DateofBirthPicker
              label={t('Date of Birth')}
              date={dateOfBirth}
              setDate={value => {
                formik.setFieldValue('dateOfBirth', value);
                formik.setFieldValue('dateOfBirth', value);
              }}
            />
            <AppText
              error={
                formik.errors.dateOfBirth
                  ? String(formik.errors.dateOfBirth)
                  : ''
              }
              visible={!!formik.touched.dateOfBirth}
            />
            <Text style={styles.label}>{t('ID card Image')}</Text>
            <View style={styles.idCardContainer}>
              {selectedIDFrontSide ? (
                <TouchableOpacity onPress={() => handleModal('idFrontSide')}>
                  <Image
                    source={{uri: selectedIDFrontSide}}
                    style={styles.image}
                  />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={styles.image}
                  onPress={() => handleModal('idFrontSide')}>
                  <Text style={styles.uploadTitle}>{t('ID Front')}</Text>
                  <Image source={upload} style={styles.uploadImage} />
                  <Text style={styles.uploadText}>{t('Upload')}</Text>
                </TouchableOpacity>
              )}
              {selectedIDBackSide ? (
                <TouchableOpacity onPress={() => handleModal('idBackSide')}>
                  <Image
                    source={{uri: selectedIDBackSide}}
                    style={styles.image}
                  />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={styles.image}
                  onPress={() => handleModal('idBackSide')}>
                  <Text style={styles.uploadTitle}>{t('ID Back')}</Text>
                  <Image source={upload} style={styles.uploadImage} />
                  <Text style={styles.uploadText}>{t('Upload')}</Text>
                </TouchableOpacity>
              )}
            </View>

            <AppText
              error={formik.errors.idBackSide}
              visible={!!formik.touched.idBackSide}
            />
            <AppText
              error={formik.errors.idFrontSide}
              visible={!!formik.touched.idFrontSide}
            />

            <AppText
              error={formik.errors.experience}
              visible={!!formik.touched.experience}
            />

            {/* <AboutMe
              label={t('About me')}
              placeholder={t('Type here...')}
              placeholderTextColor={COLORS.placeHolder}
              value={formik.values.about}
              onChangeText={formik.handleChange('about')}
              onBlur={() => formik.setFieldTouched('about')}
            />
            <AppText
              error={formik.errors.about}
              visible={!!formik.touched.about}
            /> */}
            <TextButton
              label={t('Continue')}
              onPress={formik.handleSubmit}
              containerStyle={{marginTop: 24}}
              disabled={
                isLoading ||
                !formik.isValid ||
                !formik.values.idFrontSide ||
                !formik.values.idBackSide ||
                !formik.values.dateOfBirth
              }
              isLoading={isLoading}
            />
          </View>
        </ScrollView>
        <ImageUploadModal
          isVisible={isModalVisible}
          onCameraPress={handleCameraImage}
          onGalleryPress={handlePickImage}
          onClose={() => setModalVisible(false)}
        />
      </View>
    </SafeAreaView>
  );
};

export default ProfileVerification;
