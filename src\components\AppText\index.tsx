import {StyleSheet, Text, View, TextStyle} from 'react-native';
import React from 'react';
import {FONTS} from '../../themes/fonts';

interface Props {
  error?: string;
  visible?: boolean;
  style?: TextStyle;
}
const AppText: React.FC<Props> = ({error, visible, style}) => {
  if (!visible || !error) return null;

  return (
    <View>
      <Text style={[styles.error, style]}>{error}</Text>
    </View>
  );
};

export default AppText;

const styles = StyleSheet.create({
  error: {
    marginTop: 2,
    color: 'red',
    fontFamily: FONTS.Medium,
  },
});
