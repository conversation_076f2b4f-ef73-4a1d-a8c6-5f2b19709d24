import React, {useContext, useEffect, useState} from 'react';
import {
  View,
  Text,
  Button,
  Alert,
  StyleSheet,
  SafeAreaView,
  Image,
  TouchableOpacity,
} from 'react-native';
import auth, {FirebaseAuthTypes} from '@react-native-firebase/auth';
import {COLORS, SIZES} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {useTranslation} from 'react-i18next';
import {TextButton} from '../../../components';
import {setUser} from '../../../slices/userSlice';
import {AuthContext} from '../../../../App';
import {useAppDispatch} from '../../../store/Store';
import firestore from '@react-native-firebase/firestore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import Toast from 'react-native-toast-message';
import {IUser} from '../../../interfaces';
import functions from '@react-native-firebase/functions';
import useAuth from '../../../hooks/useAuth';
import {EmailError, EmailSuccess, Reload} from '../../../assets/images';
import {verticalScale} from 'react-native-size-matters';
import {sendWelcomeEmail} from '../../../backend/send-welcome-email';

type Props = NativeStackScreenProps<AuthStackParamList, 'Login'>;

const VerifyEmailScreen: React.FC<Props> = ({navigation}) => {
  const [verified, setVerified] = useState(false);
  const {setUserId, isOwner} = useContext(AuthContext);
  const dispatch = useAppDispatch();
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const {onVerifiedLogin} = useAuth();

  const {t} = useTranslation();

  //   // Handle user state changes
  //   function handleAuthStateChanged(user: FirebaseAuthTypes.User | null) {
  //     // setUser(user);
  //     // if (initializing) setInitializing(false);
  //     setVerified(!!user?.emailVerified);
  //     if (user?.emailVerified) {
  //       onEmailVerified(user);
  //     }
  //   }

  //   useEffect(() => {
  //     const subscriber = auth().onAuthStateChanged(user => {
  //       console.log('User verified', user?.emailVerified);
  //     });
  //     return subscriber; // unsubscribe on unmount
  //   }, []);

  const checkVerification = async () => {
    try {
      setIsVerifying(true);
      const user = auth().currentUser;
      await user?.reload();
      console.log('User verified>>', user?.emailVerified);
      if (user?.emailVerified) {
        setVerified(true);
      } else {
        throw new Error('User not verified');
      }
    } catch (error) {
      console.error('Error in verification', error);
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('This user is not verified yet'),
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const resendEmail = async () => {
    try {
      setIsResending(true);
      await auth().currentUser?.sendEmailVerification();
      Toast.show({
        type: 'success',
        text1: t('Success'),
        text2: t('Verification email resent!'),
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Failed to send verification email. Try later...'),
      });
      console.error('Error in resending email', error);
    } finally {
      setIsResending(false);
    }
  };

  const onContinue = async () => {
    const user = auth().currentUser;
    if (!user) return;
    // This usecase will always appear for verified user
    // Send welcome email on user verification
    firestore()
      .collection('Users')
      .doc(user.uid)
      .get()
      .then(userDoc => {
        const userDetails = {...userDoc.data(), uid: user.uid} as IUser;
        sendWelcomeEmail({
          name: userDetails.firstName,
          email: userDetails.email,
        });
      });
    await onVerifiedLogin(user);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.innerContainer}>
        <View>
          {verified ? (
            <>
              <Image source={EmailSuccess} style={styles.img} />
              <Text style={styles.title}>{t('Email verified')}</Text>
              <Text style={{textAlign: 'center'}}>
                Email verified! You can now continue to the next steps of the
                app the app.
              </Text>
              <TextButton
                label={t('Continue')}
                isShadow
                onPress={onContinue}
                containerStyle={{marginTop: 16}}
                isLoading={isResending}
                disabled={isResending}
              />
            </>
          ) : (
            <>
              <Image source={EmailError} style={styles.img} />
              <Text style={styles.title}>{t('Verify your email')}</Text>
              <Text style={{textAlign: 'center'}}>
                We have sent you a verification a email. Please follow the link
                to verify your email address.
              </Text>

              <TouchableOpacity
                onPress={checkVerification}
                // isLoading={isVerifying}
                disabled={isVerifying}
                activeOpacity={0.7}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: 32,
                  alignSelf: 'center',
                  borderWidth: 1,
                  borderColor: COLORS.primary,
                  width: '100%',
                  justifyContent: 'center',
                  borderRadius: 8,
                  height: verticalScale(38),
                }}>
                <Image source={Reload} style={{width: 26, height: 26}} />
                <View>
                  <Text
                    style={{
                      fontSize: 16,
                      color: COLORS.primary,
                      marginLeft: 8,
                      fontWeight: 'bold',
                    }}>
                    {t('Check Verification')}
                  </Text>
                </View>
                {/* <TextButton
                  label={t('Check Verification')}
                  onPress={checkVerification}
                  containerStyle={{
                    marginTop: 24,
                    backgroundColor: 'transparent',
                  }}
                  labelStyle={{color: COLORS.primary}}
                  isLoading={isVerifying}
                  disabled={isVerifying}
                /> */}
              </TouchableOpacity>

              <TextButton
                label={t('Resend Email')}
                isShadow
                onPress={resendEmail}
                containerStyle={{marginTop: 16}}
                isLoading={isResending}
                disabled={isResending}
              />
            </>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default VerifyEmailScreen;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  innerContainer: {
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.black,
    paddingBottom: 16,
    textAlign: 'center',
  },
  img: {
    width: 64,
    height: 64,
    alignSelf: 'center',
    marginVertical: 24,
  },
});
