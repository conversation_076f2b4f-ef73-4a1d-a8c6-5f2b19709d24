import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {COLORS, SIZES} from '../../../themes/themes';
import {
  AppText,
  ButtonwithIcon,
  FormInput,
  TextButton,
} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import Message from './Message';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {ContactUsForm} from '../../../interfaces';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {verticalScale} from 'react-native-size-matters';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import {useAppSelector} from '../../../store/Store';
import functions from '@react-native-firebase/functions';

type Props = NativeStackScreenProps<PetOwnerParamList, 'ContactUs'>;

const ContactUs: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();
  const user = useAppSelector(state => state.user);
  const validationSchema = Yup.object().shape({
    firstName: Yup.string().required(t('Enter your first name')),
    surName: Yup.string().required(t('Enter your sur name')),
    email: Yup.string()
      .required(t('Enter your email'))
      .email(t('Email must be a valid email')),
    subject: Yup.string().required(t('Enter your subject')),
    message: Yup.string().required(t('Enter your message')),
  });

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          hitSlop={appStyles.hitSlop}
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  const handleSubmit = async () => {
    try {
      setIsLoading(true);

      const payload = {
        userId: user?.uid,
        firstName: user.firstName,
        surName: user.surName,
        email: user.email,
        subject: formik.values.subject,
        message: formik.values.message,
        timestamp: new Date(),
      };
      await functions()
        .httpsCallable('sendContactEmail')(payload)
        .then(() => {
          navigation.goBack();
          Toast.show({
            text1: t('Success'),
            text2: t('Our customer care will contact you shortly'),
            type: 'success',
          });
        });
    } catch (error) {
      console.log('Error saving to Firestore:', error);
      Toast.show({
        text1: t('Error'),
        text2: t('Sorry could not send email at this time'),
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<ContactUsForm>({
    initialValues: {
      firstName: user.firstName,
      surName: user.surName,
      email: user.email,
      subject: '',
      message: '',
    },
    onSubmit: handleSubmit,
    validateOnMount: true,
    validationSchema: validationSchema,
    enableReinitialize: true,
  });

  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS === 'android' ? 'height' : 'padding'}>
      <View style={styles.container}>
        <ScrollView
          contentContainerStyle={{paddingBottom: verticalScale(80)}}
          showsVerticalScrollIndicator={false}>
          <Text style={appStyles.title}>{t('Contact Us')}</Text>
          <View style={{marginTop: verticalScale(24)}}>
            <FormInput
              label={t('First Name')}
              placeholder={t('First Name')}
              placeholderTextColor={COLORS.placeHolder}
              onChangeText={formik.handleChange('firstName')}
              onBlur={() => formik.setFieldTouched('firstName')}
              value={user.firstName}
              editable={false}
            />
            <AppText
              error={formik.errors.firstName}
              visible={!!formik.touched.firstName}
            />
            <FormInput
              label={t('Surname')}
              placeholder={t('Surname')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              onChangeText={formik.handleChange('surName')}
              onBlur={() => formik.setFieldTouched('surName')}
              value={user.surName}
              editable={false}
            />
            <AppText
              error={formik.errors.surName}
              visible={!!formik.touched.surName}
            />

            <FormInput
              label={t('Email Address')}
              placeholder={t('<EMAIL>')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              onChangeText={formik.handleChange('email')}
              onBlur={() => formik.setFieldTouched('email')}
              keyboardType="email-address"
              editable={false}
              value={user.email}
            />
            <AppText
              error={formik.errors.email}
              visible={!!formik.touched.email}
            />
            <FormInput
              label={t('Subject')}
              placeholder={t('Subject')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              onChangeText={formik.handleChange('subject')}
              onBlur={() => formik.setFieldTouched('subject')}
            />
            <AppText
              error={formik.errors.subject}
              visible={!!formik.touched.subject}
            />
            <Message
              label={t('Type message')}
              placeholder={t('Type here...')}
              placeholderTextColor={COLORS.placeHolder}
              onChangeText={formik.handleChange('message')}
              onBlur={() => formik.setFieldTouched('message')}
              value={formik.values.message}
            />
            <AppText
              error={formik.errors.message}
              visible={!!formik.touched.message}
            />
          </View>
          <View style={styles.footer}>
            <TextButton
              label={t('Submit')}
              onPress={formik.handleSubmit}
              isLoading={isLoading}
              disabled={isLoading || !formik.isValid}
            />
          </View>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

export default ContactUs;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
  },
  margin: {
    marginTop: 14,
  },
  footer: {
    marginTop:
      SIZES.DeviceHeight > 760 ? verticalScale(100) : verticalScale(50),
  },
});
