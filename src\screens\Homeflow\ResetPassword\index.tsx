import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {
  AppText,
  ButtonwithIcon,
  FormInput,
  TextButton,
} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {useTogglePasswordVisibility} from '../../../hooks';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {IResetPassword} from '../../../interfaces';
import {AuthContext} from '../../../../App';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {FirebaseAuthTypes, firebase} from '@react-native-firebase/auth';
import Toast from 'react-native-toast-message';
import {PetSitterParamList} from '../../../navigation/PetSitterHomeStack';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<PetSitterParamList, 'ResetPassword'>;

const ResetPassword: React.FC<Props> = ({navigation}) => {
  const {userId} = useContext(AuthContext);
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();

  const validationSchema = Yup.object().shape({
    currentPassword: Yup.string().required(t('Current password is required')),
    password: Yup.string()
      .required(t('Password is required'))
      .matches(
        /^(?=.*[0-9])(?=.*[.!@#$%^&*])(?=.*[A-Z]).{8,}$/,
        t(
          'Password must contain at least 1 number, 1 special character, and 1 capital letter',
        ),
      ),
    confirmPassword: Yup.string()
      .label('Confirm password')
      .required(t('Confirm Password is required'))
      .oneOf([Yup.ref('password'), ''], t("Password doesn't match")),
  });

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);

  const {passwordVisibility, rightIcon, handlePasswordVisibility} =
    useTogglePasswordVisibility();

  const reauthenticate = (user: FirebaseAuthTypes.User) => {
    var cred = firebase.auth.EmailAuthProvider.credential(
      user.email || '',
      formik.values.currentPassword,
    );
    return user.reauthenticateWithCredential(cred);
  };

  const onSubmitHandler = () => {
    const user = firebase.auth().currentUser;

    if (user?.email) {
      setIsLoading(true);
      reauthenticate(user)
        .then(() => {
          user
            .updatePassword(formik.values.password)
            .then(() => {
              navigation.navigate('RecoveredPasswordFeedback');
            })
            .catch(error => {
              console.log('error', error.message);
            });
        })
        .catch(error => {
          console.log('error', error);

          Toast.show({
            type: 'error',
            text1: t('Error'),
            text2: t('Password could not be changed'),
            position: 'top',
            autoHide: true,
          });
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  };

  const formik = useFormik<IResetPassword>({
    enableReinitialize: true,
    initialValues: {
      currentPassword: '',
      password: '',
      confirmPassword: '',
    },
    onSubmit: onSubmitHandler,
    validateOnMount: true,
    validationSchema: validationSchema,
  });

  return (
    <KeyboardAvoidingView
      style={{flex: 1, backgroundColor: COLORS.white}}
      behavior={Platform.OS === 'ios' ? 'height' : undefined}>
      <View style={styles.container}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'space-between',
          }}>
          <View>
            <Text style={appStyles.title}>{t('Create New Password')}</Text>
            <Text style={[appStyles.paragraph, {marginTop: 16}]}>
              {t('Create a new password that is safer and easier to remember.')}
            </Text>
            <FormInput
              label={t('Current Password')}
              placeholder={t('Enter your current password')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              secureTextEntry={passwordVisibility}
              rightIcon={rightIcon}
              onRightIconPress={handlePasswordVisibility}
              isPassword
              onChangeText={formik.handleChange('currentPassword')}
              onBlur={() => formik.setFieldTouched('currentPassword')}
              onFocus={() => formik.setFieldTouched('currentPassword')}
            />
            <AppText
              error={formik.errors.currentPassword}
              visible={!!formik.touched.currentPassword}
            />
            <FormInput
              label={t('Password')}
              placeholder={t('Enter new Password')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              secureTextEntry={passwordVisibility}
              rightIcon={rightIcon}
              onRightIconPress={handlePasswordVisibility}
              isPassword
              onChangeText={formik.handleChange('password')}
              onBlur={() => formik.setFieldTouched('password')}
              onFocus={() => formik.setFieldTouched('password')}
            />
            <AppText
              error={formik.errors.password}
              visible={!!formik.touched.password}
            />
            <FormInput
              label={t('Confirm Password')}
              placeholder={t('Confirm new password')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              secureTextEntry={passwordVisibility}
              rightIcon={rightIcon}
              onRightIconPress={handlePasswordVisibility}
              isPassword
              onChangeText={formik.handleChange('confirmPassword')}
              onBlur={() => formik.setFieldTouched('confirmPassword')}
              onFocus={() => formik.setFieldTouched('confirmPassword')}
            />
            <AppText
              error={formik.errors.confirmPassword}
              visible={!!formik.touched.confirmPassword}
            />
          </View>

          <TextButton
            label={t('Recover Password')}
            isShadow
            onPress={formik.handleSubmit}
            isLoading={isLoading}
            disabled={isLoading || !formik.isValid}
            containerStyle={{marginVertical: 30}}
          />
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

export default ResetPassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  passwordInputFeild: {
    marginTop: 20,
  },
  margin: {
    marginTop: 14,
  },
});
