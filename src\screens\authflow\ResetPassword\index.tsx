import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect} from 'react';
import {COLORS} from '../../../themes/themes';
import {ButtonwithIcon, FormInput, TextButton} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {useTogglePasswordVisibility} from '../../../hooks';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import * as Yup from 'yup';
import {useFormik} from 'formik';
import {useTranslation} from 'react-i18next';
import functions from '@react-native-firebase/functions';

type Props = NativeStackScreenProps<AuthStackParamList, 'ResetPassword1'>;

const ResetPassword1: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {email} = route.params;
  const validationSchema = Yup.object().shape({
    password: Yup.string()
      .required(t('Password is required'))
      .matches(
        /^(?=.*[0-9])(?=.*[.!@#$%^&*])(?=.*[A-Z]).{8,}$/,
        t(
          'Password must contain at least 1 number, 1 special character, and 1 capital letter',
        ),
      ),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref('password'), ''], t('Passwords must match'))
      .required(t('Confirm Password is required')),
  });

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  const {passwordVisibility, rightIcon, handlePasswordVisibility} =
    useTogglePasswordVisibility();

  const handleChangePassword = async () => {
    try {
      const resp = await functions().httpsCallable('changeUserPassword')({
        userEmail: email.toLowerCase(),
        newPassword: formik.values.confirmPassword,
      });
      console.log(resp);

      if (resp.data) {
        navigation.navigate('PasswordSuccessFeedback');
      }
    } catch (error) {
      console.log('Error changing password', error);
    }
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      password: '',
      confirmPassword: '',
    },
    onSubmit: handleChangePassword,
    validateOnMount: true,
    validationSchema: validationSchema,
  });

  return (
    <KeyboardAvoidingView
      style={{flex: 1, backgroundColor: COLORS.white}}
      behavior={Platform.OS === 'ios' ? 'height' : undefined}>
      <View style={styles.container}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'space-between',
          }}>
          <View>
            <Text style={appStyles.title}>{t('Create New Password')}</Text>
            <Text style={[appStyles.paragraph, {marginTop: 16}]}>
              {t('Create a new password that is safer and easier to remember.')}
            </Text>
            <FormInput
              label={t('Password')}
              placeholder={t('Min. 8 characters')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.passwordInputFeild}
              secureTextEntry={passwordVisibility}
              rightIcon={rightIcon}
              onRightIconPress={handlePasswordVisibility}
              isPassword
              onChangeText={formik.handleChange('password')}
              onBlur={formik.handleBlur('password')}
              value={formik.values.password}
            />
            {formik.touched.password && formik.errors.password && (
              <Text style={styles.errorText}>{formik.errors.password}</Text>
            )}
            <FormInput
              label={t('Confirm Password')}
              placeholder={t('Confirm password')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              secureTextEntry={passwordVisibility}
              rightIcon={rightIcon}
              onRightIconPress={handlePasswordVisibility}
              isPassword
              onChangeText={formik.handleChange('confirmPassword')}
              onBlur={formik.handleBlur('confirmPassword')}
              value={formik.values.confirmPassword}
            />
            {formik.touched.confirmPassword &&
              formik.errors.confirmPassword && (
                <Text style={styles.errorText}>
                  {formik.errors.confirmPassword}
                </Text>
              )}
          </View>
          <TextButton
            label={t('Recover Password')}
            isShadow
            onPress={formik.handleSubmit}
            containerStyle={{marginVertical: 32}}
          />
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

export default ResetPassword1;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingBottom: Platform.OS === 'ios' ? 80 : 0,
  },
  passwordInputFeild: {
    marginTop: 20,
  },
  margin: {
    marginTop: 14,
  },
  errorText: {
    color: 'red',
  },
});
