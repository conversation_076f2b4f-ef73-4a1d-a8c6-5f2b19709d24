import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {verticalScale} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 24,
  },
  notificationCard: {
    paddingTop: 20,
    paddingBottom: 60,
  },
  emptyMessage: {
    fontFamily: FONTS.SemiBold,
    fontSize: 14,
    color: COLORS.Philippine_Gray,
    textAlign: 'center',
    marginTop: 30,
  },
  dateText: {
    fontFamily: FONTS.SemiBold,
    fontSize: 14,
    color: '#36363654',
    marginVertical: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginTop: 20,
  },
  rejectButton: {
    borderColor: COLORS.primary,
    borderWidth: 1,
    backgroundColor: '#FFF',
    width: '47%',
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    alignItems: 'center',
    paddingVertical: verticalScale(26),
    paddingHorizontal: 14,
  },
  modalDesc: {fontFamily: FONTS.SemiBold, fontSize: 15},
});
