import {Image, Text, TouchableOpacity, View} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import {
  ButtonwithIcon,
  ImageUploadModal,
  TextButton,
} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import {ImagePlaceholder} from '../../../assets/images';
import appStyles from '../../../themes/appStyles';
import ImagePicker from 'react-native-image-crop-picker';
import {verticalScale} from 'react-native-size-matters';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {AuthContext} from '../../../../App';
import storage from '@react-native-firebase/storage';
import firestore from '@react-native-firebase/firestore';
import {useDispatch} from 'react-redux';
import {setUser} from '../../../slices/userSlice';
import {useAppSelector} from '../../../store/Store';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';

type Props = NativeStackScreenProps<AuthStackParamList, 'UploadProfile'>;

const UploadProfile: React.FC<Props> = ({navigation, route}) => {
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [isModalVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const {isOwner} = useContext(AuthContext);
  const dispatch = useDispatch();
  const user = useAppSelector(state => state.user);
  const {uid} = route.params;
  const {t} = useTranslation();

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  const handleCameraImage = () => {
    ImagePicker.openCamera({
      width: 300,
      height: 400,
      cropping: false,
      compressImageQuality: 0.25,
    }).then(image => {
      setModalVisible(false);
      setSelectedImage(image.path);
      handleUpload();
    });
  };
  const handleGalleryImage = () => {
    ImagePicker.openPicker({
      width: 300,
      height: 400,
      cropping: false,
      maxFiles: 1,
      mediaType: 'photo',
      compressImageQuality: 0.25,
    }).then(image => {
      setModalVisible(false);
      setSelectedImage(image.path);
      handleUpload();
    });
  };

  const handleUpload = async () => {
    const reference = storage().ref('profilePicture/' + `${uid}_img`);
    const pathToFile = selectedImage && selectedImage;
    pathToFile && (await reference.putFile(pathToFile));
    const url = await storage()
      .ref('profilePicture/' + `${uid}_img`)
      .getDownloadURL();
    return url;
  };

  const handleContinue = async () => {
    try {
      setIsLoading(true);
      const profilePictureURL = await handleUpload();
      console.log(profilePictureURL);

      await firestore()
        .collection('Users')
        .doc(uid)
        .update({profilePicture: profilePictureURL})
        .then(() => {
          dispatch(
            setUser({
              ...user,
              profilePicture: profilePictureURL || '',
              uid: uid,
            }),
          );
          isOwner
            ? navigation.navigate('PetProfile', {isAuthFlow: true, uid: uid})
            : navigation.navigate('PetSitterAccountFeedback');
        });
    } catch (error) {
      console.log('Error uploading profile picture:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={{alignItems: 'center'}}>
        <Image
          source={selectedImage ? {uri: selectedImage} : ImagePlaceholder}
          style={styles.imageContainer}
        />
        <Text style={[appStyles.title, {paddingTop: verticalScale(40)}]}>
          {t('Profile Picture')}
        </Text>
        <Text style={[appStyles.paragraph, {paddingTop: verticalScale(12)}]}>
          {t('Upload your profile picture here')}
        </Text>
        <TouchableOpacity
          onPress={() => setModalVisible(true)}
          hitSlop={{left: 20, right: 20, bottom: 20, top: 20}}>
          <Text style={styles.buttonTitle}>
            {selectedImage ? t('Change') : t('Upload')}
          </Text>
        </TouchableOpacity>
      </View>
      <TextButton
        disabled={!selectedImage || isLoading}
        isLoading={isLoading}
        label={t('Continue')}
        onPress={handleContinue}
      />
      <ImageUploadModal
        isVisible={isModalVisible}
        onCameraPress={handleCameraImage}
        onGalleryPress={handleGalleryImage}
        onClose={() => setModalVisible(false)}
      />
    </View>
  );
};

export default UploadProfile;
