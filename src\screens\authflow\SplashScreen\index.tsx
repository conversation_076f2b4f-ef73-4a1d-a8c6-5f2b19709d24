import React, {useEffect, useRef} from 'react';
import {ImageBackground} from 'react-native';
import <PERSON>tieView from 'lottie-react-native';
import {styles} from './styles';

const Splash = (): JSX.Element => {
  const animationRef = useRef<LottieView>(null);

  useEffect(() => {
    animationRef.current?.play();

    animationRef.current?.play(30, 120);
  }, []);

  return (
    <ImageBackground
      source={require('../../../assets/images/SplashBG.png')}
      style={styles.container}>
      <LottieView
        style={styles.animation}
        ref={animationRef}
        source={require('../../../assets/Animations/footPrints.json')}
        autoPlay
        loop
      />
    </ImageBackground>
  );
};

export default Splash;
