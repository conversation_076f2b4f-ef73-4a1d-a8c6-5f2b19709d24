import {ActivityIndicator, StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {ButtonwithIcon} from '../../../components';
import {Edit} from '../../../assets/svgIcons';
import {verticalScale} from 'react-native-size-matters';
import FastImage from 'react-native-fast-image';
import {useAppSelector} from '../../../store/Store';
import {ImagePlaceholder, placeholderImage} from '../../../assets/images';
import {useTranslation} from 'react-i18next';

interface headerProps {
  onEditPress: () => void;
}

const Header: React.FC<headerProps> = ({onEditPress}) => {
  const [loading, setLoading] = useState(true);
  const {t} = useTranslation();
  const user = useAppSelector(state => state.user);
  const onImageLoad = () => {
    setLoading(false);
  };
  const profileStatus =
    user.profileStatus == 'approved' ? 'Approved' : 'In Review';
  const statusColor = user.profileStatus == 'approved' ? '#04BA71' : '#F29339';

  return (
    <View style={styles.container}>
      {/* {loading && (
        <ActivityIndicator
          size="small"
          color={COLORS.primary}
          style={styles.indicator}
        />
      )} */}
      <FastImage
        source={
          user.profilePicture ? {uri: user.profilePicture} : ImagePlaceholder
        }
        style={styles.imageContainer}
      />
      <View style={styles.innerContainer}>
        <View>
          <Text numberOfLines={2} style={styles.title}>
            {user.firstName} {user.surName}
          </Text>
          <Text style={styles.emailText}>{user.email}</Text>
          <View
            style={[
              styles.statusContainer,
              {
                backgroundColor: statusColor,
              },
            ]}>
            <Text style={styles.statusText}>{t(profileStatus)}</Text>
          </View>
        </View>
        <ButtonwithIcon icon={<Edit />} onPress={onEditPress} />
      </View>
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    paddingTop: 24,
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageContainer: {
    width: verticalScale(73),
    height: verticalScale(73),
    borderRadius: verticalScale(73) / 2,
    marginRight: 8,
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  title: {
    fontSize: verticalScale(24),
    color: COLORS.Bastille,
    fontFamily: FONTS.Bold,
  },
  emailText: {
    fontSize: 12,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
    marginTop: 4,
  },
  indicator: {position: 'absolute', left: 30},
  statusText: {
    fontFamily: FONTS.SemiBold,
    color: COLORS.whiteSmoke,
    fontSize: 12,
  },
  statusContainer: {
    paddingVertical: 4,
    paddingHorizontal: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    marginTop: 4,
    alignSelf: 'flex-start',
  },
});
