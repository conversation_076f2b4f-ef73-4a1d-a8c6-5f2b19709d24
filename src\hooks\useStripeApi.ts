import functions from '@react-native-firebase/functions';
import firestore from '@react-native-firebase/firestore';

import {PaymentResult, PaymentPayload} from '../interfaces/Payments';
import {useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAppDispatch, useAppSelector} from '../store/Store';
import {updateUser} from '..//slices/userSlice';

export default () => {
  const [processing, setProcessing] = useState(false);
  const user = useAppSelector(state => state.user);
  const dispatch = useAppDispatch();

  const createCustomerOnStripe = async (
    uid: string,
    email: string,
    name: string,
    phone = '',
  ) => {
    try {
      const res = await functions().httpsCallable('createCustomer')({
        email,
        name,
        phone,
      });

      const customer = res.data;

      if (customer?.id) {
        firestore().collection('Users').doc(uid).update({
          customerId: customer.id,
        });
        dispatch(updateUser({customerId: customer.id}));
      }
    } catch (error) {
      console.log('Error while creating user on stripe:', error);
    }
  };

  const processPayment = async (body: PaymentPayload) => {
    setProcessing(true);
    const res: PaymentResult = await functions().httpsCallable('createPayment')(
      body,
    );
    setProcessing(false);
    return res;
  };

  return {
    createCustomerOnStripe,
    processPayment,
  };
};
