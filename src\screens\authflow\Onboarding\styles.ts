import {Dimensions, StyleSheet} from 'react-native';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF',
  },
  skipText: {
    fontSize: scale(16),
    color: '#FF8C0E',
    fontFamily: FONTS.SemiBold,
  },
  innerContainer: {},
  imageContainer: {
    width: '100%',
    height:
      Dimensions.get('screen').height > 760
        ? verticalScale(365)
        : verticalScale(320),
  },
  label: {
    fontSize: moderateScale(18),
    color: '#FF8C0E',
    fontFamily: FONTS.SemiBold,
    paddingTop: verticalScale(24),
    textAlign: 'center',
    paddingHorizontal: scale(50),
  },
  paragraph: {
    fontSize: moderateScale(14),
    color: '#2C2C2E',
    paddingTop: verticalScale(12),
    paddingHorizontal: scale(20),
    textAlign: 'center',
    fontFamily: FONTS.Medium,
    // height: 120,
  },
  activeDot: {
    width: 14,
    height: 14,
    backgroundColor: '#FF8C0E',
    borderRadius: 14 / 2,
  },
  inActiveDot: {
    width: 10,
    height: 10,
    backgroundColor: '#E4E4E4',
    borderRadius: 10 / 2,
    marginLeft: 6,
  },
  dotContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: verticalScale(16),
  },
  buttonContainer: {},
  buttonWrapper: {
    flexDirection: 'row',
    marginBottom:
      Dimensions.get('screen').height > 760
        ? verticalScale(40)
        : verticalScale(20),
    marginHorizontal: scale(24),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonLabel: {
    fontSize: 16,
    color: '#252D3E',
    fontFamily: FONTS.SemiBold,
    marginRight: 8,
  },
  iconContainer: {
    width: 24,
    height: 24,
  },
  nextButton: {
    backgroundColor: '#FF8C0E',
    height: 46,
    width: 46,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  startedButton: {},
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF',
  },
  skipText: {
    fontSize: scale(16),
    color: '#FF8C0E',
    fontFamily: FONTS.SemiBold,
  },
  innerContainer: {},
  imageContainer: {
    width: '100%',
    height:
      Dimensions.get('screen').height > 760
        ? verticalScale(375)
        : verticalScale(330),
  },
  label: {
    fontSize: moderateScale(18),
    color: '#FF8C0E',
    fontFamily: FONTS.SemiBold,
    paddingTop: verticalScale(30),
    textAlign: 'center',
    paddingHorizontal: scale(50),
  },
  paragraph: {
    fontSize: moderateScale(14),
    color: '#2C2C2E',
    paddingTop: verticalScale(14),
    paddingHorizontal: scale(20),
    textAlign: 'center',
    fontFamily: FONTS.Medium,
  },
  activeDot: {
    width: 14,
    height: 14,
    backgroundColor: '#FF8C0E',
    borderRadius: 14 / 2,
  },
  inActiveDot: {
    width: 10,
    height: 10,
    backgroundColor: '#E4E4E4',
    borderRadius: 10 / 2,
    marginLeft: 6,
  },
  dotContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: verticalScale(30),
  },
  buttonContainer: {},
  buttonWrapper: {
    flexDirection: 'row',
    marginBottom:
      Dimensions.get('screen').height > 760
        ? verticalScale(40)
        : verticalScale(20),
    marginHorizontal: scale(24),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonLabel: {
    fontSize: 16,
    color: '#252D3E',
    fontFamily: FONTS.SemiBold,
    marginRight: 8,
  },
  iconContainer: {
    width: 24,
    height: 24,
  },
  nextButton: {
    backgroundColor: '#FF8C0E',
    height: 46,
    width: 46,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  startedButton: {},
});
