import {Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {COLORS} from '../../../themes/themes';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';

interface Props {
  onSignInPress?: () => void;
  onSignUpPress?: () => void;
  isLogin: boolean;
}

const LoginSignUpButtons: React.FC<Props> = ({
  onSignInPress,
  onSignUpPress,
  isLogin,
}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <TouchableOpacity
        disabled={isLogin}
        activeOpacity={0.5}
        style={[
          styles.loginButton,
          {
            backgroundColor: isLogin ? COLORS.white : COLORS.whiteSmoke,
          },
        ]}
        onPress={onSignInPress}>
        <Text
          style={[
            styles.label,
            {color: isLogin ? COLORS.primary : COLORS.darkGray},
          ]}>
          {t('Sign in')}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        disabled={!isLogin}
        activeOpacity={0.5}
        style={[
          styles.loginButton,
          {
            backgroundColor: !isLogin ? COLORS.white : COLORS.whiteSmoke,
          },
        ]}
        onPress={onSignUpPress}>
        <Text
          style={[
            styles.label,
            {
              color: !isLogin ? COLORS.primary : COLORS.darkGray,
            },
          ]}>
          {t('Sign up')}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default LoginSignUpButtons;
