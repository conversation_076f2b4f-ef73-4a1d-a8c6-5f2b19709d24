import React, {useContext, useEffect, useRef, useState} from 'react';
import {
  NavigationContainer,
  useNavigationContainerRef,
  useNavigationState,
} from '@react-navigation/native';
import {AuthContext} from '../../App';
import PetSitterHomeStackScreen from './PetSitterHomeStack';
import PetOwnerHomeStack from './PetOwnerHomeStack';
import AuthStackNavigator from './authStack';
import {NotificationHandlerHOC} from '../helpers/pushNotification';
import database from '@react-native-firebase/database';
import auth from '@react-native-firebase/auth';
import {Image, StyleSheet, Text, View} from 'react-native';
import {FONTS} from '../themes/fonts';
import {verticalScale} from 'react-native-size-matters';
import {COLORS} from '../themes/themes';
import Modal from 'react-native-modal';
import messaging from '@react-native-firebase/messaging';
import useChat from '../hooks/useChat';
import {logo} from '../assets/images';
import {createNativeStackNavigator} from '@react-navigation/native-stack';

interface INotificationProps {
  title: string;
  description: string;
}

const Navigation = () => {
  const {userId, setOnlineStatus} = useContext(AuthContext);
  const [isModalVisible, setModalVisible] = useState(false);
  const [notificationData, setNotificationData] =
    useState<INotificationProps>();

  const navigationRef = useNavigationContainerRef();
  const routeNameRef = useRef<string | undefined>();

  useEffect(() => {
    const currentUser = auth().currentUser;
    if (currentUser) {
      const userId = currentUser.uid;
      database()
        .ref(`/online/`)
        .on('value', snapshot => {
          const onlineStatusData = snapshot.val();
          setOnlineStatus(onlineStatusData);
        });

      const reference = database().ref(`/online/${userId}`);
      reference.set(true).then(() => console.log('Online presence set'));
      reference
        .onDisconnect()
        .remove()
        .then(() => console.log('On disconnect function configured.'));
    }
  }, [userId]);

  useEffect(() => {
    messaging().onMessage(async remoteMessage => {
      const currentRoute = navigationRef.getCurrentRoute()?.name;
      if (currentRoute !== 'ChatDetail') {
        setModalVisible(true);
        setNotificationData({
          title: remoteMessage?.notification?.body || '',
          description: remoteMessage.notification?.title || '',
        });
      }
    });
  }, []);

  const handleModalBackdrop = () => {
    setModalVisible(false);
    setNotificationData(undefined);
  };

  useEffect(() => {
    if (isModalVisible) {
      setTimeout(() => {
        setModalVisible(false);
        setNotificationData(undefined);
      }, 2000);
    }
  }, [isModalVisible]);

  return (
    <NavigationContainer
      ref={navigationRef}
      onReady={() => {
        routeNameRef.current = navigationRef.getCurrentRoute()?.name;
      }}
      onStateChange={async () => {
        routeNameRef.current = navigationRef.getCurrentRoute()?.name;
      }}>
      {userId === 'petOwner' ? (
        <PetOwnerHomeStack />
      ) : userId === 'petSitter' ? (
        <PetSitterHomeStackScreen />
      ) : (
        <AuthStackNavigator />
      )}
      <Modal isVisible={isModalVisible} onBackdropPress={handleModalBackdrop}>
        <View style={styles.notificationContainer}>
          <Image source={logo} style={styles.logo} />
          <View style={{marginLeft: 10}}>
            <Text style={styles.title}>{notificationData?.description}</Text>
            <Text style={styles.description} numberOfLines={1}>
              {notificationData?.title}
            </Text>
          </View>
        </View>
      </Modal>
    </NavigationContainer>
  );
};

export default NotificationHandlerHOC(Navigation);

const styles = StyleSheet.create({
  notificationContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    alignItems: 'center',
    paddingVertical: verticalScale(8),
    paddingHorizontal: 8,
    flexDirection: 'row',
    position: 'absolute',
    top: 40,
    width: '100%',
    alignSelf: 'center',
  },
  description: {
    fontFamily: FONTS.Medium,
    fontSize: 12,
    color: COLORS.Philippine_Gray,
    marginTop: 5,
    width: '90%',
  },
  title: {
    fontFamily: FONTS.SemiBold,
    fontSize: 15,
    color: COLORS.Philippine_Gray,
  },
  logo: {height: 40, width: 40},
});

function HomeScreen() {
  return (
    <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
      <Text>Home Screen</Text>
    </View>
  );
}

const Stack = createNativeStackNavigator();

function RootStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Home" component={HomeScreen} />
    </Stack.Navigator>
  );
}
