export interface ILoginForm {
  email: string;
  password: string;
}
export interface ISignUpForm {
  firstName: string;
  surName: string;
  email: string;
  password: string;
  confirmPassword: string;
  termofUse: boolean;
  privacyPolicy: boolean;
}

export interface IProfileVerification {
  firstName: string;
  surName: string;
  email: string;
  phoneNumber: string;
  address: string;
  city: string;
  country: string;
  experience: number;
  about: string;
  dateOfBirth: Date;
  idFrontSide: string;
  idBackSide: string;
}

export interface ContactUsForm {
  firstName: string;
  surName: string;
  email: string;
  subject?: string;
  message: string;
  deleteCheck?: boolean;
}

export interface IResetPassword {
  currentPassword: string;
  password: string;
  confirmPassword: string;
}
