import {FlatList, Text, View} from 'react-native';
import React from 'react';
import appStyles from '../../../themes/appStyles';
import {styles} from './styles';
import {useTranslation} from 'react-i18next';

interface ServicesProps {
  services: string[];
  price?: string;
}

const Services: React.FC<ServicesProps> = ({services, price}) => {
  const {t} = useTranslation();

  const renderServiceItem = ({item, index}: {item: string; index: number}) => (
    <View>
      <View style={styles.serviceStyle}>
        <Text style={styles.servicesText}>{item}</Text>
      </View>
      {index < services.length - 1 && <View style={styles.serviceSeparator} />}
    </View>
  );

  return (
    <View style={styles.serviceContainer}>
      <Text style={appStyles.title2}>{t('Services')}</Text>
      {!services.length ? (
        <Text style={styles.servicesText}>{t('No services added')}</Text>
      ) : (
        <FlatList
          data={services}
          renderItem={renderServiceItem}
          keyExtractor={(item, index) => index.toString()}
        />
      )}
      {price && (
        <Text style={[styles.servicesText, {paddingTop: 20}]}>
          {t('Total:')} {price} €
        </Text>
      )}
    </View>
  );
};

export default Services;
