import {
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
  ActivityIndicator,
} from 'react-native';
import React, {useContext, useState} from 'react';
import TextButtonwithIcon from '../Buttons/TextButtonwithIcon';
import {Location, Star} from '../../assets/svgIcons';
import {COLORS} from '../../themes/themes';
import appStyles from '../../themes/appStyles';
import {AuthContext} from '../../../App';
import FastImage from 'react-native-fast-image';
import {placeholder, placeholderImage} from '../../assets/images';
import {styles} from './styles';
import {useTranslation} from 'react-i18next';

interface Props {
  image?: string;
  name: string;
  location?: string;
  onPress: () => void;
  containerStyle?: ViewStyle;
  experience?: number;
  rating?: number;
  petOwnerPicture?: string;
  OwnerName?: string;
}

const PetCard: React.FC<Props> = ({
  image,
  name,
  onPress,
  containerStyle,
  location,
  experience,
  rating,
  petOwnerPicture,
  OwnerName,
}) => {
  const [isImageLoading, setImageLoading] = useState(true);
  const {userId} = useContext(AuthContext);
  const {t} = useTranslation();

  return (
    <TouchableOpacity
      style={[styles.container, containerStyle]}
      onPress={onPress}>
      <FastImage
        source={image ? {uri: image} : placeholder}
        style={styles.imageContainer}
        onLoad={() => {
          setImageLoading(false);
        }}
        onLoadEnd={() => {
          setImageLoading(false);
        }}
      />
      {isImageLoading ? (
        <ActivityIndicator
          size={'small'}
          color={COLORS.primary}
          style={[
            styles.activityIndicator,
            {left: userId == 'petOwner' ? 70 : 150},
          ]}
        />
      ) : null}
      <View style={styles.innerContainer}>
        <View style={styles.infoContainer}>
          <Text style={[appStyles.title2, styles.name]}>{name}</Text>
          <View style={styles.locationContainer}>
            <Location />
            <Text style={styles.label2}>{location}</Text>
          </View>
          {experience ? (
            <View style={styles.locationContainer}>
              <Location />
              <Text style={styles.label2}>
                {experience} {t('yr')}
              </Text>
            </View>
          ) : null}
        </View>
        <View style={{flexDirection: 'row'}}>
          {rating ? (
            <View style={{marginRight: 10, marginTop: 20}}>
              <TextButtonwithIcon
                label={rating.toString()}
                labelStyle={styles.label}
                leftIcon={<Star />}
                disabled
              />
            </View>
          ) : null}
          {OwnerName ? (
            <View style={styles.ownerContainer}>
              <FastImage
                source={
                  petOwnerPicture ? {uri: petOwnerPicture} : placeholderImage
                }
                style={styles.ownerImage}
                defaultSource={placeholderImage}
              />
              <Text style={[appStyles.title2, styles.name, styles.ownerName]}>
                {OwnerName}
              </Text>
            </View>
          ) : null}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default PetCard;
