import axios from 'axios';

interface WelcomeEmailBody {
  name: string;
  email: string;
}

export const sendWelcomeEmail = (payload: WelcomeEmailBody) => {
  let config = {
    method: 'post',
    maxBodyLength: Infinity,
    // url: 'https://us-central1-petsit-staging.cloudfunctions.net/sendWelcomeEmail',
    url: 'https://us-central1-pet-sit-91da5.cloudfunctions.net/sendWelcomeEmail',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify(payload),
  };

  axios
    .request(config)
    .then(response => {
      console.log(JSON.stringify(response.data));
    })
    .catch(error => {
      console.log(error);
    });
};
