import {bookingNotification, world} from '../assets/images';
import {
  Bucket,
  Contact,
  Lock,
  Pet,
  Privacy,
  Request,
  Review,
  Term,
  signOut,
} from '../assets/images/SettingsAction';

export const PETOWNERSETTINGACTION = [
  {
    id: 1,
    label: 'My Pets',
    icon: Pet,
    onPress: 'MyPets',
  },
  {
    id: 2,
    label: 'Change Language',
    icon: world,
    onPress: 'SelectLanguage1',
  },
  {
    id: 3,
    label: 'My Reviews',
    icon: Review,
    onPress: 'MyReviews',
  },
  {
    id: 4,
    label: 'Booking Requests',
    icon: Request,
    onPress: 'RequestScreen',
  },
  {
    id: 5,
    label: 'Change Password',
    icon: Lock,
    onPress: 'ResetPassword',
  },
  {
    id: 6,
    label: 'Document Verification',
    icon: Lock,
    onPress: 'IdVerification',
  },
  {
    id: 7,
    label: 'Contact Us',
    icon: Contact,
    onPress: 'ContactUs',
  },
  {
    id: 8,
    label: 'FAQ',
    icon: Request,
    onPress: 'FAQ',
  },
  {
    id: 9,
    label: 'Terms of use',
    icon: Term,
    onPress: 'TermofUse',
  },
  {
    id: 10,
    label: 'Privacy policy',
    icon: Privacy,
    onPress: 'PrivacyPolicy',
  },
  {
    id: 11,
    label: 'Delete Account',
    icon: Bucket,
    onPress: 'DeleteAccount',
  },
];
export const PETSITTERSETTINGACTION = [
  // {
  //   id: 1,
  //   label: 'Bookings',
  //   icon: Request,
  //   onPress: 'RequestScreen',
  // },
  {
    id: 2,
    label: 'Change Language',
    icon: world,
    onPress: 'SelectLanguage1',
  },

  {
    id: 3,
    label: 'Booking Requests',
    icon: Request,
    onPress: 'RequestScreen',
  },
  // {
  //   id: 4,
  //   label: 'Setup Payment Profile',
  //   icon: Review,
  //   onPress: 'SellerProfile',
  // },

  {
    id: 5,
    label: 'Change Password',
    icon: Lock,
    onPress: 'ResetPassword',
  },
  {
    id: 6,
    label: 'Document Verification',
    icon: Lock,
    onPress: 'IdVerification',
  },
  {
    id: 7,
    label: 'Contact Us',
    icon: Contact,
    onPress: 'ContactUs',
  },
  {
    id: 8,
    label: 'FAQ',
    icon: Request,
    onPress: 'FAQ',
  },
  {
    id: 9,
    label: 'Terms of use',
    icon: Term,
    onPress: 'TermofUse',
  },

  {
    id: 10,
    label: 'Privacy policy',
    icon: Privacy,
    onPress: 'PrivacyPolicy',
  },
  {
    id: 11,
    label: 'Delete Account',
    icon: Bucket,
    onPress: 'DeleteAccount',
  },
];
