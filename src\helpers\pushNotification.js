import messaging from '@react-native-firebase/messaging';
import {useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const NotificationHandlerHOC = RootNavigator => {
  const MessageHandlerComponent = () => {
    const getFCMToken = async () => {
      try {
        const fcmToken = await messaging().getToken();
        console.log('FCM Token:', fcmToken);

        if (fcmToken) {
          try {
            await AsyncStorage.setItem('fcmToken', fcmToken);

            console.log('FCM token saved to Firestore for the current user.');
          } catch (error) {
            console.log('Error saving FCM token to Firestore:', error);
            throw error;
          }
        }
      } catch (error) {
        console.log('Error in fetching fcmtoken', error);
      }
    };
    const notificationListener = async () => {
      messaging().onNotificationOpenedApp(remoteMessage => {
        console.log(
          'Notification caused app to open from background state:',
          remoteMessage.data,
        );
      });
      messaging()
        .getInitialNotification()
        .then(remoteMessage => {
          if (remoteMessage) {
            console.log(
              'Notification caused app to open from quit state:',
              remoteMessage.notification,
            );
          }
        });
      messaging().onMessage(async remoteMessage => {
        console.log('notification on foreground state....', remoteMessage);
      });
    };
    const requestUserPermission = async () => {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      if (enabled) {
        getFCMToken();
      }
    };

    useEffect(() => {
      requestUserPermission();
      notificationListener();
    }, []);
    return <RootNavigator />;
  };
  return MessageHandlerComponent;
};
