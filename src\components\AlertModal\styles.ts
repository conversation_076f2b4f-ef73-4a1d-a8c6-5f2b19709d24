import {StyleSheet} from 'react-native';
import {COLORS} from '../../themes/themes';
import {FONTS} from '../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    paddingHorizontal: 10,
    paddingVertical: 20,
    borderRadius: 10,
  },
  title: {
    fontSize: 16,
    color: '#EB1212',
    fontFamily: FONTS.Bold,
    textAlign: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginVertical: 16,
  },
  paragraph: {
    fontSize: 14,
    fontFamily: FONTS.SemiBold,
    color: COLORS.Bastille,
    textAlign: 'center',
  },
  modalButtonContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 18,
  },
  modalButton: {
    width: '48%',
  },
});
