import React from 'react';
import {useTranslation} from 'react-i18next';
import {View, Text, TouchableOpacity, Image, StyleSheet} from 'react-native';
import Modal from 'react-native-modal';
import {COLORS} from '../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../themes/fonts';
import {Camera, gallary} from '../../assets/images';

interface ImageUploadModalProps {
  isVisible: boolean;
  onClose: () => void;
  onCameraPress: () => void;
  onGalleryPress: () => void;
}

const ImageUploadModal: React.FC<ImageUploadModalProps> = ({
  isVisible,
  onClose,
  onCameraPress,
  onGalleryPress,
}) => {
  const {t} = useTranslation();

  return (
    <Modal isVisible={isVisible} onBackdropPress={onClose}>
      <View style={styles.outerButtonContainer}>
        <Text style={[styles.title, {alignSelf: 'center'}]}>{t('Upload')}</Text>
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.innerButtonContainer}
            onPress={onCameraPress}>
            <Image source={Camera} style={{width: 24, height: 24}} />
            <Text style={styles.text}>{t('Camera')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.innerButtonContainer}
            onPress={onGalleryPress}>
            <Image source={gallary} style={{width: 24, height: 24}} />
            <Text style={styles.text}>{t('Gallery')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  outerButtonContainer: {
    backgroundColor: COLORS.white,
    padding: 12,
    borderRadius: 16,
    width: '80%',
    alignSelf: 'center',
  },

  button: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    backgroundColor: COLORS.white,
    borderRadius: 40,
  },
  buttonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: verticalScale(12),
    justifyContent: 'space-evenly',
    paddingHorizontal: verticalScale(12),
  },
  innerButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: 12,
    fontFamily: FONTS.Medium,
    color: COLORS.black,
    marginTop: verticalScale(8),
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.black,
    paddingBottom: 16,
  },
});

export default ImageUploadModal;
