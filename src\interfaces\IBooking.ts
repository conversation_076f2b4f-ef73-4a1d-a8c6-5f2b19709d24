import {IPet} from './IPet';
import {IUser} from './IUser';

export interface IBooking {
  bookingDate: {
    endDate: string;
    startDate: string;
  };
  key: string;
  petData: IPet;
  price: string;
  services: string[];
  userData: IUser;
  status: 'accepted' | 'pending' | 'done';
  id: string;
  userId: string;
}

export interface IConversationBooking {
  bookingDate: {
    endDate: string;
    startDate: string;
  };
  price: string;
  services: string[];
  id: string;
}
