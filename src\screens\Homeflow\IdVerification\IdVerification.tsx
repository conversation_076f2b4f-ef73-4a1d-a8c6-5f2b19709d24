import React, {useContext, useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import ImagePicker from 'react-native-image-crop-picker';
import storage from '@react-native-firebase/storage';
import firestore from '@react-native-firebase/firestore';
import * as geofirestore from 'geofirestore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Toast from 'react-native-toast-message';
import {useDispatch} from 'react-redux';
import {useTranslation} from 'react-i18next';

import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {
  AlertModal,
  AppText,
  ImageUploadModal,
  TextButton,
} from '../../../components';
import {upload} from '../../../assets/images';
import {AuthContext} from '../../../../App';
import {setUser} from '../../../slices/userSlice';
import {useAppSelector} from '../../../store/Store';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {PetSitterParamList} from '../../../navigation/PetSitterHomeStack';
import {verticalScale} from 'react-native-size-matters';

const WIDTH = Dimensions.get('window').width;

interface IdVerificationForm {
  idFrontSide: string;
  idBackSide: string;
}

type Props = NativeStackScreenProps<PetSitterParamList, 'IdVerification'>;

const IdVerification: React.FC<Props> = ({navigation}) => {
  // const {setUserId} = useContext(AuthContext);

  const dispatch = useDispatch();
  const {t} = useTranslation();

  const {idBackSide, idFrontSide, uid} = useAppSelector(state => state.user);
  console.log('UID', uid);

  const [isModalVisible, setModalVisible] = useState(false);
  const [isConfirmationVisible, setConfirmationVisible] = useState(false);
  const [selectedField, setSelectedField] = useState<
    keyof IdVerificationForm | ''
  >('');
  const [images, setImages] = useState<IdVerificationForm>({
    idFrontSide: idFrontSide || '',
    idBackSide: idBackSide || '',
  });

  const [haveChanges, setHaveChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const validationSchema = Yup.object().shape({
    idFrontSide: Yup.string().required(t('ID card front image is required')),
    idBackSide: Yup.string().required(t('ID card back image is required')),
  });

  const formik = useFormik<IdVerificationForm>({
    initialValues: {
      idFrontSide: idFrontSide || '',
      idBackSide: idBackSide || '',
    },
    validationSchema,
    validateOnMount: true,
    onSubmit: async () => {
      try {
        setConfirmationVisible(false);
        setIsLoading(true);
        let uploadedFront: string;
        let uploadedBack: string;
        if (images.idFrontSide !== idFrontSide) {
          console.log('Upload front');
          const filename = `${uid}_frontSide_${Date.now()}.jpg`;

          uploadedFront = await uploadImage(images.idFrontSide, filename);
        }

        if (images.idBackSide !== idBackSide) {
          console.log('Upload back');
          const filename = `${uid}_frontSide_${Date.now()}.jpg`;
          uploadedBack = await uploadImage(images.idBackSide, filename);
        }

        const firestoreApp = firestore();
        const GeoFirestore = geofirestore.initializeApp(firestoreApp);
        const usersCollection = GeoFirestore.collection('Users');

        const payload: any = {
          profileStatus: 'pending',
        };

        if (uploadedFront) payload.idFrontSide = uploadedFront;
        if (uploadedBack) payload.idBackSide = uploadedBack;

        await usersCollection.doc(uid).set(payload, {merge: true});

        const userSnapshot = await usersCollection.doc(uid).get();
        dispatch(setUser({...userSnapshot.data(), uid}));

        Toast.show({
          type: 'success',
          text1: t('Success'),
          text2: t('Documents resubmitted for verification'),
        });
      } catch (error) {
        console.error('Error submitting ID verification:', error);
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Sorry, could not save information'),
        });
      } finally {
        setIsLoading(false);
      }
    },
  });

  const uploadImage = async (
    path: string,
    filename: string,
  ): Promise<string> => {
    if (!path) return '';

    try {
      const ref = storage().ref(`idCard/${filename}`);
      console.log(path);
      await ref.putFile(path);
      return await ref.getDownloadURL();
    } catch (err: any) {
      console.error('Error uploading image:', err);
      throw err; // Re-throw so parent can catch too
    }
  };

  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  const pickImage = async (source: 'camera' | 'gallery') => {
    try {
      setModalVisible(false);
      await delay(500); // Adjust delay if needed
      const pickerFn =
        source === 'camera' ? ImagePicker.openCamera : ImagePicker.openPicker;

      const image = await pickerFn({
        width: 300,
        height: 400,
        cropping: false,
        mediaType: 'photo',
        compressImageQuality: 0.25,
      });

      if (selectedField) {
        setHaveChanges(true);
        setImages(prev => ({...prev, [selectedField]: image.path}));

        formik.setFieldTouched(selectedField, true);
        formik.setFieldValue(selectedField, image.path);
      }
    } catch (error) {
      console.log('Image picking error:', error);
    }
  };

  const handleModal = (field: keyof IdVerificationForm) => {
    setSelectedField(field);
    setModalVisible(true);
  };

  const renderImageUpload = (
    label: string,
    field: keyof IdVerificationForm,
    imagePath: string,
  ) => (
    <>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity
        style={styles.imageContainer}
        onPress={() => handleModal(field)}
        activeOpacity={0.8}>
        {imagePath ? (
          <Image
            source={{uri: imagePath}}
            style={styles.image}
            // resizeMode="contain"
          />
        ) : (
          <>
            <Text style={styles.uploadTitle}>
              {t(field === 'idFrontSide' ? 'ID Front' : 'ID Back')}
            </Text>
            <Image source={upload} style={styles.uploadImage} />
            <Text style={styles.uploadText}>{t('Upload')}</Text>
          </>
        )}
      </TouchableOpacity>
      <AppText error={formik.errors[field]} visible={!!formik.touched[field]} />
    </>
  );

  return (
    <KeyboardAvoidingView
      style={{flex: 1, backgroundColor: COLORS.white}}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
      <View style={styles.container}>
        <ScrollView
          contentContainerStyle={{flexGrow: 1, justifyContent: 'space-between'}}
          showsVerticalScrollIndicator={false}>
          <View>
            {renderImageUpload(
              t('Front Side'),
              'idFrontSide',
              images.idFrontSide,
            )}
            {renderImageUpload(t('Back Side'), 'idBackSide', images.idBackSide)}
          </View>

          <TextButton
            label={t('Submit')}
            onPress={() => {
              if (!haveChanges) {
                navigation.goBack();
                return;
              }
              setConfirmationVisible(true);
            }}
            isLoading={isLoading}
            disabled={
              isLoading ||
              !formik.isValid ||
              !formik.values.idFrontSide ||
              !formik.values.idBackSide
            }
            containerStyle={{marginVertical: 30}}
          />
        </ScrollView>
      </View>

      <ImageUploadModal
        isVisible={isModalVisible}
        onCameraPress={() => pickImage('camera')}
        onGalleryPress={() => pickImage('gallery')}
        onClose={() => setModalVisible(false)}
      />
      <AlertModal
        isModalVisible={isConfirmationVisible}
        onBackdropPress={() => setConfirmationVisible(false)}
        onCancelPress={() => setConfirmationVisible(false)}
        handleConfirm={formik.handleSubmit}
        redLabel={t('Confirm')}
        title={t('Confirmation')}
        paragraph={t(
          t(
            'Submitting this action will place your profile under review. You will not be able to create new bookings until your profile is approved.',
          ),
        )}
      />
    </KeyboardAvoidingView>
  );
};

export default IdVerification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
  },
  label: {
    fontSize: 14,
    color: '#2C2C2E',
    fontFamily: FONTS.Medium,
    paddingTop: 12,
  },
  imageContainer: {
    height: verticalScale(110),
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.Philippine_Gray,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    overflow: 'hidden',
  },
  image: {
    height: '100%',
    width: '100%',
  },
  uploadTitle: {
    fontSize: 16,
    fontFamily: FONTS.Medium,
    color: COLORS.darkGray,
  },
  uploadText: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.primary,
    marginTop: 4,
  },
  uploadImage: {
    width: 24,
    height: 24,
    marginVertical: 8,
  },
});
