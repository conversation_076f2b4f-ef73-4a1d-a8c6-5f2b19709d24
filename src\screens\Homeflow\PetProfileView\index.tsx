import {ImageBackground, ScrollView, Text, View} from 'react-native';
import React, {useState} from 'react';
import {verticalScale} from 'react-native-size-matters';
import {ButtonwithIcon, TextButtonwithIcon} from '../../../components';
import {BackArrow, Location, Star} from '../../../assets/svgIcons';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import Buttons from '../PetSitterDetails/Buttons';
import appStyles from '../../../themes/appStyles';
import Services from '../PetDetails/services';
import About from '../PetSitterDetails/About';
import {styles} from './styles';
import {ImagePlaceholder2} from '../../../assets/images';
import {useAppSelector} from '../../../store/Store';
type Props = NativeStackScreenProps<PetOwnerParamList, 'PetProfileView'>;

const PetProfileView: React.FC<Props> = ({navigation, route}) => {
  const [selectedButton, setSelectedButton] = useState<string>('Services');

  const {item} = route.params;
  const user = useAppSelector(state => state.user);

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <ImageBackground
          source={item?.picture ? {uri: item?.picture} : ImagePlaceholder2}
          style={{height: verticalScale(282)}}
          imageStyle={styles.imageStyle}>
          <ButtonwithIcon
            hitSlop={{left: 20, right: 20, top: 20, bottom: 20}}
            icon={<BackArrow />}
            containerStyle={styles.icon}
            onPress={() => navigation.goBack()}
          />
        </ImageBackground>
        <View style={styles.innerContainer}>
          <Text style={[appStyles.title, {textAlign: 'center'}]}>
            {item?.name}
          </Text>
          <View style={styles.infoContainer}>
            {user.rating ? (
              <>
                <TextButtonwithIcon
                  label={user.rating.toString()}
                  labelStyle={styles.label2}
                  leftIcon={<Star />}
                  disabled
                />
                <Text style={styles.line}>|</Text>
              </>
            ) : null}
            <TextButtonwithIcon
              label={user?.city ? user?.city : ''}
              labelStyle={styles.label2}
              leftIcon={<Location />}
              disabled
            />
          </View>
          <Buttons
            onAboutPress={() => setSelectedButton('About')}
            onReviewPress={() => setSelectedButton('Reviews')}
            onServicesPress={() => setSelectedButton('Services')}
            isServicesVisible={true}
            selectedButton={selectedButton}
          />
          {selectedButton === 'Services' ? (
            <Services services={item?.services} />
          ) : (
            <About paragraph={user.about} />
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default PetProfileView;
