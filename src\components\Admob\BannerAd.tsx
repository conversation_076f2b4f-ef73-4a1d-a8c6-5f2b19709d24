import React, {useRef} from 'react';
import {Platform} from 'react-native';
import {
  BannerAd,
  BannerAdSize,
  TestIds,
  // useForeground,
} from 'react-native-google-mobile-ads';

const BannerAdComponent = () => {
  const bannerRef = useRef<typeof BannerAd>(null);

  // (iOS) WKWebView can terminate if app is in a "suspended state", resulting in an empty banner when app returns to foreground.
  // Therefore it's advised to "manually" request a new ad when the app is foregrounded (https://groups.google.com/g/google-admob-ads-sdk/c/rwBpqOUr8m8).
  // useForeground(() => {
  //   Platform.OS === 'ios' && bannerRef.current?.load();
  // });

  return (
    <BannerAd
      unitId={
        __DEV__
          ? TestIds.ADAPTIVE_BANNER
          : Platform.OS === 'android'
          ? 'ca-app-pub-3942822984828389/2878771168'
          : 'ca-app-pub-3942822984828389/4251928863'
      }
      size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
      // ref={bannerRef}
      requestOptions={{
        networkExtras: {
          collapsible: 'bottom',
        },
        keywords: ['animals', 'pets'],
      }}
    />
  );
};
export default BannerAdComponent;
