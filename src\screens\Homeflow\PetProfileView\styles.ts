import {Platform, StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  imageStyle: {
    width: '100%',
    height: verticalScale(300),
  },
  icon: {
    alignSelf: 'flex-start',
    paddingLeft: 26,
    paddingTop:
      Platform.OS === 'android' ? verticalScale(32) : verticalScale(52),
  },
  innerContainer: {
    backgroundColor: COLORS.white,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingTop: 16,
    paddingHorizontal: 24,
    paddingBottom:
      Platform.OS === 'android' ? verticalScale(20) : verticalScale(38),
  },
  label2: {
    fontSize: 10,
    fontFamily: FONTS.Medium,
    color: '#989898',
    marginLeft: 4,
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingTop: 10,
  },
  line: {
    color: COLORS.lightGray,
    paddingHorizontal: 6,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceContainer: {
    flex: 1,
    backgroundColor: COLORS.white,
    marginTop: 24,
  },
  servicesText: {
    fontSize: 14,
    color: COLORS.Philippine_Gray,
    fontFamily: FONTS.Medium,
    paddingTop: 12,
  },

  serviceModalContainer: {
    backgroundColor: COLORS.white,
    alignItems: 'center',
    padding: 14,
    borderRadius: 12,
  },
  serviceInnerContainer: {
    height: 100,
    backgroundColor: COLORS.white,
    borderColor: COLORS.lightGray,
    borderWidth: 1,
    marginTop: 8,
    padding: 12,
    width: '100%',
  },
  serviceLabel: {
    fontSize: 14,
    color: COLORS.Bastille,
    textAlign: 'left',
    fontFamily: FONTS.Medium,
  },
  serviceModalButton: {
    width: '48%',
  },
  serviceModalButtonContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 18,
  },
  serviceStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 14,
  },
  serviceSeparator: {
    alignSelf: 'center',
    width: '95%',
    height: 1,
    backgroundColor: '#EEEEEE',
    marginTop: 12,
  },
});
