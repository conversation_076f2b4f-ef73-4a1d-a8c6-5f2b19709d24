import {StyleSheet} from 'react-native';
import {moderateScale, verticalScale} from 'react-native-size-matters';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  innerContainer: {
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  title: {
    fontSize: 24,
    color: COLORS.black,
    fontFamily: FONTS.Bold,
    paddingBottom: 20,
  },
  emptyText: {
    fontFamily: FONTS.SemiBold,
    marginTop: 27,
    fontSize: 10,
    color: '#939090',
  },
  emptyDesc: {
    fontFamily: FONTS.Medium,
    color: '#A0A0A0',
    fontSize: 9,
    marginTop: 4,
  },
  chatCardContainer: {
    flexDirection: 'row',
  },
  profileImage: {
    width: verticalScale(55),
    height: verticalScale(55),
    borderRadius: verticalScale(55) / 2,
  },
  chatCardInnerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    marginLeft: 16,
    paddingVertical: 8,
  },
  messageContainer: {
    justifyContent: 'space-between',
    width: '75%',
  },
  recipientName: {
    fontSize: moderateScale(16),
    color: COLORS.black,
    fontFamily: FONTS.SemiBold,
  },
  messageText: {
    fontSize: moderateScale(12),
    color: COLORS.black,
    fontFamily: FONTS.Medium,
  },
  timeContainer: {
    justifyContent: 'space-between',
  },
  dividerLine: {
    height: 1,
    backgroundColor: '#EEEEEE',
    width: '80%',
    alignSelf: 'flex-end',
    marginVertical: 7,
  },
  unreadMessageBadge: {
    backgroundColor: COLORS.primary,
    borderRadius: 100,
    width: 20,
    height: 20,
    justifyContent: 'center',
    marginTop: 10,
  },
  unreadMessageText: {
    fontFamily: FONTS.Medium,
    color: COLORS.white,
    alignSelf: 'center',
    fontSize: 12,
  },
});
