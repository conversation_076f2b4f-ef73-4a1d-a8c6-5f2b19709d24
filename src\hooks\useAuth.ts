import {useDispatch} from 'react-redux';
import firestore from '@react-native-firebase/firestore';
import {setUser} from '../slices/userSlice';
import {addPet} from '../slices/petSlice';
import {IUser} from '../interfaces';
import Toast from 'react-native-toast-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {FirebaseAuthTypes} from '@react-native-firebase/auth';
import {useContext} from 'react';
import {AuthContext} from '../../App';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import functions from '@react-native-firebase/functions';
import {sendWelcomeEmail} from '../backend/send-welcome-email';

export default () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const {setUserId, isOwner} = useContext(AuthContext);
  const {t} = useTranslation();

  const onVerifiedLogin = async (user: FirebaseAuthTypes.User) => {
    const userDoc = await firestore().collection('Users').doc(user.uid).get();
    await AsyncStorage.setItem('uid', user.uid);
    const fcm_Token = await AsyncStorage.getItem('fcmToken');
    // This case mustn't appear
    // if (!userDoc.exists) {
    //   navigation.navigate('ProfileVerification', {
    //     email: user.email || '',
    //     uid: user.uid,
    //     firstName: user.displayName || '',
    //     surName: '',
    //   });
    //   return;
    // }
    const userDetails = {...userDoc.data(), uid: user.uid} as IUser;

    const payload: IUser = {
      firstName: userDetails?.firstName,
      surName: userDetails.surName,
      email: userDetails?.email,
      phoneNumber: userDetails?.phoneNumber,
      address: userDetails?.address,
      city: userDetails?.city,
      country: userDetails?.country,
      dateOfBirth: userDetails?.dateOfBirth,
      about: userDetails?.about || '',
      idFrontSide: userDetails?.idFrontSide,
      idBackSide: userDetails?.idBackSide,
      userType: isOwner ? 'petOwner' : 'petSitter',
      countryCode: userDetails?.countryCode,
      uid: userDetails?.uid,
      profilePicture: userDetails?.profilePicture,
      coordinates: userDetails?.coordinates,
      fcmToken: fcm_Token || '',
      profileStatus: userDetails?.profileStatus,

      // customerId: user?.customerId,
    };

    dispatch(setUser(payload));

    const fcmToken = await AsyncStorage.getItem('fcmToken');
    await firestore().collection('Users').doc(user.uid).update({
      fcmToken: fcmToken,
    });

    const userData = userDoc.data()?.userType;
    const usersPet = await firestore()
      .collection('Pets')
      .where('userId', '==', user.uid)
      .get();

    if (userDetails.isBlocked) {
      Toast.show({
        type: 'error',
        text1: t('Blocked Access'),
        text2: t('<NAME_EMAIL>'),
      });
    } else if (!userDetails.phoneNumber) {
      navigation.navigate('ProfileVerification', {
        email: user.email || '',
        uid: user?.uid,
        firstName: userDetails?.firstName || '',
        surName: userDetails.surName || '',
      });
    } else if (!userDetails.isVerified) {
      await functions()
        .httpsCallable('sendOtpCode')({
          phoneNumber: user.phoneNumber,
        })
        .then(() => {
          navigation.navigate('CodeVerification', {
            uid: user.uid,
            phoneNumber: userDetails.phoneNumber,
          });
        });
    } else if (userData == 'petOwner' && isOwner) {
      if (!usersPet) {
        navigation.navigate('PetProfile', {
          isAuthFlow: true,
          uid: user.uid,
        });
      }
      // else if (!user.isPremiumCustomer) {
      //   navigation.navigate('Subscription', {uid: response.user.uid});
      // }
      else {
        if (userData == 'petOwner' && isOwner) {
          setUserId('petOwner');
        } else if (userData == 'petSitter' && !isOwner) {
          setUserId('petSitter');
        } else {
          Toast.show({
            type: 'error',
            text1: t('Error'),
            text2: t('Login with correct credentials'),
          });
        }
      }
    } else {
      if (userData == 'petOwner' && isOwner) {
        setUserId('petOwner');
      } else if (userData == 'petSitter' && !isOwner) {
        setUserId('petSitter');
      } else {
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Login with correct credentials'),
        });
      }
    }
  };

  return {
    onVerifiedLogin,
  };
};
