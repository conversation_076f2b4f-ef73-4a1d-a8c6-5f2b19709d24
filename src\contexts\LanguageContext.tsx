import React, {createContext, useContext, useState, useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from '../i18n/i18n';
import {LocaleConfig} from 'react-native-calendars';

type LanguageInitial = 'en' | 'nl' | 'fr' | 'de';

type LanguageContextType = {
  selectedLanguage?: LanguageInitial;
  setLanguage: (language?: LanguageInitial) => void;
  loadingLanguage: boolean;
};

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined,
);
type Props = {
  children?: React.ReactNode;
};

export const LanguageProvider: React.FC<Props> = ({children}) => {
  const [selectedLanguage, setSelectedLanguage] = useState<LanguageInitial>();
  const [loadingLanguage, setLoadingLanguage] = useState(true);

  useEffect(() => {
    const loadSelectedLanguage = async () => {
      try {
        const storedLanguage = await AsyncStorage.getItem('selectedLanguage');
        if (storedLanguage) {
          setSelectedLanguage(storedLanguage as LanguageInitial);
          i18n.changeLanguage(storedLanguage);
          LocaleConfig.defaultLocale = storedLanguage;
        }
        setLoadingLanguage(false);
      } catch (error) {
        console.error('Error loading selected language:', error);
        setLoadingLanguage(false);
      }
    };

    loadSelectedLanguage();
  }, []);

  const setLanguage = async (language?: LanguageInitial) => {
    try {
      await AsyncStorage.setItem('selectedLanguage', language || '');
      setSelectedLanguage(language);
    } catch (error) {
      console.error('Error setting selected language---:', error);
    }
  };

  return (
    <LanguageContext.Provider
      value={{selectedLanguage, setLanguage, loadingLanguage}}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
