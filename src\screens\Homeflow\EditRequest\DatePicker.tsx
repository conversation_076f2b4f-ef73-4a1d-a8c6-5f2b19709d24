import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {FONTS} from '../../../themes/fonts';
import {COLORS} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {
  Calenderfill,
  ChevronLeft,
  ChevronRight,
} from '../../../assets/svgIcons';
import {Calendar, DateData} from 'react-native-calendars';
import Modal from 'react-native-modal';
import {TextButton} from '../../../components';
import moment from 'moment';
import {useTranslation} from 'react-i18next';
interface DateType {
  startDate: string;
  endDate: string;
}
interface MarkedDates {
  [key: string]: {
    color: string;
  };
}
interface Props {
  setSelectedDate: (startDate: string, isEndDate: string) => void;
  date: DateType;
}

const DatePicker: React.FC<Props> = ({setSelectedDate, date}) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [markedDates, setMarkedDates] = useState<MarkedDates>({});
  const [selectedStartDate, setSelectedStartDate] = useState<string | null>(
    null,
  );
  const [selectedEndDate, setSelectedEndDate] = useState<string | null>(null);
  const {t} = useTranslation();

  const handleDayPress = (day: DateData) => {
    const selectedDate = moment(day.dateString).format('YYYY-MM-DD');

    if (selectedStartDate && selectedEndDate) {
      setSelectedStartDate(null);
      setSelectedEndDate(null);
      setMarkedDates({});
    } else if (!selectedStartDate) {
      setSelectedStartDate(selectedDate);
      setSelectedDate(selectedDate, '');
      setMarkedDates({
        [selectedDate]: {
          color: COLORS.primary,
        },
      });
    } else if (!selectedEndDate && selectedDate !== selectedStartDate) {
      setSelectedEndDate(selectedDate);
      setSelectedDate(selectedStartDate, selectedDate);

      const startDate = moment(selectedStartDate);
      const endDate = moment(selectedDate);
      const daysInRange: string[] = [];
      while (startDate.isBefore(endDate, 'day')) {
        startDate.add(1, 'day');
        daysInRange.push(startDate.format('YYYY-MM-DD'));
      }
      setMarkedDates(prevDates => ({
        ...prevDates,
        ...daysInRange.reduce((acc, date) => {
          acc[date] = {
            color: COLORS.primary,
          };
          return acc;
        }, {} as MarkedDates),
      }));
    } else {
      setSelectedStartDate(null);
      setSelectedEndDate(null);
      setMarkedDates({});
    }
  };

  const formatSelectedDates = (dates: string[]) => {
    if (dates.length === 2) {
      return `${dates[0]} - ${dates[1]}`;
    } else {
      return dates[0];
    }
  };

  return (
    <View>
      <Text style={styles.title}>{t('Calendar')}</Text>
      <TouchableOpacity
        style={styles.container}
        onPress={() => setModalVisible(true)}>
        <Text
          style={[
            styles.text,
            {color: date.startDate ? COLORS.Bastille : COLORS.placeHolder},
          ]}>
          {date.startDate && date.endDate
            ? `${date.startDate} - ${date.endDate}`
            : date.startDate
            ? date.startDate
            : '10/8/2023'}
        </Text>
        <Text style={styles.text}>
          <Calenderfill />
        </Text>
      </TouchableOpacity>
      <Modal
        isVisible={isModalVisible}
        onBackdropPress={() => setModalVisible(false)}>
        <View style={styles.modalContainer}>
          <Calendar
            minDate={new Date().toDateString()}
            i18nIsDynamicList={true}
            onDayPress={handleDayPress}
            renderArrow={direction =>
              direction === 'left' ? (
                <ChevronLeft width={24} height={24} />
              ) : (
                <ChevronRight width={24} height={24} />
              )
            }
            markedDates={markedDates}
            markingType={'period'}
            theme={{
              textSectionTitleColor: '#2F3C46',
              selectedDayBackgroundColor: COLORS.primary,
              selectedDayTextColor: COLORS.white,
              todayTextColor: '#00adf5',
              dayTextColor: '#2F3C46',
            }}
          />
          <TextButton
            label={t('Ok')}
            containerStyle={styles.button}
            onPress={() => setModalVisible(false)}
          />
        </View>
      </Modal>
    </View>
  );
};

export default DatePicker;

const styles = StyleSheet.create({
  container: {
    height: verticalScale(48),
    borderWidth: 1,
    borderColor: '#E2E2E2',
    borderRadius: 6,
    marginTop: 8,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  title: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
    marginTop: 14,
  },
  text: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
  },
  button: {
    width: '60%',
    alignSelf: 'center',
    marginVertical: 28,
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    paddingHorizontal: 12,
    borderRadius: 24,
    paddingTop: 16,
  },
});
