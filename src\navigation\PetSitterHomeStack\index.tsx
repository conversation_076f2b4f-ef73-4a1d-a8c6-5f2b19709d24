import {StyleSheet} from 'react-native';
import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {SitterBottomStackParamList} from '../PetSitterBottomTabs';
import {EmailVerification} from '../../screens/authflow';
import RequestScreen from '../../screens/Homeflow/Request';
import PetSitterBottomTab from '../PetSitterBottomTabs';
import {
  ChatDetails,
  ContactUs,
  DeleteAccount,
  MyPets,
  MyReviews,
  Notification,
  PrivacyPolicy,
  TermofUse,
  RecoveredPasswordFeedback,
  PetDetails,
  ResetPassword,
} from '../../screens/Homeflow';
import EditProfile from '../../screens/Homeflow/EditProfile';
import SelectLanguage from '../../screens/authflow/SelectLanguage';
import SellerProfile from '../../screens/Homeflow/SellerProfile/SellerProfile';
import {IConversationBooking} from '../../interfaces/IBooking';
import IdVerification from '../../screens/Homeflow/IdVerification/IdVerification';
import FAQ from '../../screens/Homeflow/FAQ';
import PaymentProfile from '../../screens/Homeflow/PaymentProfile';

export type PetSitterParamList = {
  PetSitterBottomTabs: SitterBottomStackParamList;
  Notification: undefined;
  PetDetails: {id: string};
  ChatDetails: {
    recipientId: string;
    serviceDone: boolean;
    booking?: IConversationBooking;
  };
  TermofUse: undefined;
  PrivacyPolicy: undefined;
  FAQ: undefined;
  MyPets: undefined;
  AddServices: undefined;
  PetProfile: undefined;
  MyReviews: undefined;
  EmailVerification: undefined;
  IdVerification: undefined;
  ResetPassword: undefined;
  ContactUs: undefined;
  DeleteAccount: undefined;
  Login: undefined;
  SitterPetDetails: undefined;
  RecoveredPasswordFeedback: undefined;
  RequestScreen: undefined;
  EditProfile: undefined;
  SelectLanguage1: undefined;
  SellerProfile: undefined;
  PaymentProfile: {uri: string};
};
const PetSitterHomeStack = createNativeStackNavigator<PetSitterParamList>();
const PetSitterHomeStackScreen = () => {
  return (
    <PetSitterHomeStack.Navigator
      screenOptions={{headerShown: false, headerShadowVisible: false}}>
      <PetSitterHomeStack.Screen
        name="PetSitterBottomTabs"
        component={PetSitterBottomTab}
        options={{headerShown: true, headerTitle: ''}}
      />

      <PetSitterHomeStack.Screen
        name="Notification"
        component={Notification}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name="PetDetails"
        component={PetDetails}
        options={{headerShown: false}}
      />
      <PetSitterHomeStack.Screen
        name="ChatDetails"
        component={ChatDetails}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name="TermofUse"
        component={TermofUse}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name="PrivacyPolicy"
        component={PrivacyPolicy}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name="FAQ"
        component={FAQ}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name="MyPets"
        component={MyPets}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name="IdVerification"
        component={IdVerification}
        options={{headerShown: true, headerTitle: ''}}
      />
      {/* <PetSitterHomeStack.Screen
                name="PetProfile"
                component={PetProfile}
                options={{ headerShown: true, headerTitle: '' }}
            /> */}
      <PetSitterHomeStack.Screen
        name="MyReviews"
        component={MyReviews}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name={'EmailVerification'}
        component={EmailVerification}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name={'ResetPassword'}
        component={ResetPassword}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name={'ContactUs'}
        component={ContactUs}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name={'DeleteAccount'}
        component={DeleteAccount}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name={'RequestScreen'}
        component={RequestScreen}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name={'RecoveredPasswordFeedback'}
        component={RecoveredPasswordFeedback}
        options={{headerShown: false}}
      />
      <PetSitterHomeStack.Screen
        name={'EditProfile'}
        component={EditProfile}
        options={{headerShown: true, headerTitle: ''}}
      />
      <PetSitterHomeStack.Screen
        name={'SelectLanguage1'}
        component={SelectLanguage}
        options={{headerShown: false}}
      />

      <PetSitterHomeStack.Screen
        name={'SellerProfile'}
        component={SellerProfile}
        options={{headerShown: false}}
      />
      <PetSitterHomeStack.Screen
        name={'PaymentProfile'}
        component={PaymentProfile}
        options={{headerShown: true, headerTitle: ''}}
      />
    </PetSitterHomeStack.Navigator>
  );
};

export default PetSitterHomeStackScreen;

const styles = StyleSheet.create({});
