import {
  ActivityIndicator,
  ImageBackground,
  ScrollView,
  Text,
  TextInput,
  View,
  ViewStyle,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {
  Alert,
  ButtonwithIcon,
  TextButton,
  TextButtonwithIcon,
} from '../../../components';
import {BackArrow, Location, Report, Star} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import Buttons from '../PetSitterDetails/Buttons';
import About from '../PetSitterDetails/About';
import Services from './services';
import {verticalScale} from 'react-native-size-matters';
import firestore from '@react-native-firebase/firestore';
import {SitterBottomStackParamList} from '../../../navigation/PetSitterBottomTabs';
import Toast from 'react-native-toast-message';
import functions from '@react-native-firebase/functions';
import {styles} from './styles';
import {useAppSelector} from '../../../store/Store';
import {ImagePlaceholder2, ImagePlaceholder3} from '../../../assets/images';
import {IBooking} from '../../../interfaces';
import {useTranslation} from 'react-i18next';
import Modal from 'react-native-modal';

type Props = NativeStackScreenProps<SitterBottomStackParamList, 'PetDetails'>;

const PetDetails: React.FC<Props> = ({navigation, route}) => {
  const [selectedButton, setSelectedButton] = useState<string>('About');
  const [bookingData, setBookingData] = useState<IBooking>();
  const [isLoading, setIsLoading] = useState(true);
  const [isReporting, setIsReporting] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [isReportModalVisible, setReportModalVisible] = useState(false);
  const [reportReason, setReportReason] = useState('');

  const {id} = route.params;
  const user = useAppSelector(state => state.user);
  const {t} = useTranslation();

  const renderModal = () => {
    return (
      <Modal
        isVisible={isReportModalVisible}
        onBackdropPress={() => setReportModalVisible(false)}>
        <View style={styles.serviceModalContainer}>
          <Report />
          <Text style={[appStyles.title2, {fontSize: 18, paddingTop: 10}]}>
            {t('Report User')}
          </Text>
          <View style={{width: '100%'}}>
            <Text style={styles.serviceLabel}>{t('Reason')}</Text>
            <View style={styles.serviceInnerContainer}>
              <TextInput
                placeholder={t('Reason for reporting...')}
                placeholderTextColor={COLORS.placeHolder}
                multiline={true}
                value={reportReason}
                onChangeText={text => setReportReason(text)}
              />
            </View>
          </View>
          <View style={styles.serviceModalButtonContainer}>
            <TextButton
              label={t('Cancel')}
              labelStyle={{color: COLORS.Bastille}}
              containerStyle={
                [
                  styles.serviceModalButton,
                  {backgroundColor: COLORS.white},
                ] as ViewStyle
              }
              onPress={() => setReportModalVisible(false)}
            />
            <TextButton
              label={t('Report')}
              containerStyle={styles.serviceModalButton}
              onPress={() => {
                handleReport(reportReason);
              }}
              disabled={!reportReason || isReporting}
              isLoading={isReporting}
            />
          </View>
        </View>
      </Modal>
    );
  };

  const handleReport = async (reportReason: string) => {
    try {
      setIsReporting(true);
      const payload = {
        userId: user?.uid,
        name: user?.firstName,
        reason: reportReason,
        reportedUserId: id,
      };
      await functions()
        .httpsCallable('sendEmail')(payload)
        .then(() => {
          navigation.goBack();
          setReportModalVisible(false);
        });
    } catch (error) {
      console.log('Error reporting pet:', error);
      Toast.show({
        type: 'error',
        text1: t('Sorry could not report right now'),
        position: 'top',
        autoHide: true,
      });
    } finally {
      setIsReporting(false);
    }
  };

  const fetchBookingDetails = async () => {
    setIsLoading(true);
    try {
      const bookingDoc = await firestore().collection('Bookings').doc(id).get();

      if (bookingDoc.exists) {
        const petId = bookingDoc.data()?.petId;
        const petDoc = await firestore().collection('Pets').doc(petId).get();
        const userDoc = await firestore()
          .collection('Users')
          .doc(bookingDoc.data()?.userId)
          .get();

        if (petDoc.exists) {
          setBookingData({
            ...bookingDoc.data(),
            key: bookingDoc.id,
            petData: petDoc.data(),
            userData: userDoc.data(),
          } as IBooking);
        } else {
          console.log('Pet not found');
        }
      } else {
        console.log('Booking not found');
      }
    } catch (error) {
      console.log('Error fetching booking details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBookingDetails();
  }, [id]);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  const handleInterested = async () => {
    try {
      const payload = {
        senderId: user.uid,
        senderName: user.firstName,
        receiverId: bookingData!.userData.uid,
        receiverToken: bookingData?.userData.fcmToken,
        timeStamp: new Date(),
        bookingId: bookingData!.key,
        booking: {
          price: bookingData?.price,
          services: bookingData?.services,
          id: bookingData?.key,
          bookingDate: bookingData?.bookingDate,
        },
      };

      await functions().httpsCallable('sendInterestedNotifications')(payload);
      await firestore().collection('Requests').add({
        bookingId: bookingData?.key,
        senderId: user.uid,
        receiverId: bookingData?.userData.uid,
      });
      setModalVisible(true);
      // if (petData) {
      setTimeout(() => {
        setModalVisible(false);
        // navigation.navigate('ChatDetails', {
        //   recipientId: petData?.user.uid || '',
        //   serviceDone: false,
        //   // price: petData?.price || '',
        //   booking: {
        //     price: petData?.price,
        //     services: petData?.services,
        //     id: petData.key,
        //     bookingDate: petData.bookingDate,
        //   },
        // });
      }, 3000);
      // }
    } catch (error) {
      console.log('An error occurred while handling interest:', error);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <ImageBackground
          source={
            bookingData?.petData.picture
              ? {uri: bookingData?.petData.picture}
              : ImagePlaceholder3
          }
          style={{height: verticalScale(282)}}
          imageStyle={styles.imageStyle}
          // loadingIndicatorSource={ImagePlaceholder2}
        >
          <ButtonwithIcon
            hitSlop={{left: 20, right: 20, top: 20, bottom: 20}}
            icon={<BackArrow />}
            containerStyle={styles.icon}
            onPress={() => navigation.goBack()}
          />
        </ImageBackground>
        <View style={styles.innerContainer}>
          <Text style={[appStyles.title, {textAlign: 'center'}]}>
            {bookingData?.petData.name}
          </Text>
          <View style={styles.infoContainer}>
            {bookingData?.userData.rating ? (
              <>
                <TextButtonwithIcon
                  label={bookingData?.userData.rating?.toString() || '0'}
                  labelStyle={styles.label2}
                  leftIcon={<Star />}
                  disabled
                />
                <Text style={styles.line}>|</Text>
              </>
            ) : null}
            <TextButtonwithIcon
              label={
                bookingData?.userData.city ? bookingData?.userData?.city : ''
              }
              labelStyle={styles.label2}
              leftIcon={<Location />}
              disabled
            />
          </View>
          <Buttons
            onAboutPress={() => setSelectedButton('About')}
            onReviewPress={() => setSelectedButton('Reviews')}
            onServicesPress={() => setSelectedButton('Services')}
            isServicesVisible={true}
            selectedButton={selectedButton}
          />
          {selectedButton === 'Services' ? (
            <Services
              services={bookingData?.services || []}
              price={bookingData?.price || ''}
            />
          ) : (
            <About paragraph={bookingData?.userData.about || ''} />
          )}
        </View>

        <TextButton
          label={t('Interested')}
          onPress={handleInterested}
          containerStyle={styles.interestedButton}
          disabled={user.profileStatus === 'pending'}
        />
        <TextButton
          label={t('Report')}
          labelStyle={{color: COLORS.primary}}
          containerStyle={styles.button}
          onPress={() => setReportModalVisible(true)}
          disabled={user.profileStatus === 'pending'}
        />
      </ScrollView>
      <Alert
        onBackdropPress={() => setModalVisible(false)}
        isModalVisible={isModalVisible}
        title={t('Notification Sent')}
        paragraph={t(
          'Notification of your interest has been sent to the pet owner.',
        )}
      />
      {renderModal()}
    </View>
  );
};

export default PetDetails;
