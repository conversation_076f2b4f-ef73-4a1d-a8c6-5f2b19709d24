import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import appStyles from '../../../themes/appStyles';
import {Magnify} from '../../../assets/svgIcons';
import {ButtonwithIcon} from '../../../components';
import {COLORS} from '../../../themes/themes';
import {useTranslation} from 'react-i18next';

interface props {
  onPress: () => void;
}

const Header: React.FC<props> = ({onPress}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <Text style={[appStyles.title2, {fontSize: 18}]}>{t('My Reviews')}</Text>
      <ButtonwithIcon
        icon={<Magnify stroke={COLORS.primary} />}
        onPress={onPress}
      />
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
