import {
  StyleSheet,
  Text,
  View,
  ViewStyle,
  TextStyle,
  Platform,
} from 'react-native';
import React from 'react';
import RNPickerSelect from 'react-native-picker-select';
import {COLORS} from '../../themes/themes';
import {DropDown} from '../../assets/svgIcons';
import {FONTS} from '../../themes/fonts';
import {verticalScale} from 'react-native-size-matters';
import {DropDownIcon} from '../../assets/images';

interface PickerItem {
  label: string;
  value: string;
}

interface Props {
  item: PickerItem[];
  containerStyle?: ViewStyle;
  onValueChange: (text: string) => void;
  label?: string;
  labelStyle?: TextStyle;
  placeholder: string;
  value: string;
}

const CustomIcon = () => <DropDown />;

const Picker: React.FC<Props> = ({
  item,
  containerStyle,
  onValueChange,
  label,
  labelStyle,
  placeholder,
  value,
}) => {
  return (
    <View style={{width: '100%'}}>
      {label && <Text style={[styles.label, labelStyle]}>{label}</Text>}
      <View style={[styles.container, containerStyle]}>
        <RNPickerSelect
          placeholder={{
            label: placeholder,
            value: null,
            color: COLORS.Bastille,
          }}
          value={value}
          onValueChange={onValueChange}
          items={item}
          useNativeAndroidPickerStyle={false}
          Icon={CustomIcon}
          style={{
            iconContainer: {
              top: Platform.OS === 'ios' ? 0 : 18,
              right: 0,
            },
            inputAndroid: styles.inputAndroid,
            inputIOS: styles.inputIOS,
          }}
        />
      </View>
    </View>
  );
};

export default Picker;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    height: verticalScale(48),
    borderWidth: 1,
    borderRadius: 5,
    borderColor: COLORS.lightGray,
    paddingHorizontal: 10,
    justifyContent: 'center',
  },
  label: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
    marginBottom: 8,
  },
  inputAndroid: {
    color: COLORS.Bastille,
  },
  inputIOS: {
    color: COLORS.Bastille,
  },
});
