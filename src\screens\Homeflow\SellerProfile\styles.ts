import {Dimensions, StyleSheet} from 'react-native';
import {FONTS} from '../../../themes/fonts';
import {COLORS} from '../../../themes/themes';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  onboardComplete: {
    fontFamily: FONTS.SemiBold,
    textAlign: 'center',
    marginTop: Dimensions.get('window').height / 2 - 100,
    color: COLORS.Philippine_Gray,
  },
  balanceText: {
    fontFamily: FONTS.SemiBold,
    fontSize: 18,
    color: COLORS.primary,
  },
  descText: {
    fontFamily: FONTS.SemiBold,
    color: COLORS.primary,
    fontSize: 13,
    marginTop: 2,
  },
  nameText: {
    fontFamily: FONTS.SemiBold,
    color: COLORS.primary,
    fontSize: 16,
  },
  descContainer: {
    backgroundColor: '#F9F9F9',
    borderWidth: 1,
    borderColor: '#EBEBEB',
    borderRadius: 15,
    paddingVertical: 15,
    paddingHorizontal: 10,
    marginTop: 12,
    marginHorizontal: 10,
  },
  headingText: {
    fontFamily: FONTS.Medium,
    fontSize: 12,
    color: '#2C2C2E',
    marginTop: 23,
    paddingLeft: 10,
  },
  text: {color: '#2C2C2E', fontFamily: FONTS.Medium},
  priceContainer: {
    marginTop: 40,
    alignSelf: 'center',
    padding: 40,
    borderRadius: 100,
    shadowColor: '#FFF9F3',
  },
});
