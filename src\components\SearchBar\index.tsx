import {StyleSheet, Text, TextInput, View, ViewStyle} from 'react-native';
import React from 'react';
import {COLORS} from '../../themes/themes';
import {Magnify} from '../../assets/svgIcons';
import {useTranslation} from 'react-i18next';

interface Props {
  containerStyle?: ViewStyle;
}

const SearchBar: React.FC<Props> = ({containerStyle, ...textInputProps}) => {
  const {t} = useTranslation();

  return (
    <View style={[styles.container, containerStyle]}>
      <Magnify stroke={'#BEBEBE'} />
      <TextInput
        placeholder={t('Search')}
        placeholderTextColor={COLORS.placeHolder}
        style={{height: 48, marginLeft: 8, flex: 1}}
        {...textInputProps}
      />
    </View>
  );
};

export default SearchBar;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    height: 48,
    paddingLeft: 10,
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
});
