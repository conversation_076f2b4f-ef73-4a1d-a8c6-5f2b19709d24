import {Image, ScrollView, Text, View} from 'react-native';
import React, {useContext} from 'react';
import {CatFamily, logo} from '../../../assets/images';
import {TextButton} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {AuthContext} from '../../../../App';
import {styles} from './styles';
import {useTranslation} from 'react-i18next';

type Props = NativeStackScreenProps<AuthStackParamList, 'JoinUs'>;

const JoinUs: React.FC<Props> = ({navigation}) => {
  const {setIsOwner} = useContext(AuthContext);
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={{paddingBottom: 40}}
        showsVerticalScrollIndicator={false}>
        <View>
          <Image source={logo} style={styles.logoContainer} />
          <Image
            resizeMode="contain"
            source={CatFamily}
            style={styles.imageContainer}
          />
        </View>
        <View style={{width: '100%'}}>
          <Text style={styles.title}>{t('Join us as')}</Text>
          <TextButton
            label={t('Pet Sitter')}
            onPress={() => {
              navigation.navigate('Login');
              setIsOwner(false);
            }}
            isShadow
            containerStyle={{padding: 5}}
          />
          <TextButton
            label={t('Pet Owner')}
            containerStyle={styles.buttonContainer}
            labelStyle={styles.label}
            onPress={() => {
              navigation.navigate('Login');
              setIsOwner(true);
            }}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default JoinUs;
