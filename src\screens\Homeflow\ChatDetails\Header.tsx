import {StyleSheet, Text, View} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import appStyles from '../../../themes/appStyles';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {AlertModal, TextButton} from '../../../components';
import firestore from '@react-native-firebase/firestore';
import {useAppSelector} from '../../../store/Store';
import {useNavigation} from '@react-navigation/native';
import {placeholderImage} from '../../../assets/images';
import Toast from 'react-native-toast-message';
import FastImage from 'react-native-fast-image';
import {AuthContext} from '../../../../App';
import {IUser} from '../../../interfaces';
import {useTranslation} from 'react-i18next';

interface HeaderProps {
  isOnline: boolean;
  recipientId: string;
  serviceDone: boolean;
  handleServiceDonePress?: () => void;
  isBlocked?: boolean;
}
const Header: React.FC<HeaderProps> = ({
  isOnline,
  recipientId,
  serviceDone,
  handleServiceDonePress,
  isBlocked,
}) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [recipient, setRecipient] = useState<IUser>();
  const [newRating, setNewRating] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const {userId} = useContext(AuthContext);

  const user = useAppSelector(state => state.user);
  const navigation = useNavigation();
  const {t} = useTranslation();

  const handleSubmitRating = async () => {
    try {
      setIsLoading(true);
      const existingReviewSnapshot = await firestore()
        .collection('Reviews')
        .where('sentTo', '==', recipientId)
        .where('sentBy', '==', user.uid)
        .get();

      if (!existingReviewSnapshot.empty) {
        const existingReviewId = existingReviewSnapshot.docs[0].id;
        await firestore().collection('Reviews').doc(existingReviewId).update({
          rating: newRating,
          reviewDate: new Date(),
        });
      } else {
        const payload = {
          user: user,
          sentTo: recipientId,
          sentBy: user.uid,
          rating: newRating,
          reviewDate: new Date(),
        };
        await firestore().collection('Reviews').doc().set(payload);
      }

      const querySnapshot = await firestore()
        .collection('Reviews')
        .where('sentTo', '==', recipientId)
        .get();

      const reviewData = querySnapshot.docs.map(doc => ({
        ...doc.data(),
        key: doc.id,
      }));

      const totalRatings = reviewData.reduce(
        (acc, review) => acc + review.rating,
        0,
      );

      const averageRating =
        reviewData.length > 0 ? totalRatings / reviewData.length : 0;

      await firestore().collection('Users').doc(recipientId).update({
        rating: averageRating,
      });

      if (recipientId) {
        const uid =
          recipientId > user.uid
            ? user.uid + '-' + recipientId
            : recipientId + '-' + user.uid;

        await firestore().collection('ServiceDone').add({
          petOwner: user,
          petSitter: recipient,
          timeStamp: new Date(),
        });

        await firestore()
          .collection('Conversations')
          .doc(uid)
          .update({serviceDone: true})
          .then(() => {
            Toast.show({
              type: 'success',
              text1: t('Success'),
              text2: t('Rating submitted'),
            });
            setModalVisible(false);
            navigation.goBack();
          });
      }
    } catch (error) {
      console.log('Error adding review:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecipent = async () => {
    const recipent = await firestore()
      .collection('Users')
      .doc(recipientId)
      .get();
    setRecipient(recipent.data() as IUser);
  };

  useEffect(() => {
    fetchRecipent();
  }, []);

  return (
    <View style={styles.container}>
      <FastImage
        source={
          recipient?.profilePicture
            ? {uri: recipient?.profilePicture}
            : placeholderImage
        }
        style={styles.imageContainer}
      />
      <View style={styles.innerContainer2}>
        <View>
          <Text style={[appStyles.title2, {width: '100%'}]} numberOfLines={1}>
            {`${recipient?.firstName || ''} ${recipient?.surName || ''}`}
          </Text>
          <View style={styles.innerContainer}>
            <View
              style={[
                styles.dot,
                {backgroundColor: isOnline ? '#00C968' : '#FF5733'},
              ]}
            />
            <Text style={styles.text}>
              {isOnline ? t('Online') : t('Offline')}
            </Text>
          </View>
        </View>
        {(userId == 'petOwner' && !serviceDone) ||
          (!isBlocked && (
            <TextButton
              label={t('Service Done')}
              labelStyle={styles.label}
              containerStyle={styles.button}
              onPress={() => setModalVisible(true)}
            />
          ))}
      </View>
      <AlertModal
        isModalVisible={isModalVisible}
        onBackdropPress={() => setModalVisible(false)}
        onCancelPress={() => setModalVisible(false)}
        handleConfirm={handleSubmitRating}
        redLabel={t('Submit')}
        title={t('Rate Service')}
        paragraph={t('Would you like to rate the pet owner?')}
        isRating
        onRatingCompleted={setNewRating}
        isLoading={isLoading}
      />
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  imageContainer: {
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    marginRight: 8,
  },
  text: {
    fontSize: 10,
    color: COLORS.Bastille,
    fontFamily: FONTS.Medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 6 / 2,
    marginRight: 3,
  },
  button: {
    width: '30%',
    height: 28,
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 30,
  },
  label: {
    fontSize: 10,
    color: COLORS.primary,
    fontFamily: FONTS.Medium,
  },
  innerContainer2: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});
