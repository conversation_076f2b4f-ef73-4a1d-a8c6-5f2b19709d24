import React, {useEffect, useMemo, useState} from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import {Location} from '../../../assets/images';
import {ButtonwithIcon, TextButton} from '../../../components';
import appStyles from '../../../themes/appStyles';
import {BackArrow} from '../../../assets/svgIcons';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import firestore from '@react-native-firebase/firestore';
import FastImage from 'react-native-fast-image';
import {IBooking} from '../../../interfaces/IBooking';
import {useAppSelector} from '../../../store/Store';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';
import {COLORS} from '../../../themes/themes';
import {IPet, IUser} from '../../../interfaces';
import functions from '@react-native-firebase/functions';
import {IRequest} from '../../../interfaces/IRequest';
import useBookings from '../../../hooks/useBookings';

type Props = NativeStackScreenProps<PetOwnerParamList, 'RequestScreen'>;

const RequestScreen: React.FC<Props> = ({navigation}) => {
  const [bookingRequests, setBookingRequests] = useState<IRequest[]>([]);

  const [selectedTab, setSelectedTab] = useState<string>('Received');
  const [isLoading, setIsLoading] = useState(true);
  const user = useAppSelector(state => state.user);
  const {t} = useTranslation();
  const {getBookingRequests} = useBookings();
  const [loadingStates, setLoadingStates] = useState<{[key: string]: boolean}>(
    {},
  );

  console.log(user.uid);

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          hitSlop={appStyles.hitSlop}
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);
  const fetchRequests = async () => {
    setIsLoading(true);

    const getBookings = async (
      query: 'senderId' | 'receiverId',
      value: string,
    ) => {
      const querySnapshot = await firestore()
        .collection('Requests')
        .where(query, '==', value)
        .get();

      const bookingsPromises = querySnapshot.docs.map(async doc => {
        const bookingDoc = await firestore()
          .collection('Bookings')
          .doc(doc.data().bookingId)
          .get();
        if (!bookingDoc.exists) return null;

        const bookingData = bookingDoc.data();
        const userDoc = await firestore()
          .collection('Users')
          .doc(doc.data()?.senderId)
          .get();
        const petDoc = await firestore()
          .collection('Pets')
          .doc(bookingData?.petId)
          .get();

        return {
          ...doc.data(),
          id: doc.id,
          bookingData,
          bookingId: bookingDoc.id,
          petData: petDoc.data(),
          userData: {...userDoc.data(), uid: userDoc.id},
        } as IRequest;
      });

      const bookingsData = await Promise.all(bookingsPromises);
      return bookingsData.filter(
        (booking): booking is IRequest => booking !== null,
      );
    };

    try {
      const receivedRequestsData = await getBookings('receiverId', user.uid);
      const sentRequestsData = await getBookings('senderId', user.uid);

      setBookingRequests([...receivedRequestsData, ...sentRequestsData]);
    } catch (error) {
      console.error('Error fetching data', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, [selectedTab, navigation]);

  const filteredBookingRequests = useMemo(() => {
    if (selectedTab === 'Received') {
      return bookingRequests.filter(
        booking =>
          booking.bookingData.status === 'pending' &&
          booking.senderId !== user.uid,
      );
    }
    if (selectedTab === 'Sent') {
      return bookingRequests.filter(
        booking =>
          booking.bookingData.status === 'pending' &&
          booking.senderId === user.uid,
      );
    }
    if (selectedTab === 'Accepted') {
      return bookingRequests.filter(
        booking => booking.bookingData.status === 'accepted',
      );
    }
    if (selectedTab === 'Done') {
      return bookingRequests.filter(
        booking => booking.bookingData.status === 'done',
      );
    }
    return [];
  }, [bookingRequests, selectedTab, user.uid]);

  const handleAccept = async (
    item: IBooking,
    bookingId: string,
    recipientId: string,
    fcmToken: string,
  ) => {
    try {
      setLoadingStates(prev => ({...prev, [bookingId]: true}));
      await firestore()
        .collection('Bookings')
        .doc(bookingId)
        .update({
          status: 'accepted',
        })
        .then(async () => {
          fetchRequests();
          const payload = {
            senderId: user.uid,
            senderName: user.firstName,
            receiverId: recipientId,
            receiverToken: fcmToken,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          navigation.navigate('ChatDetails', {
            recipientId: recipientId,
            serviceDone: false,
            booking: item,
          });
          await functions().httpsCallable('sendAcceptedNotifications')(payload);
        });
    } catch (error) {
      console.log('Error accepting request', error);
    } finally {
      setLoadingStates(prev => ({...prev, [bookingId]: false}));
    }
  };

  const handleMessagePress = (item: IRequest) => {
    try {
      navigation.navigate('ChatDetails', {
        recipientId: item.senderId,
        serviceDone: false,
        booking: item.bookingData,
      });
    } catch (error) {
      console.log('Error on message press', error);
    }
  };

  const handleCancel = async (request: IRequest) => {
    try {
      setLoadingStates(prev => ({...prev, [request.id]: true}));
      await firestore().collection('Requests').doc(request.id).delete();
      fetchRequests();
    } catch (error) {
      console.log('Error cancelling request', error);
    } finally {
      setLoadingStates(prev => ({...prev, [request.id]: false}));
    }
  };
  const handleCancelRequest = async (bookingId: string) => {
    try {
      setLoadingStates(prev => ({...prev, [bookingId]: true}));
      await firestore().collection('Bookings').doc(bookingId).update({
        status: 'cancelled',
      });
      fetchRequests();
    } catch (error) {
      console.log('Error canceling request', error);
    } finally {
      setLoadingStates(prev => ({...prev, [bookingId]: false}));
    }
  };

  const renderTabButton = (tabName: string) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        selectedTab === tabName && {backgroundColor: COLORS.primary},
      ]}
      onPress={() => setSelectedTab(tabName)}>
      <Text
        style={[
          styles.tabText,
          selectedTab === tabName && {color: COLORS.white},
        ]}>
        {t(tabName)}
      </Text>
    </TouchableOpacity>
  );
  const tabs = ['Received', 'Sent', 'Accepted', 'Done'];
  return (
    <View style={styles.container}>
      <View>
        <FlatList
          data={tabs}
          horizontal
          contentContainerStyle={{paddingLeft: 24, marginBottom: 20}}
          showsHorizontalScrollIndicator={false}
          renderItem={({item}) => renderTabButton(item)}
        />
      </View>
      <View style={{flex: 1}}>
        <FlatList
          data={filteredBookingRequests}
          contentContainerStyle={styles.flatListContainer}
          showsVerticalScrollIndicator={false}
          renderItem={({item, index}) => (
            <View key={index} style={styles.innerContainer}>
              <FastImage
                style={styles.imageProp}
                source={{uri: item?.petData.picture}}
              />

              <View style={styles.rowContainer}>
                <FastImage
                  source={{uri: item.userData.profilePicture}}
                  style={styles.profilePicture}
                />
                <View>
                  <Text style={styles.nameText}>
                    {item?.userData?.firstName} {item?.userData?.surName}
                  </Text>
                  <View style={styles.cityContainer}>
                    <Image
                      style={styles.icons}
                      source={Location}
                      resizeMode="contain"
                    />
                    <Text style={styles.textStyle}>{item?.userData?.city}</Text>
                  </View>
                </View>
              </View>

              {item.bookingData.status == 'pending' &&
              selectedTab !== 'Sent' ? (
                <View style={[styles.textViewStyle, styles.buttonContainer]}>
                  <TextButton
                    label={t('Cancel')}
                    containerStyle={styles.cancelButton}
                    labelStyle={{color: '#FF8C0E'}}
                    onPress={() => handleCancel(item)}
                    disabled={loadingStates[item.id]}
                    isLoading={loadingStates[item.id]}
                  />
                  <TextButton
                    label={t('Accept')}
                    containerStyle={styles.messageButton}
                    onPress={() =>
                      handleAccept(
                        item.bookingData,
                        item.bookingId,
                        item.senderId,
                        item.userData.fcmToken || '',
                      )
                    }
                    disabled={loadingStates[item.bookingId]}
                    isLoading={loadingStates[item.bookingId]}
                  />
                </View>
              ) : item.bookingData.status === 'accepted' ? (
                <TextButton
                  label={t('Message')}
                  containerStyle={styles.messageButtonStyle}
                  onPress={() => handleMessagePress(item)}
                />
              ) : selectedTab == 'Sent' ? (
                <TextButton
                  label={t('Cancel')}
                  containerStyle={styles.messageButtonStyle}
                  onPress={() => handleCancelRequest(item.bookingId)}
                  disabled={loadingStates[item.bookingId]}
                  isLoading={loadingStates[item.bookingId]}
                />
              ) : null}
            </View>
          )}
          ListEmptyComponent={() =>
            isLoading ? (
              <ActivityIndicator
                color={COLORS.primary}
                style={styles.loader}
                size={'large'}
              />
            ) : (
              <Text style={styles.emptyText}>
                {t('No booking request found')}
              </Text>
            )
          }
        />
      </View>
    </View>
  );
};

export default RequestScreen;
