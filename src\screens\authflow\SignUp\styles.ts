import {StyleSheet} from 'react-native';
import {COLORS, SIZES} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {verticalScale} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 24,
    paddingTop: SIZES.DeviceHeight > 760 ? 16 : 0,
    paddingBottom: SIZES.DeviceHeight > 760 ? 50 : 30,
  },
  emailInputFeild: {
    marginTop: 24,
  },
  nameInputFeild: {
    marginTop: 26,
  },
  margin: {
    marginTop: 14,
  },
  paragraph: {
    fontSize: 14,
    color: COLORS.primary,
    fontFamily: FONTS.SemiBold,
  },
  title: {
    marginTop: SIZES.DeviceHeight > 760 ? verticalScale(20) : verticalScale(10),
  },
  button: {
    marginTop: SIZES.DeviceHeight > 760 ? verticalScale(60) : verticalScale(40),
  },
});
