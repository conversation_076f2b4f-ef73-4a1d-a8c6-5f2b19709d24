{"name": "PetSit", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/auth": "^22.2.0", "@react-native-firebase/database": "^22.2.0", "@react-native-firebase/firestore": "^22.2.0", "@react-native-firebase/functions": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native-firebase/storage": "^22.2.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/elements": "^2.4.3", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "formik": "^2.4.6", "geofirestore": "^5.2.0", "i18next": "^25.2.1", "lodash.debounce": "^4.0.8", "lottie-react-native": "^7.2.2", "moment": "^2.30.1", "react": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.2", "react-native-calendars": "^1.1312.1", "react-native-config": "^1.5.5", "react-native-country-picker-modal": "^2.0.0", "react-native-date-picker": "^5.0.12", "react-native-dimension": "^1.0.6", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.25.0", "react-native-get-random-values": "^1.11.0", "react-native-gifted-chat": "^2.8.1", "react-native-google-mobile-ads": "^15.4.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-image-crop-picker": "^0.50.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-controller": "^1.17.4", "react-native-modal": "^14.0.0-rc.1", "react-native-permissions": "^5.4.1", "react-native-picker-select": "^9.3.1", "react-native-popup-menu": "^0.17.0", "react-native-ratings": "^8.1.0", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-size-matters": "^0.4.2", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.12.0", "react-native-svg-transformer": "^1.5.1", "react-native-system-navigation-bar": "^2.6.4", "react-native-toast-message": "^2.3.0", "react-native-tracking-transparency": "^0.1.2", "react-native-uuid": "^2.0.3", "react-native-webview": "^13.15.0", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/lodash.debounce": "^4.0.9", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}