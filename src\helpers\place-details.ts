import {GooglePlaceDetail} from 'react-native-google-places-autocomplete';

export const getPlaceComponents = (placeDetail: GooglePlaceDetail) => {
  const city = placeDetail?.address_components.find(city =>
    city.types.includes('locality'),
  );

  const country = placeDetail?.address_components.find(city =>
    city.types.includes('country'),
  );
  const province = placeDetail.address_components.find(v =>
    v.types.includes('administrative_area_level_1'),
  );
  const formattedAddress = placeDetail?.formatted_address;
  console.log(placeDetail);

  return {
    city: city?.long_name,
    country: country?.long_name,
    formattedAddress,
    province,
  };
};
