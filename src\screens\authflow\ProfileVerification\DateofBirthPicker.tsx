import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {COLORS} from '../../../themes/themes';
import DatePicker from 'react-native-date-picker';
import {FONTS} from '../../../themes/fonts';
import {useLanguage} from '../../../contexts/LanguageContext';
import {useTranslation} from 'react-i18next';

interface Props {
  label: string;
  date: Date;
  setDate: (date: Date) => void;
}

const DateofBirthPicker: React.FC<Props> = ({label, date, setDate}) => {
  const [open, setOpen] = useState(false);
  const [isDateSelected, setIsDateSelected] = useState(false);
  const currentDate = new Date();
  const {selectedLanguage} = useLanguage();
  const {t} = useTranslation();

  const minAllowedDate = new Date(
    currentDate.getFullYear() - 18,
    currentDate.getMonth(),
    currentDate.getDate(),
  );

  const formattedDate = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(date);

  const handleDateSelect = (selectedDate: Date) => {
    setOpen(false);
    setDate(selectedDate);
    setIsDateSelected(true);
  };

  return (
    <View>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity
        style={styles.innerContainer}
        onPress={() => setOpen(true)}>
        <Text
          style={[
            styles.label,
            {color: isDateSelected ? COLORS.black : COLORS.placeHolder},
          ]}>
          {formattedDate}
        </Text>
      </TouchableOpacity>
      <DatePicker
        mode="date"
        locale={selectedLanguage}
        modal
        open={open}
        date={date}
        maximumDate={minAllowedDate}
        onConfirm={handleDateSelect}
        onCancel={() => setOpen(false)}
        confirmText={t('Confirm')}
        cancelText={t('Cancel')}
        title={t('Select date of birth')}
      />
    </View>
  );
};

export default DateofBirthPicker;

const styles = StyleSheet.create({
  innerContainer: {
    height: 48,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    justifyContent: 'center',
    paddingLeft: 12,
    borderRadius: 5,
  },
  label: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
    marginTop: 14,
    marginBottom: 6,
  },
});
