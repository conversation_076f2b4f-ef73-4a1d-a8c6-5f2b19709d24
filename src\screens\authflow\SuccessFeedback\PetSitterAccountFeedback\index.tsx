import {StyleSheet, View} from 'react-native';
import React, {useContext} from 'react';
import {ActionFeedBack} from '../../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../../navigation/AuthStack';
import {AuthContext} from '../../../../../App';
import {COLORS} from '../../../../themes/themes';
import {SuccesState2} from '../../../../assets/images';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<
  AuthStackParamList,
  'PetSitterAccountFeedback'
>;

const PetSitterAccountFeedback: React.FC<Props> = ({navigation}) => {
  const {setUserId} = useContext(AuthContext);
  const {t} = useTranslation();

  setTimeout(() => {
    setUserId('petSitter');
  }, 2000);
  return (
    <View style={styles.container}>
      <ActionFeedBack
        image={SuccesState2}
        title={t('Profile Created')}
        paragraph={t('Your pet’s profile is created successfully.')}
        isWhite
      />
    </View>
  );
};

export default PetSitterAccountFeedback;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingBottom: 80,
  },
});
