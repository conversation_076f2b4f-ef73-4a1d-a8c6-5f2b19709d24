import {
  Text,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import React from 'react';
import {COLORS} from '../../../themes/themes';
import {styles} from './styles';

interface Props {
  label: string;
  containerStyle?: ViewStyle;
  onPress?: () => void;
  labelStyle?: TextStyle;
  disabled?: boolean;
  isLoading?: boolean;
  isShadow?: boolean;
}

const TextButton: React.FC<Props> = ({
  label,
  containerStyle,
  onPress,
  labelStyle,
  disabled,
  isLoading,
  isShadow,
}) => {
  return (
    <TouchableOpacity
      disabled={disabled}
      onPress={onPress}
      style={[
        styles.container,
        isShadow ? styles.shadow : null,
        {
          backgroundColor: disabled ? COLORS.Lavender : COLORS.primary,
        },
        containerStyle,
      ]}>
      {isLoading ? (
        <ActivityIndicator size={'small'} color={COLORS.primary} />
      ) : (
        <Text
          style={[
            styles.label,
            {color: disabled ? COLORS.Philippine_Gray : COLORS.white},
            labelStyle,
          ]}>
          {label}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default TextButton;
