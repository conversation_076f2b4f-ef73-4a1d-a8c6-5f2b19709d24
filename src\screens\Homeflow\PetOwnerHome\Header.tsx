import {Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Bell, Location} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {ButtonwithIcon} from '../../../components';
import FastImage from 'react-native-fast-image';
import {ImagePlaceholder, placeholderImage} from '../../../assets/images';
import {useAppSelector} from '../../../store/Store';
import {useTranslation} from 'react-i18next';
import firestore from '@react-native-firebase/firestore';
import {INotifications} from '../../../interfaces';
import {styles} from './styles';

interface Props {
  onPress: () => void;
}

const Header: React.FC<Props> = ({onPress}) => {
  const user = useAppSelector(state => state.user);
  const [unreadNotifications, SetUnreadNotifications] = useState<number>();
  const {t} = useTranslation();

  const fetchNotifications = async () => {
    try {
      const documentRef = firestore()
        .collection('Notifications')
        .where('sentTo', '==', user.uid)
        .where('isRead', '==', false);

      documentRef.onSnapshot(
        querySnapshot => {
          const _notificationsData: INotifications[] = [];
          querySnapshot.forEach(doc => {
            const notificationData = {
              id: doc.id,
              ...doc.data(),
            } as INotifications;
            _notificationsData.push(notificationData);
          });
          SetUnreadNotifications(_notificationsData.length);
        },
        error => {
          console.log('error--------', error);
        },
      );
    } catch (error) {
      console.log('An error occurred while fetching notifications:', error);
    }
  };
  useEffect(() => {
    fetchNotifications();
  });

  return (
    <View style={styles.headerContainer}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <FastImage
          source={
            user.profilePicture ? {uri: user.profilePicture} : ImagePlaceholder
          }
          style={styles.profileImage}
        />
        <View>
          <Text style={[appStyles.title2, {fontSize: 14}]}>
            {t('Hi')} {user.firstName} {user.surName}
          </Text>
          <View style={styles.userInfoContainer}>
            <Location />
            <Text style={styles.userName} numberOfLines={2}>
              {user.address}
            </Text>
          </View>
        </View>
      </View>
      <ButtonwithIcon
        icon={<Bell />}
        containerStyle={styles.bellIconContainer}
        onPress={onPress}
      />
      {unreadNotifications && unreadNotifications > 0 ? (
        <View style={styles.notificationContainer}>
          <Text style={styles.notifcationNumber}>{unreadNotifications}</Text>
        </View>
      ) : null}
    </View>
  );
};

export default Header;
