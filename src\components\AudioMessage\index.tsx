import React from 'react';
import {useTranslation} from 'react-i18next';
import {View, Text, TouchableOpacity} from 'react-native';

interface AudioMessageProps {
  audioUri: string;
  playAudio: () => void;
}

const AudioMessage: React.FC<AudioMessageProps> = ({audioUri, playAudio}) => {
  const {t} = useTranslation();

  return (
    <TouchableOpacity onPress={playAudio}>
      <View>
        <Text>{t('Audio Message - Tap to Play')}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default AudioMessage;
