import {StyleSheet} from 'react-native';
import {COLORS} from '../../themes/themes';
import {FONTS} from '../../themes/fonts';

export const styles = StyleSheet.create({
  wraper: {
    backgroundColor: '#F9F9F9',
    padding: 8,
    borderWidth: 1,
    borderColor: '#EBEBEB',
    borderRadius: 5,
    marginBottom: 12,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    width: 45,
    height: 45,
    borderRadius: 45 / 2,
    marginRight: 8,
  },
  innerContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    fontSize: 12,
    color: COLORS.Bastille,
    fontFamily: FONTS.Medium,
    marginLeft: 3,
  },
  icon: {
    borderWidth: 1,
    borderColor: COLORS.primary,
    padding: 5,
    borderRadius: 26,
    paddingRight: 6,
  },
  dateText: {
    fontSize: 13,
    color: '#8E8E8E',
    paddingTop: 8,
    fontFamily: FONTS.Medium,
  },
  name: {
    fontSize: 16,
    fontFamily: FONTS.SemiBold,
    color: COLORS.primary,
  },
  email: {
    fontSize: 12,
    color: COLORS.Bastille,
    fontFamily: FONTS.Medium,
  },
});
