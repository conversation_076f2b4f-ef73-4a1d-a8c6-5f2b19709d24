import {Keyboard, Text, TouchableWithoutFeedback, View} from 'react-native';
import React, {useEffect} from 'react';
import appStyles from '../../../themes/appStyles';
import {useTranslation} from 'react-i18next';
import {ButtonwithIcon, TextButton} from '../../../components';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {styles} from './styles';
import {BackArrow} from '../../../assets/svgIcons';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import Toast from 'react-native-toast-message';
import OtpInput from '../../../components/OtpInput';
type Props = NativeStackScreenProps<
  AuthStackParamList,
  'EmailCodeVerification'
>;
const EmailCodeVerification: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {code, email} = route.params;
  const validationSchema = Yup.object().shape({
    verificationCode: Yup.number().required(t('Verification code is required')),
  });
  const handleCodeVerification = async () => {
    if (code === formik.values.verificationCode) {
      navigation.navigate('ResetPassword1', {email: email});
    } else {
      Toast.show({
        type: 'error',
        text2: t('Verification code incorrect'),
      });
    }
  };
  const formik = useFormik({
    initialValues: {
      verificationCode: null,
    },
    onSubmit: handleCodeVerification,
    validateOnMount: true,
    validationSchema: validationSchema,
  });
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <View style={styles.container}>
        <View>
          <Text style={appStyles.title}>{t('Verification code')}</Text>
          <Text style={[appStyles.paragraph, {marginTop: 16}]}>
            {t('Enter verification code sent to your email address.')}
          </Text>
          {/* <OTPInputView
            style={styles.optView}
            pinCount={4}
            autoFocusOnLoad={false}
            keyboardAppearance="dark"
            codeInputFieldStyle={styles.underlineStyleBase}
            codeInputHighlightStyle={styles.underlineStyleHighLighted}
            onCodeFilled={code => {
              formik.handleChange('verificationCode')(code);
            }}
          /> */}
          <OtpInput
            length={4}
            onComplete={otp => {
              formik.handleChange('verificationCode')(otp);
            }}
          />
          {formik.touched.verificationCode &&
            formik.errors.verificationCode && (
              <Text style={{color: 'red'}}>
                {formik.errors.verificationCode}
              </Text>
            )}
        </View>
        <TextButton
          label={t('Verify')}
          onPress={formik.handleSubmit}
          isShadow
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default EmailCodeVerification;
