import {
  Text,
  View,
  SafeAreaView,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import React, {useContext, useState} from 'react';
import {SIZES} from '../../../themes/themes';
import Header from './Header';
import SettingAction from './SettingAction';
import {
  PETOWNERSETTINGACTION,
  PETSITTERSETTINGACTION,
} from '../../../constants';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {OwnerBottomStackParamList} from '../../../navigation/PetOwnerBottomTabs';
import {signOut, Term} from '../../../assets/images/SettingsAction';
import {AuthContext} from '../../../../App';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {verticalScale} from 'react-native-size-matters';
import {AlertModal} from '../../../components';
// import {GoogleSignin} from '@react-native-google-signin/google-signin';
import {styles} from './styles';
import {useTranslation} from 'react-i18next';
import {useAppSelector} from '../../../store/Store';
import {BannerAdComponent} from '../../../components/Admob';
import firestore from '@react-native-firebase/firestore';
import DeepLinkTester from '../../../components/DeepLinkTester';
import {serverBaseUrl} from '../../../constants';

type Props = NativeStackScreenProps<OwnerBottomStackParamList, 'Settings'>;

const Settings: React.FC<Props> = ({navigation}) => {
  const {setUserId, userId} = useContext(AuthContext);

  const [isModalVisible, setModalVisible] = useState<boolean>(false);
  // const [currentBalance, setCurrentBalance] = useState(0);
  // const [isPriceLoading, setIsPriceLoading] = useState(true);
  const {t} = useTranslation();

  const user = useAppSelector(state => state.user);
  // const adUnitId = __DEV__ ? TestIds.ADAPTIVE_BANNER : TestIds.ADAPTIVE_BANNER;
  // const getBalance = async () => {
  //   try {
  //     if (user.accountId) {
  //       const balance = await functions().httpsCallable('GetCurrenttBalance')({
  //         accountId: user.accountId,
  //       });
  //       console.log('balance', balance);

  //       setCurrentBalance(balance.data.currentBalance.pending[0].amount);
  //     }
  //   } catch (error) {
  //     console.log('Error fetching current balance', error);
  //   } finally {
  //     setIsPriceLoading(false);
  //   }
  // };
  // useEffect(() => {
  //   getBalance();
  // }, []);

  const handleSignOut = async () => {
    setModalVisible(false);
    // const isGoogleSignIn = await GoogleSignin.isSignedIn();
    // if (isGoogleSignIn) {
    //   GoogleSignin.signOut();
    // }

    setTimeout(() => {
      setUserId('');
      AsyncStorage.removeItem('UserId');
    }, 1000);
    await firestore().collection('Users').doc(user.uid).update({fcmToken: ''});
  };

  const settingAction =
    userId === 'petOwner' ? PETOWNERSETTINGACTION : PETSITTERSETTINGACTION;

  return (
    <SafeAreaView style={styles.container}>
      <BannerAdComponent />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom:
            SIZES.DeviceHeight > 760 ? verticalScale(100) : verticalScale(160),
        }}>
        <View style={styles.innerContainer}>
          <View style={styles.headerContainer}>
            <Text style={styles.title}>{t('Settings')}</Text>
            {/* {isPriceLoading ? (
              <ActivityIndicator size={'small'} color={COLORS.primary} />
            ) : (
              userId === 'petSitter' && (
                <Text style={styles.priceText}>
                  {currentBalance / 100 || 0} €
                </Text>
              )
            )} */}
          </View>
          <Header
            onEditPress={() => {
              navigation.navigate('EditProfile');
            }}
          />
          <View style={styles.divier} />
          {settingAction.map(item => {
            return (
              <SettingAction
                key={item.id}
                icon={item.icon}
                label={t(item.label)}
                onPress={() => navigation.navigate(item.onPress)}
              />
            );
          })}
          {/* temp Payment Profile */}
          <SettingAction
            icon={Term}
            label={t('Payment Profile')}
            onPress={() =>
              navigation.getParent()?.navigate('PaymentProfile', {
                uri: `${serverBaseUrl}/startOAuth`,
              })
            }
          />
          <View style={styles.divier} />
          <DeepLinkTester />
          <TouchableOpacity
            style={styles.signOutButton}
            onPress={() => {
              setModalVisible(true);
            }}>
            <Image
              source={signOut}
              resizeMode="contain"
              style={{height: 20, width: 20}}
            />
            <Text style={styles.signoutText}>{t('Sign Out')}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      <AlertModal
        isModalVisible={isModalVisible}
        onBackdropPress={() => setModalVisible(false)}
        onCancelPress={() => setModalVisible(false)}
        handleConfirm={handleSignOut}
        redLabel={t('Sign Out')}
        title={t('Sign Out')}
        paragraph={t('Are you sure you want to log out')}
      />
    </SafeAreaView>
  );
};

export default Settings;
