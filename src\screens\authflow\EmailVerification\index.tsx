import {
  Keyboard,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {
  AppText,
  ButtonwithIcon,
  FormInput,
  TextButton,
} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import {COLORS} from '../../../themes/themes';
import appStyles from '../../../themes/appStyles';
import * as Yup from 'yup';
import {useFormik} from 'formik';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {useTranslation} from 'react-i18next';
import functions from '@react-native-firebase/functions';

type Props = NativeStackScreenProps<AuthStackParamList, 'EmailVerification'>;

interface EmailForm {
  email: string;
}

const EmailVerification: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();
  const validationSchema = Yup.object().shape({
    email: Yup.string()
      .required(t('Kindly enter email for verification'))
      .email(t('Email must be a valid email'))
      .label(''),
  });
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  const formik = useFormik<EmailForm>({
    initialValues: {email: ''},
    validateOnMount: true,
    validationSchema: validationSchema,
    onSubmit: async () => {
      try {
        setIsLoading(true);
        const resp = await functions().httpsCallable('sendEmailVerification')({
          email: formik.values.email,
        });
        console.log('hey', resp);

        navigation.navigate('EmailCodeVerification', {
          code: resp.data.verificationCode,
          email: formik.values.email,
        });
      } catch (error) {
        console.log('Error sending password reset email:', error);
      } finally {
        setIsLoading(false);
      }
    },
  });

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <View style={styles.container}>
        <View>
          <Text style={appStyles.title}>{t('Forgot Password')}</Text>
          <Text style={[appStyles.paragraph, {marginTop: 16}]}>
            {t('Enter the email address for which you forgot the password.')}
          </Text>
          <FormInput
            label={t('Email Address')}
            placeholder={t('<EMAIL>')}
            placeholderTextColor={COLORS.placeHolder}
            containerStyle={styles.margin}
            onChangeText={formik.handleChange('email')}
            onBlur={() => formik.setFieldTouched('email')}
          />
          <AppText
            error={formik.errors.email}
            visible={!!formik.touched.email}
          />
        </View>
        <TextButton
          label={t('Send verification code')}
          isShadow
          onPress={formik.handleSubmit}
          isLoading={isLoading}
          disabled={isLoading || !formik.isValid}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default EmailVerification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingBottom: 80,
  },
  margin: {
    marginTop: 20,
  },
});
