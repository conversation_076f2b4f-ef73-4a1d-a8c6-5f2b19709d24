import {StyleSheet} from 'react-native';
import {COLORS} from '../../themes/themes';
import {FONTS} from '../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    width: 18,
    height: 18,
    borderRadius: 4,
    borderWidth: 1,
  },
  emptyView: {
    width: 18,
    height: 18,
    borderRadius: 2,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  text: {
    fontSize: 12,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
    marginLeft: 6,
    alignItems: 'center',
  },
  text2: {
    fontSize: 12,
    fontFamily: FONTS.Medium,
    color: COLORS.primary,
    textDecorationLine: 'underline',
  },
  errorText: {
    fontSize: 14,
    color: 'red',
    marginTop: 2,
  },
});
