import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {IUser} from '../interfaces';

const initialState: IUser = {
  firstName: '',
  surName: '',
  email: '',
  phoneNumber: '',
  address: '',
  city: '',
  country: '',
  dateOfBirth: new Date(),
  about: '',
  idFrontSide: '',
  idBackSide: '',
  userType: '',
  countryCode: '',
  profilePicture: '',
  uid: '',
  coordinates: {
    longitude: 0,
    latitude: 0,
  },
  accountId: '',
  profileStatus: 'pending',
  // customerId: '',
};

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<IUser>) => {
      return action.payload;
    },
    updateUser: (state, action: PayloadAction<Partial<IUser>>) => {
      return {...state, ...action.payload};
    },
  },
});

export const {setUser, updateUser} = userSlice.actions;

export default userSlice;
