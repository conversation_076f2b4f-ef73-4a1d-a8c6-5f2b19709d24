import {ActivityIndicator, FlatList, Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {COLORS} from '../../../themes/themes';
import Header from './Header';
import {
  ButtonwithIcon,
  ReviewCard,
  SearchBar,
  TextButtonwithIcon,
} from '../../../components';
import {BackArrow, Star} from '../../../assets/svgIcons';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import appStyles from '../../../themes/appStyles';
import firestore from '@react-native-firebase/firestore';
import moment from 'moment';
import {useAppSelector} from '../../../store/Store';
import {useTranslation} from 'react-i18next';
import {IUser} from '../../../interfaces';
import {styles} from './styles';
type Props = NativeStackScreenProps<PetOwnerParamList, 'MyReviews'>;

export interface IReview {
  key: string;
  user: IUser;
  rating: number;
  reviewDate: Date;
}

const MyReviews: React.FC<Props> = ({navigation}) => {
  const user = useAppSelector(state => state.user);
  const [isLoading, setIsLoading] = useState(true);
  const [reviews, setReviews] = useState<IReview[]>([]);
  const [averageRating, setAverageRating] = useState(0);
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [enteredText, setEnteredText] = useState('');
  const [filteredReviews, setFilteredReviews] = useState<IReview[]>([]);
  const {t} = useTranslation();

  const getReviews = async () => {
    try {
      let querySnapshot;
      if (user.userType !== 'petOwner') {
        querySnapshot = await firestore()
          .collection('Reviews')
          .where('sentBy', '==', user.uid)
          .orderBy('reviewDate', 'desc')
          .get();
      } else {
        querySnapshot = await firestore()
          .collection('Reviews')
          .where('sentTo', '==', user.uid)
          .orderBy('reviewDate', 'desc')
          .get();
      }

      const reviewData = querySnapshot.docs.map(
        doc =>
          ({
            ...doc.data(),
            key: doc.id,
          } as IReview),
      );

      setReviews(reviewData);
      setFilteredReviews(reviewData);
      if (reviewData.length > 0) {
        const totalRating = reviewData.reduce(
          (acc, review) => acc + review.rating,
          0,
        );
        const avgRating = totalRating / reviewData.length;
        setAverageRating(avgRating);
      } else {
        setAverageRating(0);
      }
    } catch (error) {
      console.log('Error fetching reviews:', error);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);
  useEffect(() => {
    getReviews();
  }, []);

  const handleSearchPress = () => {
    setShowSearchBar(prevShowSearchBar => !prevShowSearchBar);
  };
  const additionalTextInputProps = {
    onChangeText: (text: string) => {
      setEnteredText(text);
      searchFilterFunction();
    },
    enterKeyHint: 'search',
  };

  const searchFilterFunction = () => {
    if (enteredText) {
      const newData = reviews?.filter(function (item) {
        const itemData = item?.user?.name ? item?.user?.name.toUpperCase() : '';
        const textData = enteredText.toUpperCase();
        return itemData.indexOf(textData) > -1;
      });

      setFilteredReviews(newData);
    } else {
      setFilteredReviews(reviews);
    }
  };
  useEffect(() => {
    searchFilterFunction();
  }, [enteredText]);
  const reviewLength = reviews.length <= 1 ? t('review') : t('reviews');
  return (
    <View style={styles.container}>
      <Header onPress={handleSearchPress} />
      {showSearchBar ? (
        <SearchBar
          containerStyle={{marginTop: 20}}
          {...additionalTextInputProps}
        />
      ) : null}
      <View style={styles.ratingContainer}>
        {user.userType == 'petSitter' ? (
          <TextButtonwithIcon
            label={averageRating.toString()}
            labelStyle={styles.label}
            leftIcon={<Star width={18} height={18} />}
            disabled
          />
        ) : (
          <View />
        )}
        <Text>
          ({filteredReviews.length} {reviewLength})
        </Text>
      </View>
      {isLoading ? (
        <ActivityIndicator
          size="large"
          color={COLORS.primary}
          style={{marginTop: 40}}
        />
      ) : (
        <FlatList
          data={filteredReviews}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainerStyle}
          renderItem={({item}) => {
            return (
              <ReviewCard
                name={item?.user?.name}
                email={item?.user?.email}
                rating={item?.rating}
                date={moment(item?.reviewDate.toDate()).format('DD/MM/YYYY')}
                picture={item.user?.profilePicture || ''}
              />
            );
          }}
          ListEmptyComponent={
            <Text style={styles.emptyText}>{t('No reviews yet')}</Text>
          }
        />
      )}
    </View>
  );
};

export default MyReviews;
