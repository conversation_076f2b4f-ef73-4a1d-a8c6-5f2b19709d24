import {StyleSheet, View} from 'react-native';
import React from 'react';
import {ActionFeedBack} from '../../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../../navigation/AuthStack';
import {COLORS} from '../../../../themes/themes';
import {SuccesState} from '../../../../assets/images';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<
  AuthStackParamList,
  'PasswordSuccessFeedback'
>;

const PasswordSuccessFeedback: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  return (
    <View style={styles.container}>
      <ActionFeedBack
        image={SuccesState}
        title={t('Password Recovered')}
        paragraph={t(
          'The password has been successfully recovered, you can log in back with a new password',
        )}
        buttonTitle={t('Back to Sign in')}
        showActionButton
        onPress={() => navigation.navigate('Login')}
      />
    </View>
  );
};

export default PasswordSuccessFeedback;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.primary,
    paddingBottom: 80,
  },
});
