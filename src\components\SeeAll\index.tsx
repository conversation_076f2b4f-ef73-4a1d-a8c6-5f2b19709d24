import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import appStyles from '../../themes/appStyles';
import {FONTS} from '../../themes/fonts';
import {COLORS} from '../../themes/themes';
import {useTranslation} from 'react-i18next';

interface Props {
  heading: string;
  onPress: () => void;
}

const SeeAll: React.FC<Props> = ({heading, onPress}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <Text style={appStyles.title2}>{heading}</Text>
      <TouchableOpacity onPress={onPress}>
        <Text style={styles.label}>{t('See all')}</Text>
      </TouchableOpacity>
    </View>
  );
};

export default SeeAll;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 26,
  },
  label: {
    fontSize: 12,
    fontFamily: FONTS.Medium,
    color: COLORS.primary,
    textDecorationLine: 'underline',
  },
});
