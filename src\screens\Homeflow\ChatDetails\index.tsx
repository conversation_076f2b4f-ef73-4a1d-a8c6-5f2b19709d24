import {
  ActivityIndicator,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
  useTransition,
} from 'react';
import {AlertModal, ButtonwithIcon} from '../../../components';
import {Attachment, BackArrow} from '../../../assets/svgIcons';
import Header from './Header';
import {
  Bubble,
  GiftedChat,
  IMessage,
  InputToolbar,
  Message,
  Send,
  Time,
} from 'react-native-gifted-chat';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import useChat from '../../../hooks/useChat';
import storage from '@react-native-firebase/storage';
import {styles} from './styles';
import ImageCropPicker from 'react-native-image-crop-picker';
import {COLORS} from '../../../themes/themes';
import {useAppSelector} from '../../../store/Store';
import {Camera, gallary} from '../../../assets/images';
import Modal from 'react-native-modal';
import Toast from 'react-native-toast-message';
import {AuthContext} from '../../../../App';
import {useTranslation} from 'react-i18next';
// import {usePaymentSheet} from '@stripe/stripe-react-native';
import functions from '@react-native-firebase/functions';
import firestore from '@react-native-firebase/firestore';
import {IUser} from '../../../interfaces';
// import {customAppearance} from '../../../constants/PaymentSheetCustomAppearence';

type Props = NativeStackScreenProps<PetOwnerParamList, 'ChatDetails'>;

const ChatDetails: React.FC<Props> = ({navigation, route}) => {
  const {onSend, messages, getAllMessages, isLoading, markReadMessages} =
    useChat();
  // const {initPaymentSheet, presentPaymentSheet, loading} = usePaymentSheet();

  const user = useAppSelector(state => state.user);
  const [recipient, setRecipient] = useState<IUser>();
  const [imageUploading, setImageUploading] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [imageLocalPath, setImageLocalPath] = useState<string>('');
  const [status, setStatus] = useState(false);
  const [newRating, setNewRating] = useState(0);
  const [isBtnLoading, setIsLoading] = useState(false);
  const [isReviewModalVisible, setReviewModalVisible] = useState(false);
  const [isPending, startTransition] = useTransition();
  const {onlineStatus, userId} = useContext(AuthContext);
  const {t} = useTranslation();

  const {recipientId, serviceDone, booking} = route.params;
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  const debounceMarkReadMessages = useCallback(() => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
    debounceTimeout.current = setTimeout(() => {
      startTransition(() => {
        markReadMessages(recipientId);
      });
    }, 2000);
  }, [recipientId, markReadMessages, startTransition]);

  useEffect(() => {
    debounceMarkReadMessages();
  }, [messages, debounceMarkReadMessages]);

  useEffect(() => {
    if (onlineStatus && onlineStatus[recipientId]) {
      setStatus(true);
    }
  }, [onlineStatus, recipientId]);

  // const initializePaymentSheet = async () => {
  //   const response = await functions().httpsCallable('retrieveAccount')({
  //     accountId: recipient?.accountId,
  //   });
  //   if (
  //     response.data.capabilities.transfers === 'inactive' ||
  //     response.data.capabilities.payouts === 'inactive' ||
  //     response.data.requirements.currently_due.length > 0
  //   ) {
  //     Toast.show({
  //       type: 'error',
  //       text1: t('Error'),
  //       text2: t('Receivers onboarding is not complete'),
  //     });
  //   } else {
  //     const {paymentIntent, ephemeralKey, customer} =
  //       await fetchPaymentSheetParams();

  //     const {error} = await initPaymentSheet({
  //       customerId: customer,
  //       customerEphemeralKeySecret: ephemeralKey,
  //       paymentIntentClientSecret: paymentIntent,
  //       merchantDisplayName: `${user.firstName} ${user.surName}`,
  //       allowsDelayedPaymentMethods: true,
  //       appearance: customAppearance,
  //       returnURL: 'stripe-example://stripe-redirect',
  //     });

  //     if (error) {
  //       console.log('Error in initializing payment sheet', error);
  //       Toast.show({
  //         type: 'error',
  //         text2: t('Error initializing payment sheet. try again later'),
  //       });
  //     } else {
  //       payToSeller();
  //     }
  //   }
  // };

  // const fetchPaymentSheetParams = async () => {
  //   try {
  //     if (!booking) throw new Error(t('booking not available'));
  //     if (!user.customerId) throw new Error(t('No customer found'));

  //     const payload = {
  //       customerId: user.customerId,
  //       accountId: recipient?.accountId,
  //       amount: parseInt(booking.price) * 100,
  //       currency: 'usd',
  //     };

  //     const res = await functions().httpsCallable('payToSeller')(payload);

  //     const {paymentIntent, ephemeralKey, customer} = res.data;

  //     return {
  //       paymentIntent,
  //       ephemeralKey,
  //       customer,
  //     };
  //   } catch (error: any) {
  //     console.log('Error creating Payment Intent:', error.message);
  //     Toast.show({
  //       type: 'error',
  //       text2: error.message,
  //     });
  //     throw error;
  //   }
  // };

  // const payToSeller = async () => {
  //   const {error} = await presentPaymentSheet();
  //   if (error) {
  //     console.error(`Error code: ${error.code}`, error.message);
  //     throw error;
  //   } else {
  //     setReviewModalVisible(true);
  //   }
  // };

  const handleSubmitRating = async () => {
    try {
      setIsLoading(true);
      const existingReviewSnapshot = await firestore()
        .collection('Reviews')
        .where('sentTo', '==', recipientId)
        .where('sentBy', '==', user.uid)
        .get();

      if (!existingReviewSnapshot.empty) {
        const existingReviewId = existingReviewSnapshot.docs[0].id;
        await firestore().collection('Reviews').doc(existingReviewId).update({
          rating: newRating,
          reviewDate: new Date(),
        });
      } else {
        const payload = {
          user: user,
          sentTo: recipientId,
          sentBy: user.uid,
          rating: newRating,
          reviewDate: new Date(),
        };
        await firestore().collection('Reviews').doc().set(payload);
      }

      const querySnapshot = await firestore()
        .collection('Reviews')
        .where('sentTo', '==', recipientId)
        .get();

      const reviewData = querySnapshot.docs.map(doc => ({
        ...doc.data(),
        key: doc.id,
      }));

      const totalRatings = reviewData.reduce(
        (acc, review) => acc + review.rating,
        0,
      );

      const averageRating =
        reviewData.length > 0 ? totalRatings / reviewData.length : 0;

      await firestore().collection('Users').doc(recipientId).update({
        rating: averageRating,
      });

      if (recipientId) {
        const uid =
          recipientId > user.uid
            ? user.uid + '-' + recipientId
            : recipientId + '-' + user.uid;

        await firestore().collection('ServiceDone').add({
          petOwner: user,
          petSitter: recipient,
          timeStamp: new Date(),
        });
        await functions().httpsCallable('sendServiceDoneNotifications')({
          sender: user,
          receiver: recipient,
          uid: uid,
        });

        if (booking?.id) {
          await firestore().collection('Bookings').doc(booking.id).update({
            status: 'done',
          });
        }
        await firestore()
          .collection('Conversations')
          .doc(uid)
          .update({serviceDone: true})
          .then(() => {
            Toast.show({
              type: 'success',
              text1: t('Success'),
              text2: t('Rating submitted'),
            });
            setModalVisible(false);
            navigation.goBack();
          });
      }
    } catch (error) {
      console.log('Error adding review:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecipent = async () => {
    const recipent = await firestore()
      .collection('Users')
      .doc(recipientId)
      .get();
    setRecipient(recipent.data() as IUser);
  };

  useEffect(() => {
    fetchRecipent();
  }, []);

  const handlePickImage = () => {
    ImageCropPicker.openPicker({
      width: 300,
      height: 400,
      cropping: true,
      maxFiles: 1,
      mediaType: 'photo',
    }).then(image => {
      setModalVisible(false);
      setImageLocalPath(image.path);
    });
  };

  const handleCameraImage = () => {
    ImageCropPicker.openCamera({
      width: 300,
      height: 400,
      cropping: true,
      mediaType: 'photo',
    }).then(image => {
      console.log(image);
      setModalVisible(false);
      setImageLocalPath(image.path);
    });
  };

  const handleUploadImage = async (imagePath: string) => {
    let filename = imagePath.substring(imagePath.lastIndexOf('/') + 1);

    const reference = storage().ref('ChatImages/' + filename);
    const pathToFile = imagePath;
    await reference.putFile(pathToFile);
    return await storage()
      .ref('ChatImages/' + filename)
      .getDownloadURL();
  };

  const renderChatFooter = useCallback(() => {
    if (imageLocalPath) {
      return (
        <View style={styles.chatFooter}>
          <Image
            resizeMode="contain"
            source={{uri: imageLocalPath || ''}}
            style={{height: 75, width: 75}}
          />
          {imageUploading ? (
            <Text style={{color: COLORS.primary, alignSelf: 'center'}}>
              {t('Uploading...')}
            </Text>
          ) : null}
          <TouchableOpacity
            onPress={() => setImageLocalPath('')}
            style={styles.buttonFooterChatImg}>
            <Text style={styles.textFooterChat}>X</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return null;
  }, [imageLocalPath, imageUploading]);

  const onSendMessage = async (newMessages: IMessage[]) => {
    try {
      const [messageToSend] = newMessages;
      if (imageLocalPath) {
        setImageUploading(true);
        const imageUrl = await handleUploadImage(imageLocalPath);

        setImageUploading(false);
        const newMessage = {
          ...messageToSend,
          image: imageUrl,
        };
        // @TODO: verify that booking is always available here
        onSend([newMessage], recipientId, booking);
        setImageLocalPath('');
      } else {
        // @TODO: verify that booking is always available here
        onSend(newMessages, recipientId, booking);
      }
    } catch (error) {
      setImageUploading(false);
      console.log('error while sending message', error);
      Toast.show({
        text1: t('Error'),
        text2: t('Could not send attachment at this time'),
        type: 'error',
      });
    }
  };

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);
  useEffect(() => {
    getAllMessages(recipientId);
  }, [recipientId]);
  // useEffect(() => {
  //   markReadMessages(recipientId);
  // }, [messages]);

  return (
    <View style={styles.container}>
      <Header
        isOnline={status}
        recipientId={recipientId}
        serviceDone={serviceDone && !!recipient}
        handleServiceDonePress={() => setModalVisible(true)}
        isBlocked={recipient?.isBlocked}
      />
      {isLoading ? (
        <ActivityIndicator
          size={'large'}
          color={COLORS.primary}
          style={{marginTop: 100}}
        />
      ) : (
        <GiftedChat
          messages={messages}
          onSend={newMessages => onSendMessage(newMessages)}
          user={{
            _id: user.uid,
          }}
          listViewProps={{showsVerticalScrollIndicator: false}}
          alwaysShowSend
          showUserAvatar={false}
          showAvatarForEveryMessage={false}
          placeholder={t('Your message')}
          messagesContainerStyle={
            !messages?.length &&
            Platform.select({
              ios: {transform: [{scaleY: -1}]},
              android: {transform: [{scaleY: -1}, {scaleX: -1}]},
            })
          }
          renderBubble={props => {
            return (
              <Bubble
                {...props}
                textStyle={{
                  left: styles.leftTextStyle,
                  right: styles.rightTextStyle,
                }}
                wrapperStyle={{
                  right: styles.rightWrapperStyle,
                  left: styles.leftWrapperStyle,
                }}
              />
            );
          }}
          renderChatFooter={renderChatFooter}
          renderInputToolbar={props => {
            return serviceDone || recipient?.isBlocked ? null : (
              <InputToolbar
                {...props}
                containerStyle={styles.inputToolbarContainer}
                primaryStyle={{alignItems: 'center'}}
              />
            );
          }}
          renderSend={props => {
            const isSendDisabled = !props.text && !imageLocalPath;
            return serviceDone || recipient?.isBlocked ? null : (
              <Send
                {...props}
                disabled={isSendDisabled}
                containerStyle={styles.containerStyle}>
                <TouchableOpacity
                  disabled={isSendDisabled}
                  onPress={() => {
                    const trimmedText = props.text?.trim() ?? '';
                    if (props.onSend) {
                      props.onSend({text: trimmedText}, true);
                    }
                  }}>
                  <Text
                    style={[
                      styles.sendText,
                      {
                        color: isSendDisabled
                          ? COLORS.Philippine_Gray
                          : COLORS.primary,
                      },
                    ]}>
                    {t('Send')}
                  </Text>
                </TouchableOpacity>
              </Send>
            );
          }}
          renderActions={() => {
            return serviceDone || recipient?.isBlocked ? null : (
              <TouchableOpacity
                style={styles.rightAction}
                onPress={() => setModalVisible(true)}>
                <Attachment height={18} width={18} color={COLORS.darkGray} />
              </TouchableOpacity>
            );
          }}
          renderTime={props => (
            <Time
              {...props}
              timeTextStyle={{
                left: styles.leftTimeTextStyle,
                right: styles.rightTimeTextStyle,
              }}
            />
          )}
          renderMessage={props => {
            return (
              <Message
                {...props}
                linkStyle={{
                  right: {
                    color: 'black',
                  },
                  left: {
                    color: 'black',
                  },
                }}
              />
            );
          }}
          renderChatEmpty={() => (
            <Text style={styles.emptyChat}>
              {t('Send a message to start chat')}
            </Text>
          )}
          renderFooter={() => {
            return serviceDone ? (
              <Text style={styles.footerText}>
                {t('you cannot send message when service is done')}
              </Text>
            ) : recipient?.isBlocked ? (
              <Text style={styles.footerText}>
                {t('This user has been blocked')}
              </Text>
            ) : null;
          }}
        />
      )}
      <Modal
        isVisible={isModalVisible}
        onBackdropPress={() => setModalVisible(false)}>
        <View style={styles.outerButtonContainer}>
          <Text style={[styles.title, {alignSelf: 'center'}]}>
            {t('Upload')}
          </Text>
          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={styles.innerButtonContainer}
              onPress={handleCameraImage}>
              <Image source={Camera} style={styles.cameraIcon} />
              <Text style={styles.text}>{t('Camera')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.innerButtonContainer}
              onPress={handlePickImage}>
              <Image source={gallary} style={styles.cameraIcon} />
              <Text style={styles.text}>{t('Gallery')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <AlertModal
        isModalVisible={isReviewModalVisible}
        onBackdropPress={() => setReviewModalVisible(false)}
        onCancelPress={() => setReviewModalVisible(false)}
        handleConfirm={handleSubmitRating}
        redLabel={t('Submit')}
        title={t('Rate Service')}
        paragraph={t('Would you like to rate the pet owner?')}
        isRating
        onRatingCompleted={setNewRating}
        isLoading={isBtnLoading}
      />
    </View>
  );
};

export default ChatDetails;
