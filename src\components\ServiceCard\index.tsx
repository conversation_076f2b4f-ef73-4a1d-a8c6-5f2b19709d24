import {StyleSheet, Text, View, ViewStyle} from 'react-native';
import React from 'react';
import {COLORS} from '../../themes/themes';
import ButtonwithIcon from '../Buttons/ButtonwithIcon';
import {Plus2} from '../../assets/svgIcons';

interface Props {
  serviceName: string;
  onPress: () => void;
  containerStyle?: ViewStyle;
}

const ServiceCard: React.FC<Props> = ({
  serviceName,
  onPress,
  containerStyle,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.innerContainer}>
        <Text style={{flex: 1}}>{serviceName}</Text>
        <View style={styles.priceContainer}>
          <ButtonwithIcon icon={<Plus2 />} onPress={onPress} />
        </View>
      </View>
      <View style={styles.divider}></View>
    </View>
  );
};

export default ServiceCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  divider: {
    height: 1,
    width: '100%',
    backgroundColor: '#EEEEEE',
    marginVertical: 12,
  },
  priceContainer: {
    flexDirection: 'row',
  },
  priceText: {
    marginRight: 12,
  },
});
