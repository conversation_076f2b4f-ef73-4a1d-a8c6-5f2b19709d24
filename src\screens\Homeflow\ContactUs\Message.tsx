import {
  StyleSheet,
  Text,
  TextInput,
  View,
  NativeSyntheticEvent,
  TextInputFocusEventData,
} from 'react-native';
import React from 'react';
import {COLORS} from '../../../themes/themes';

interface Props {
  onChangeText?: (text: string) => void;
  onBlur?: (e: NativeSyntheticEvent<TextInputFocusEventData>) => void;
  isPassword?: boolean;
  placeholder: string;
  placeholderTextColor: string;
  label: string;
  value: string;
}

const Message: React.FC<Props> = ({
  onChangeText,
  onBlur,
  placeholder,
  placeholderTextColor,
  label,
  value,
  ...rest
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.innerContainer}>
        <TextInput
          placeholder={placeholder}
          placeholderTextColor={placeholderTextColor}
          onChangeText={onChangeText}
          onBlur={onBlur}
          multiline={true}
          style={styles.inputStyles}
          textAlignVertical="top"
          value={value}
          {...rest}
        />
      </View>
    </View>
  );
};

export default Message;

const styles = StyleSheet.create({
  container: {
    marginTop: 14,
  },
  innerContainer: {
    height: 130,
    backgroundColor: COLORS.white,
    borderColor: COLORS.lightGray,
    borderWidth: 1,
    marginTop: 8,
    paddingHorizontal: 10,
  },
  label: {
    fontSize: 14,
    color: COLORS.Bastille,
  },
  inputStyles: {
    padding: 10,
    marginVertical: 10,
    flex: 1,
  },
});
