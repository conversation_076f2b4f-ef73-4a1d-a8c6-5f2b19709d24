import {StyleSheet} from 'react-native';
import {COLORS} from '../../themes/themes';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../themes/fonts';

export const styles = StyleSheet.create({
  innerContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: COLORS.lightGray,
    height: verticalScale(48),
    paddingHorizontal: scale(12),
  },
  textInputStyles: {
    flex: 1,
    fontSize: moderateScale(13),
    height: verticalScale(48),
    color: COLORS.black,
  },
  label: {
    fontSize: moderateScale(14),
    color: COLORS.Bastille,
    marginBottom: verticalScale(6),
    fontFamily: FONTS.Medium,
  },
});
