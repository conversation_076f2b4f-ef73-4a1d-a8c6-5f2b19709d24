import {StyleSheet, View} from 'react-native';
import React from 'react';
import {ActionFeedBack} from '../../../components';
import {SuccesState2} from '../../../assets/images';
import {COLORS} from '../../../themes/themes';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {useTranslation} from 'react-i18next';
import {AuthStackParamList} from '../../../navigation/AuthStack';
type Props = NativeStackScreenProps<AuthStackParamList, 'ProfileCreated'>;

const ProfileCreated: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  return (
    <View style={styles.container}>
      <ActionFeedBack
        image={SuccesState2}
        title={t('Profile Created')}
        paragraph={t('Your pet’s profile is created successfully.')}
        isWhite
        showActionButton={false}
        showSkipButton={false}
        onPress={() => navigation.navigate('Login')}
        onSkipPress={() => navigation.navigate('PetProfile', {})}
      />
    </View>
  );
};

export default ProfileCreated;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
});
