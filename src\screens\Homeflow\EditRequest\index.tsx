import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {
  AppText,
  ButtonwithIcon,
  FormInput,
  ServiceCard,
  TextButton,
} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import DropDown from './DropDown';
import PetModal from './PetModal';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {Services} from '../../../constants';
import ServiceModal from './ServiceModal';
import DatePicker from './DatePicker';
import firestore from '@react-native-firebase/firestore';
import {useAppSelector} from '../../../store/Store';
import {IPet} from '../../../interfaces';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import * as geofirestore from 'geofirestore';

type Props = NativeStackScreenProps<PetOwnerParamList, 'EditRequest'>;

interface DateType {
  startDate: string;
  endDate: string;
}
interface AddServiceForm {
  petName: string;
  serviceName: string[];
  date: DateType;
  price: string;
}

const EditRequest: React.FC<Props> = ({navigation, route}) => {
  const [isPetModalVisible, setPetModalVisible] = useState(false);
  const [isServiceModalVisible, setServiceModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [pets, setPets] = useState<IPet[]>();
  const [selectedPetId, setSelectedPetId] = useState('');
  const user = useAppSelector(state => state.user);
  const {t} = useTranslation();

  const validationSchema = Yup.object().shape({
    petName: Yup.string().required(t('Enter your pet name')),
    serviceName: Yup.array().required(t('Enter required services')),
    date: Yup.object()
      .shape({
        startDate: Yup.string().required(),
        endDate: Yup.string().required(),
      })
      .required(t('Enter date')),
    price: Yup.string().required(t('Enter prices')),
  });
  const {isEdit, bookingData} = route.params;
  const fetchPetData = () => {
    try {
      const petData = firestore()
        .collection('Pets')
        .where('userId', '==', user.uid);
      petData.onSnapshot(
        snapshot => {
          const _petData: IPet[] = [];
          snapshot.forEach(result => {
            _petData.push({
              ...result.data(),
              id: result.id,
            } as IPet);
          });
          setPets(_petData);
        },
        error => {
          console.log('error ', error);
        },
      );
    } catch (error) {
      console.log('Error fetching pet data:', error);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchPetData();
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);

  const handleCreate = async () => {
    try {
      setIsLoading(true);
      const firestoreApp = firestore();
      const GeoFirestore = geofirestore.initializeApp(firestoreApp);
      const geocollection = GeoFirestore.collection('Bookings');

      const payload = {
        petId: selectedPetId,
        userId: user.uid,
        bookingDate: formik.values.date,
        services: formik.values.serviceName,
        price: formik.values.price,
        createdAt: new Date(),
        updatedAt: new Date(),
        coordinates: user.coordinates,
        status: 'pending',
      };

      if (isEdit) {
        await geocollection.doc(bookingData.key).update(payload);
      } else {
        await geocollection.add(payload);
      }
      navigation.goBack();
    } catch (error) {
      console.log('Error updating document:', error);
      Toast.show({
        type: 'error',
        text2: t('Sorry, could not create booking at the moment'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<AddServiceForm>({
    initialValues: {
      petName: isEdit ? bookingData.petType : '',
      serviceName: isEdit ? bookingData.services : [],
      date: isEdit
        ? {
            startDate: bookingData.bookingDate.startDate,
            endDate: bookingData.bookingDate.endDate,
          }
        : {startDate: null, endDate: null},
      price: isEdit ? bookingData.price : '',
    },
    onSubmit: handleCreate,
    validateOnMount: true,
    validationSchema: validationSchema,
  });
  const {petName, serviceName} = formik.values;

  const handleRemoveService = (removingService: string) => {
    const updatedServices = formik.values.serviceName.filter(
      item => item != removingService,
    );
    formik.setFieldValue('serviceName', updatedServices);
  };
  const renderService = ({item}: {item: string}) => (
    <ServiceCard serviceName={item} onPress={() => handleRemoveService(item)} />
  );

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={{flexGrow: 1}}
        showsVerticalScrollIndicator={false}>
        <View style={{flex: 1, justifyContent: 'space-between'}}>
          <View>
            <Text style={styles.title}>
              {isEdit ? t('Edit Booking Details') : t('Create New Booking')}
            </Text>
            <DropDown
              label={t('Pet')}
              placeholder={petName ? petName : t('Pet')}
              color={petName ? '#000' : COLORS.placeHolder}
              onPress={() => setPetModalVisible(true)}
            />
            <AppText
              error={formik.errors.petName}
              visible={!!formik.touched.petName}
            />
            <DatePicker
              date={formik.values.date}
              setSelectedDate={(startDate, endDate) => {
                formik.setFieldValue('date', {
                  startDate: startDate,
                  endDate: endDate,
                });
              }}
            />
            <AppText
              error={formik.errors.date?.startDate}
              visible={!!formik.touched.date}
            />
            <DropDown
              label={t('Service')}
              placeholder={t('Add services')}
              color={serviceName ? '#000' : COLORS.placeHolder}
              onPress={() => setServiceModalVisible(true)}
            />
            <AppText
              error={formik.errors.serviceName?.toString()}
              visible={!!formik.touched.serviceName}
            />

            {formik.values.serviceName ? (
              <View style={{marginTop: 16}}>
                {formik.values.serviceName.map((service, index) => (
                  <View key={index}>{renderService({item: service})}</View>
                ))}
              </View>
            ) : null}

            <FormInput
              placeholder={t('Enter price for service')}
              label={t('Total price')}
              containerStyle={{marginTop: 16, marginBottom: 20}}
              keyboardType="numeric"
              value={formik.values.price}
              onChangeText={formik.handleChange('price')}
              onBlur={formik.handleBlur('price')}
            />
            <AppText
              error={formik.errors.price}
              visible={!!formik.touched.price}
            />
          </View>
          <TextButton
            label={isEdit ? t('Save') : t('Create')}
            onPress={formik.handleSubmit}
            isLoading={isLoading}
            disabled={
              isLoading || !formik.isValid || !formik.values.serviceName
            }
            containerStyle={{marginBottom: 50}}
          />
        </View>
        <PetModal
          isModalVisible={isPetModalVisible}
          setModalVisible={setPetModalVisible}
          data={pets}
          setPetname={(value, id) => {
            formik.setFieldValue('petName', value);
            setSelectedPetId(id);
          }}
        />
        <ServiceModal
          isModalVisible={isServiceModalVisible}
          setModalVisible={setServiceModalVisible}
          data={Services}
          setServiceName={value => {
            if (!formik.values.serviceName.includes(value)) {
              formik.setFieldValue('serviceName', [
                ...formik.values.serviceName,
                value,
              ]);
            }
          }}
        />
      </ScrollView>
    </View>
  );
};

export default EditRequest;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 18,
    fontFamily: FONTS.Bold,
    color: COLORS.Bastille,
  },
});
