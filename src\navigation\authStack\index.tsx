import React, {useContext} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {
  JoinUs,
  Login,
  SignUp,
  EmailVerification,
  CodeVerification,
  ProfileVerification,
  UploadProfile,
  PasswordSuccessFeedback,
  Subscription,
  AddNewCard,
  PetProfile,
  AddService,
  ProfileCreated,
  PetSitterAccountFeedback,
  PetOwnerAccountFeedback,
  Onboard,
} from '../../screens/authflow';
import {AuthContext} from '../../../App';
import Splash from '../../screens/authflow/SplashScreen';
import {IPet, IService} from '../../interfaces/IPet';
import {PrivacyPolicy, TermofUse} from '../../screens/Homeflow';
import SelectLanguage from '../../screens/authflow/SelectLanguage';
import {useLanguage} from '../../contexts/LanguageContext';
import EmailCodeVerification from '../../screens/authflow/EmailCodeVerification';
import ResetPassword1 from '../../screens/authflow/ResetPassword';
import AboutMe from '../../screens/authflow/AboutMe';
import VerifyEmailScreen from '../../screens/authflow/SignUp/VerifyEmail';

export type AuthStackParamList = {
  Onboard: undefined;
  Login: undefined;
  SplashScreen: undefined;
  SignUp: undefined;
  // this screen will be used for forgot password
  EmailVerification: undefined;
  // this screen will be used for email verification after signup
  VerifyEmail: undefined;
  CodeVerification: {uid: string; phoneNumber: string};
  ResetPassword1: {email: string};
  ProfileVerification: {
    uid: string;
    email: string;
    firstName: string;
    surName: string;
  };
  UploadProfile: {uid: string};
  UploadIDBackSide: undefined;
  UploadIDFrontSide: undefined;
  Subscription: {uid: string};
  JoinUs: undefined;
  PaymentMethod: undefined;
  AddNewCard: undefined;
  PetProfile: {uid?: string; petData?: IPet; isAuthFlow?: boolean};
  AddAuthService: {addservice: (service: string) => void};
  ProfileCreated: undefined;
  PasswordSuccessFeedback: undefined;
  PetSitterAccountFeedback: undefined;
  PetOwnerAccountFeedback: undefined;
  PrivacyPolicy: undefined;
  TermofUse: undefined;
  SelectLanguage: undefined;
  EmailCodeVerification: {code: number; email: string};
  AboutMe: {uid: string; phoneNumber: string};
};

const AuthStack = createNativeStackNavigator<AuthStackParamList>();

const AuthStackNavigator = () => {
  const {firstLaunch} = useContext(AuthContext);
  const {selectedLanguage} = useLanguage();

  return (
    <AuthStack.Navigator
      screenOptions={{headerShown: false, headerShadowVisible: false}}>
      {firstLaunch && (
        <AuthStack.Screen
          name={'SelectLanguage'}
          component={SelectLanguage}
          options={{headerShown: false}}
        />
      )}
      {firstLaunch && (
        <AuthStack.Screen
          name={'Onboard'}
          component={Onboard}
          options={{headerShown: false}}
        />
      )}
      <AuthStack.Screen name={'JoinUs'} component={JoinUs} />
      <AuthStack.Screen
        name={'Login'}
        component={Login}
        options={{
          headerShown: true,
          headerTitle: '',
        }}
      />
      <AuthStack.Screen
        name={'SplashScreen'}
        component={Splash}
        options={{headerShown: false, headerTitle: ''}}
      />
      <AuthStack.Screen
        name={'SignUp'}
        component={SignUp}
        options={{headerShown: true, headerTitle: ''}}
      />

      <AuthStack.Screen
        name={'EmailVerification'}
        component={EmailVerification}
        options={{headerShown: true, headerTitle: ''}}
      />

      <AuthStack.Screen
        name={'EmailCodeVerification'}
        component={EmailCodeVerification}
        options={{headerShown: true, headerTitle: ''}}
      />
      <AuthStack.Screen
        name={'AboutMe'}
        component={AboutMe}
        options={{headerShown: true, headerTitle: 'About'}}
      />
      <AuthStack.Screen
        name={'CodeVerification'}
        component={CodeVerification}
        options={{headerShown: true, headerTitle: ''}}
      />
      <AuthStack.Screen
        name={'ResetPassword1'}
        component={ResetPassword1}
        options={{headerShown: true, headerTitle: ''}}
      />
      <AuthStack.Screen
        name={'ProfileVerification'}
        component={ProfileVerification}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name={'VerifyEmail'}
        component={VerifyEmailScreen}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name={'UploadProfile'}
        component={UploadProfile}
        options={{headerShown: true, headerTitle: ''}}
      />

      <AuthStack.Screen
        name={'Subscription'}
        component={Subscription}
        options={{headerShown: false}}
      />

      <AuthStack.Screen
        name={'AddNewCard'}
        component={AddNewCard}
        options={{headerShown: true, headerTitle: ''}}
      />
      <AuthStack.Screen
        name={'PetProfile'}
        component={PetProfile}
        options={{headerShown: true, headerTitle: ''}}
      />
      <AuthStack.Screen
        name={'AddAuthService'}
        component={AddService}
        options={{headerShown: true, headerTitle: ''}}
      />
      <AuthStack.Screen
        name={'ProfileCreated'}
        component={ProfileCreated}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name={'PasswordSuccessFeedback'}
        component={PasswordSuccessFeedback}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name={'PetSitterAccountFeedback'}
        component={PetSitterAccountFeedback}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name={'PetOwnerAccountFeedback'}
        component={PetOwnerAccountFeedback}
        options={{headerShown: false}}
      />
      {/* <AuthStack.Screen
        name="TermofUse"
        component={TermofUse}
        options={{headerShown: true, headerTitle: ''}}
      />
      <AuthStack.Screen
        name="PrivacyPolicy"
        component={PrivacyPolicy}
        options={{headerShown: true, headerTitle: ''}}
      /> */}
    </AuthStack.Navigator>
  );
};

export default AuthStackNavigator;
