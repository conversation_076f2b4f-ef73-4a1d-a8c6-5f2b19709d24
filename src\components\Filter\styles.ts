import {moderateScale, verticalScale} from 'react-native-size-matters';
import {COLORS} from '../../themes/themes';
import {FONTS} from '../../themes/fonts';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    padding: 24,
    paddingBottom: 40,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    height: '100%',
    width: '100%',
    // justifyContent: 'space-between',
    // position: 'absolute',
    // bottom: 0,
  },
  errorText: {
    color: 'red',
    marginTop: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  searchContainer: {
    alignItems: 'center',
    height: 40,
  },
  labelText: {
    fontSize: moderateScale(14),
    color: COLORS.Bastille,
    marginBottom: verticalScale(6),
    fontFamily: FONTS.Medium,
    marginTop: 14,
  },
  modalStyle: {
    flex: 1,
    justifyContent: 'flex-end',
    margin: 0,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: COLORS.lightGray,
  },
});
