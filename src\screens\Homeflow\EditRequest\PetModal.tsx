import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {useTranslation} from 'react-i18next';

interface IPetModal {
  name: string;
  id: string;
  picture: string;
}
interface Props {
  isModalVisible: boolean;
  setModalVisible: (isModalVisible: boolean) => void;
  data: IPetModal[];
  setPetname: (name: string, petId: string) => void;
}

const PetModal: React.FC<Props> = ({
  setModalVisible,
  isModalVisible,
  data,
  setPetname,
}) => {
  const {t} = useTranslation();
  return (
    <Modal
      isVisible={isModalVisible}
      onBackdropPress={() => setModalVisible(false)}>
      <View style={styles.petModalContainer}>
        <Text style={styles.petTitleText}>{t('Choose your pet')}</Text>
        {data?.map((item, index) => {
          return (
            <View key={item.id}>
              <TouchableOpacity
                key={item.id}
                style={styles.modalContainer}
                onPress={() => {
                  setPetname(item.name, item.id);
                  setModalVisible(false);
                }}>
                <Image source={{uri: item.picture}} style={styles.petName} />
                <Text style={styles.text}>{item.name}</Text>
              </TouchableOpacity>
              {index != data.length - 1 && (
                <View style={styles.serviceSeparator} />
              )}
            </View>
          );
        })}
      </View>
    </Modal>
  );
};

export default PetModal;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingVertical: 12,
  },
  innerContainer: {
    paddingHorizontal: 12,
  },
  divider: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginVertical: 12,
  },
  text: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
  },
  modalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: 26,
  },
  petName: {
    height: 40,
    width: 40,
    borderRadius: 100,
    marginRight: 14,
  },
  petTitleText: {
    textAlign: 'center',
    fontFamily: FONTS.Bold,
    fontSize: 18,
    color: COLORS.Bastille,
  },
  petModalContainer: {
    backgroundColor: COLORS.white,
    paddingVertical: 12,
    borderRadius: 12,
  },
  serviceSeparator: {
    alignSelf: 'center',
    width: '95%',
    height: 1,
    backgroundColor: '#EEEEEE',
    marginTop: 12,
  },
});
