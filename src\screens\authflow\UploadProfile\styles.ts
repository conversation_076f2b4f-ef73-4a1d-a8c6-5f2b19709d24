import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: verticalScale(40),
    alignItems: 'center',
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingBottom: verticalScale(80),
  },
  imageContainer: {
    height: verticalScale(180),
    width: verticalScale(180),
    borderRadius: verticalScale(180),
  },
  buttonTitle: {
    fontSize: verticalScale(20),
    fontFamily: FONTS.SemiBold,
    color: COLORS.primary,
    paddingTop: verticalScale(28),
  },
});
