import {
  ImageSourcePropType,
  SectionList,
  Text,
  View,
  SectionListData,
  ActivityIndicator,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {ButtonwithIcon} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {COLORS} from '../../../themes/themes';
import NotificationCard from './NotificationCard';
import {OwnerBottomStackParamList} from '../../../navigation/PetOwnerBottomTabs';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import firestore from '@react-native-firebase/firestore';
import {useAppSelector} from '../../../store/Store';
import {INotifications} from '../../../interfaces/INotifications';
import {
  accountNotification,
  bookingNotification,
  messageNotification,
  paymentNotification,
  petNotification,
} from '../../../assets/images';
import moment from 'moment';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';

type Props = NativeStackScreenProps<OwnerBottomStackParamList, 'Notification'>;

const Notification: React.FC<Props> = ({navigation}) => {
  const [notifications, setNotifications] = useState<Array<INotifications>>([]);
  const [isLoading, setIsLoading] = useState(true);

  const user = useAppSelector(state => state.user);
  const {t} = useTranslation();

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  const fetchNotifications = async () => {
    try {
      const documentRef = firestore()
        .collection('Notifications')
        .where('sentTo', '==', user.uid)
        .orderBy('createdAt', 'desc');

      const querySnapshot = await documentRef.get();

      const _notificationsData: INotifications[] = [];
      const batch = firestore().batch();
      querySnapshot.forEach(doc => {
        const notificationData = {
          id: doc.id,
          ...doc.data(),
        } as INotifications;
        _notificationsData.push(notificationData);

        const notificationRef = firestore()
          .collection('Notifications')
          .doc(doc.id);
        batch.update(notificationRef, {isRead: true});
      });

      await batch.commit();

      setNotifications(_notificationsData);
    } catch (error) {
      console.log('An error occurred while fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, []);
  const handleimageSource = (type: string): ImageSourcePropType => {
    if (type == 'isBooking') {
      return bookingNotification;
    } else if (type == 'isPet') {
      return petNotification;
    } else if (type == 'isUser') {
      return accountNotification;
    } else if (type == 'isPayment') {
      return paymentNotification;
    } else if (type == 'isMessage') {
      return messageNotification;
    }
    return petNotification;
  };

  const groupedNotifications: SectionListData<INotifications>[] = Object.values(
    notifications.reduce(
      (acc: {[key: string]: {title: string; data: INotifications[]}}, item) => {
        const time = moment(item.createdAt.toDate()).format('MMMM D, YYYY');
        if (!acc[time]) {
          acc[time] = {
            title: time,
            data: [],
          };
        }
        acc[time].data.push(item);
        return acc;
      },
      {},
    ),
  );
  const updateStatus = async (id: string) => {
    try {
      await firestore()
        .collection('Notifications')
        .doc(id)
        .update({isActionDone: true});
    } catch (error) {
      console.log('Error updating status', error);
    }
  };
  const handleNotificationPress = async (item: INotifications) => {
    if (!item.isActionDone) {
      if (item.type == 'isInterested') {
        navigation.navigate('RequestScreen');
      }
    }
  };

  return (
    <View style={styles.container}>
      <Text style={appStyles.title}>{t('Notifications')}</Text>
      {isLoading ? (
        <ActivityIndicator
          size={'large'}
          color={COLORS.primary}
          style={{marginTop: 100}}
        />
      ) : (
        <View style={styles.notificationCard}>
          <SectionList
            sections={groupedNotifications}
            showsVerticalScrollIndicator={false}
            renderSectionHeader={({section}) => (
              <Text style={styles.dateText}>{section.title}</Text>
            )}
            stickySectionHeadersEnabled={false}
            renderItem={({item}) => (
              <View style={{marginTop: 16}}>
                <NotificationCard
                  title={item?.message?.notification?.title}
                  paragraph={item.message?.notification.body}
                  imageSource={handleimageSource(item.type)}
                  onPress={() => handleNotificationPress(item)}
                />
              </View>
            )}
            ListEmptyComponent={
              <Text style={styles.emptyMessage}>
                {t('No Notifications found')}
              </Text>
            }
          />
        </View>
      )}
    </View>
  );
};

export default Notification;
