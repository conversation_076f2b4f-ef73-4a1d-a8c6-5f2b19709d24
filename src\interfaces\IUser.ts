export interface IUser {
  firstName: string;
  surName: string;
  email: string;
  phoneNumber: string;
  address: string;
  city: string;
  country: string;
  dateOfBirth: Date;
  about: string;
  idFrontSide?: string;
  idBackSide?: string;
  userType: 'petOwner' | 'petSitter' | '';
  countryCode: string;
  profilePicture?: string;
  uid: string;
  fcmToken?: string;
  customerId?: string;
  isPremiumCustomer?: boolean;
  experience?: number;
  rating?: number;
  coordinates: {longitude: number; latitude: number};
  accountId?: string;
  profileStatus?: 'pending' | 'approved';
  isBlocked?: boolean;
  isVerified?: boolean;
}
