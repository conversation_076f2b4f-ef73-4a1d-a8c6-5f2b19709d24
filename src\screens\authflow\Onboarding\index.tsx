import React, {useContext, useRef, useState} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  Image,
  ScrollView,
  Animated,
  Dimensions,
} from 'react-native';
import {ArrowRight, Frame1, Frame2, Frame3} from '../../../assets/images';
import {TextButton, TextButtonwithIcon} from '../../../components';
import styles from './styles';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AuthContext} from '../../../../App';
import {useTranslation} from 'react-i18next';

type Props = NativeStackScreenProps<AuthStackParamList, 'Onboard'>;

const Onboard: React.FC<Props> = ({navigation}) => {
  const {setFirstLaunch} = useContext(AuthContext);

  const [selectedIndex, setSelectedIndex] = useState(0);
  const {t} = useTranslation();
  const windowWidth = Dimensions.get('window').width;
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollX = new Animated.Value(0);

  const handleOnboard = () => {
    AsyncStorage.setItem('appLaunched', 'false');
    setFirstLaunch(false);
  };

  const slides = [
    {
      key: 'one',
      image: Frame1,
      label: t('Welcome to My Pet Sit App'),
      paragraph: t(
        `Your go-to platform for finding nearby pet sitters. Whether you're going on vacation, need someone to care for your furry friend during the day, or simply want to provide companionship, we've got you covered.`,
      ),
    },
    {
      key: 'two',
      image: Frame2,
      label: t('Discover the Perfect Pet Sitting Experience'),
      paragraph: t(
        `With our user-friendly interface, finding the perfect pet sitter is a breeze. Browse through profiles, read reviews, and choose from a variety of services tailored to your pet's unique needs. Stay connected with real-time updates and photos, ensuring you never miss a moment of your pet's happiness.`,
      ),
    },
    {
      key: 'three',
      image: Frame3,
      label: t('Your Trusted Pet Care Companion'),
      paragraph: t(
        `built-in chat system enables seamless communication, allowing you to discuss specific requirements, provide instructions, and stay updated throughout the pet sitting process. It's time to discover the My Pet Sit experience.`,
      ),
    },
  ];

  const onScroll = Animated.event(
    [{nativeEvent: {contentOffset: {x: scrollX}}}],
    {useNativeDriver: false},
  );

  const onMomentumScrollEnd = (event: any) => {
    const selectedIndex = Math.floor(
      event.nativeEvent.contentOffset.x / Math.floor(windowWidth),
    );
    setSelectedIndex(selectedIndex);
  };

  const onArrowButtonPress = () => {
    const nextIndex = selectedIndex + 1;
    if (nextIndex < slides.length) {
      scrollViewRef.current?.scrollTo({
        x: nextIndex * windowWidth,
        animated: true,
      });
    }
    setSelectedIndex(selectedIndex + 1);
  };

  return (
    <ScrollView
      ref={scrollViewRef}
      horizontal
      pagingEnabled
      showsHorizontalScrollIndicator={false}
      onMomentumScrollEnd={onMomentumScrollEnd}
      onScroll={onScroll}
      scrollEventThrottle={16}>
      {slides.map((item, index) => {
        const inputRange = [
          (index - 1) * windowWidth,
          index * windowWidth,
          (index + 1) * windowWidth,
        ];
        const scale = scrollX.interpolate({
          inputRange,
          outputRange: [0.8, 1, 0.8],
          extrapolate: 'extend',
        });
        const opacity = scrollX.interpolate({
          inputRange,
          outputRange: [0.3, 1, 0.3],
          extrapolate: 'extend',
        });

        return (
          <View key={index} style={[styles.container, {width: windowWidth}]}>
            <Animated.View style={{transform: [{scale}], opacity}}>
              <View style={styles.innerContainer}>
                <Image
                  resizeMode="stretch"
                  source={item.image}
                  style={styles.imageContainer}
                />
                <Text style={styles.label}>{item.label}</Text>
                <Text style={styles.paragraph}>{item.paragraph}</Text>
              </View>
              <View style={styles.dotContainer}>
                {slides.map((_, dotIndex) => (
                  <View
                    key={dotIndex}
                    style={[
                      styles.inActiveDot,
                      dotIndex === selectedIndex && styles.activeDot,
                    ]}
                  />
                ))}
              </View>
              {selectedIndex == 0 || selectedIndex == 1 ? (
                <View style={styles.buttonWrapper}>
                  <TouchableOpacity
                    onPress={() => {
                      handleOnboard();
                      navigation.replace('JoinUs');
                    }}>
                    <Text style={[styles.skipText]}>{t('Skip')}</Text>
                  </TouchableOpacity>
                  <View>
                    <TextButtonwithIcon
                      label=""
                      labelStyle={styles.buttonLabel}
                      onPress={onArrowButtonPress}
                      rightIcon={
                        <View style={styles.nextButton}>
                          <Image
                            resizeMode="contain"
                            source={ArrowRight}
                            style={styles.iconContainer}
                          />
                        </View>
                      }
                    />
                  </View>
                </View>
              ) : (
                <View style={{marginHorizontal: 20, marginTop: 8}}>
                  <TextButton
                    label={t('Lets Get Started')}
                    onPress={() => {
                      handleOnboard();
                      navigation.replace('JoinUs');
                    }}
                    containerStyle={{marginTop: -3}}
                  />
                </View>
              )}
            </Animated.View>
          </View>
        );
      })}
    </ScrollView>
  );
};

export default Onboard;
