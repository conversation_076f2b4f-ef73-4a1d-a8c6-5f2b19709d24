import {Image, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {placeholderImage} from '../../assets/images';
import TextButtonwithIcon from '../Buttons/TextButtonwithIcon';
import {Star} from '../../assets/svgIcons';
import {styles} from './styles';
interface ReviewCardProps {
  name: string;
  email: string;
  rating: number;
  date: string;
  picture: string;
}
const ReviewCard: React.FC<ReviewCardProps> = ({
  name,
  email,
  rating,
  date,
  picture,
}) => {
  return (
    <TouchableOpacity style={styles.wraper}>
      <View style={styles.container}>
        <Image
          source={picture ? {uri: picture} : placeholderImage}
          style={styles.image}
        />
        <View style={styles.innerContainer}>
          <View>
            <Text style={styles.name}>{name}</Text>
            <Text style={styles.email}>{email}</Text>
          </View>
          <TextButtonwithIcon
            leftIcon={<Star width={16} height={16} />}
            label={rating.toString()}
            labelStyle={styles.label}
            containerStyle={styles.icon}
            disabled
          />
        </View>
      </View>
      <Text style={styles.dateText}>{date}</Text>
    </TouchableOpacity>
  );
};

export default ReviewCard;
