import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  scrollViewContent: {
    flexGrow: 1,
    backgroundColor: 'red',
  },
  emailInputFeild: {
    marginTop: 26,
  },
  passwordInputFeild: {
    marginTop: 14,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginVertical: verticalScale(16),
  },
  forgotPasswordLabel: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.primary,
  },
  title: {
    marginTop: verticalScale(12),
  },
});
