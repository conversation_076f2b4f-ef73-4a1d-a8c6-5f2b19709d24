import { StyleSheet } from "react-native";
import { COLORS, SIZES } from "../../../themes/themes";
import { verticalScale } from "react-native-size-matters";
import { FONTS } from "../../../themes/fonts";

export const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.white,
        paddingTop: verticalScale(8),
        paddingHorizontal: 24,
    },
    margin: {
        marginTop: 14,
    },
    footer: {
        marginTop:
            SIZES.DeviceHeight > 760 ? verticalScale(100) : verticalScale(50),
    },
    deleteContainer: {
        alignSelf: 'flex-start',
        paddingTop: 18,
    },
    iconContainer: {
        width: 18,
        height: 18,
        borderRadius: 3,
        borderColor: COLORS.lightGray,
        borderWidth: 1,
        marginRight: 6,
    },
    emptyView: {
        width: 18,
        height: 18,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: COLORS.primary,
    },
    privacyPolicy: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: verticalScale(16),
    },
    text: {
        fontSize: 12,
        fontFamily: FONTS.Medium,
        color: COLORS.Bastille,
        marginLeft: 6,
    },
    passwordInputFeild: {
        marginTop: 14,
    },
});
