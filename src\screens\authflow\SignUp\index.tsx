import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  View,
} from 'react-native';
import React, {useContext, useLayoutEffect, useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {
  AgreedCheckBox,
  AppText,
  ButtonwithIcon,
  FormInput,
  LoginSignUpButtons,
  TextButton,
} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {useTogglePasswordVisibility} from '../../../hooks';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {ISignUpForm} from '../../../interfaces';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import auth from '@react-native-firebase/auth';
import {styles} from './styles';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import firestore from '@react-native-firebase/firestore';
import {AuthContext} from '../../../../App';

type Props = NativeStackScreenProps<AuthStackParamList, 'SignUp'>;

const SignUp: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();
  const {isOwner} = useContext(AuthContext);

  const validationSchema = Yup.object().shape({
    firstName: Yup.string().required(t('Enter your first name')),
    surName: Yup.string().required(t('Enter your sur name')),
    email: Yup.string()
      .required(t('Enter your email'))
      .email(t('Enter a valid email')),
    password: Yup.string()
      .required(t('Password is required'))
      .matches(
        /^(?=.*[0-9])(?=.*[@#£_&\-+()\/?!;:'",.~`|•√π÷×§∆\\}{=°^¢$¥€%©®™✓\[\]<>\/\-+,.\]{1,})(?=.*[A-Z]).{8,}$/,
        t(
          'Password must contain at least 1 number, 1 special character, and 1 capital letter',
        ),
      ),
    confirmPassword: Yup.string()
      .label('Confirm Password')
      .required(t('Enter your password again'))
      .oneOf([Yup.ref('password'), ''], t("Password doesn't match")),
    termofUse: Yup.boolean()
      .isTrue(t('You must accept the Terms of Use'))
      .required(t('You must accept the Terms of Use')),
    privacyPolicy: Yup.boolean()
      .isTrue(t('You must accept Privacy Policy'))
      .required(t('You must accept the Privacy Policy')),
  });

  useLayoutEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, [navigation]);

  const {passwordVisibility, rightIcon, handlePasswordVisibility} =
    useTogglePasswordVisibility();

  const onSubmitHandler = async () => {
    const {email, password, firstName, surName} = formik.values;
    setIsLoading(true);
    try {
      await auth()
        .createUserWithEmailAndPassword(email, password)
        .then(async res => {
          await firestore()
            .collection('Users')
            .doc(res.user.uid)
            .set({
              onboardingComplete: false,
              createdAt: firestore.FieldValue.serverTimestamp(),
              userType: isOwner ? 'petOwner' : 'petSitter',
              firstName: firstName,
              surName: surName,
              email: email,
            });

          console.log('User account created & signed in!', res.user.uid);

          auth().currentUser?.sendEmailVerification();
          navigation.navigate('VerifyEmail');
        });
    } catch (error) {
      let errorMessage = 'An error occurred';
      if (error instanceof Error) {
        switch (error.message) {
          case 'auth/email-already-in-use':
            errorMessage =
              'That email address is already in use, try Logging in';
            break;
          case 'auth/invalid-email':
            errorMessage = 'That email address is invalid!';
            break;
          default:
            errorMessage = error.message;
            break;
        }
      }
      console.error(errorMessage);
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t(errorMessage),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<ISignUpForm>({
    enableReinitialize: true,
    initialValues: {
      firstName: '',
      surName: '',
      email: '',
      password: '',
      confirmPassword: '',
      termofUse: false,
      privacyPolicy: false,
    },
    onSubmit: onSubmitHandler,
    validateOnMount: true,
    validationSchema: validationSchema,
  });

  return (
    <View style={{flex: 1, backgroundColor: COLORS.white}}>
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20} // adjust if you have a header
      >
        <ScrollView
          contentContainerStyle={{flexGrow: 1}}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled">
          <View style={styles.container}>
            <LoginSignUpButtons
              onSignInPress={() => navigation.navigate('Login')}
              isLogin={false}
            />
            <Text style={[appStyles.title, styles.title]}>{t('Sign up')}</Text>
            <Text style={[appStyles.paragraph, {marginTop: 8}]}>
              {t('Welcome to')}
              <Text style={styles.paragraph}>{t(' My Pet Sit ')}</Text>
            </Text>
            <FormInput
              label={t('First Name')}
              placeholder={t('First Name')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.nameInputFeild}
              onChangeText={formik.handleChange('firstName')}
              onBlur={() => formik.setFieldTouched('firstName')}
              onFocus={() => {
                formik.setFieldTouched('firstName');
              }}
            />
            <AppText
              error={formik.errors.firstName}
              visible={!!formik.touched.firstName}
            />

            <FormInput
              label={t('Surname')}
              placeholder={t('Surname')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              onChangeText={formik.handleChange('surName')}
              onBlur={() => formik.setFieldTouched('surName')}
              onFocus={() => {
                formik.setFieldTouched('surName');
              }}
            />
            <AppText
              error={formik.errors.surName}
              visible={!!formik.touched.surName}
            />

            <FormInput
              label={t('Email Address')}
              placeholder={t('<EMAIL>')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              onChangeText={formik.handleChange('email')}
              onBlur={() => formik.setFieldTouched('email')}
              onFocus={() => {
                formik.setFieldTouched('email');
              }}
              keyboardType="email-address"
            />
            <AppText
              error={formik.errors.email}
              visible={!!formik.touched.email}
            />
            <FormInput
              label={t('Password')}
              placeholder={t('Enter password')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              secureTextEntry={passwordVisibility}
              rightIcon={rightIcon}
              onRightIconPress={handlePasswordVisibility}
              isPassword
              onChangeText={formik.handleChange('password')}
              onBlur={() => formik.setFieldTouched('password')}
              onFocus={() => formik.setFieldTouched('password')}
            />
            <AppText
              error={formik.errors.password}
              visible={!!formik.touched.password}
            />
            <FormInput
              label={t('Confirm Password')}
              placeholder={t('Confirm Password')}
              placeholderTextColor={COLORS.placeHolder}
              containerStyle={styles.margin}
              secureTextEntry={passwordVisibility}
              rightIcon={rightIcon}
              onRightIconPress={handlePasswordVisibility}
              isPassword
              onChangeText={formik.handleChange('confirmPassword')}
              onBlur={() => formik.setFieldTouched('confirmPassword')}
              onFocus={() => formik.setFieldTouched('confirmPassword')}
            />
            <AppText
              error={formik.errors.confirmPassword}
              visible={!!formik.touched.confirmPassword}
            />
            <AgreedCheckBox
              label={t('Terms of Use')}
              containerStyle={{marginTop: 14}}
              selected={formik.values.termofUse}
              setSelected={value => formik.setFieldValue('termofUse', value)}
              handleOnPress={() => navigation.navigate('TermofUse')}
            />
            <AppText
              error={formik.errors.termofUse}
              visible={!!formik.touched.termofUse}
            />
            <AgreedCheckBox
              containerStyle={{marginTop: 14}}
              label={t('Privacy Policy')}
              selected={formik.values.privacyPolicy}
              setSelected={value =>
                formik.setFieldValue('privacyPolicy', value)
              }
              handleOnPress={() => navigation.navigate('PrivacyPolicy')}
            />
            <AppText
              error={formik.errors.privacyPolicy}
              visible={!!formik.touched.privacyPolicy}
            />
            <TextButton
              label={t('Sign up')}
              isShadow
              onPress={formik.handleSubmit}
              containerStyle={styles.button}
              isLoading={isLoading}
              disabled={isLoading || !formik.isValid}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default SignUp;
