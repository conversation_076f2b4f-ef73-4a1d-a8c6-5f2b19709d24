import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';
import enTranslations from './locales/en.json';
import frTranslations from './locales/fr.json';
import nlTranslations from './locales/nl.json';
import grTranslations from './locales/de.json';

i18n.use(initReactI18next).init({
  compatibilityJSON: 'v3',
  resources: {
    en: {
      translation: enTranslations,
    },

    fr: {
      translation: frTranslations,
    },
    nl: {
      translation: nlTranslations,
    },
    de: {
      translation: grTranslations,
    },
  },
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
