import {
  Keyboard,
  SafeAreaView,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {useState} from 'react';
import Modal from 'react-native-modal';
import {COLORS} from '../../themes/themes';
import ButtonwithIcon from '../Buttons/ButtonwithIcon';
import {Cross} from '../../assets/svgIcons';
import appStyles from '../../themes/appStyles';
import {FONTS} from '../../themes/fonts';
import FormInput from '../FormInput';
import TextButton from '../Buttons/TextButton';
import {
  GooglePlaceDetail,
  GooglePlacesAutocomplete,
} from 'react-native-google-places-autocomplete';
import Config from 'react-native-config';

import {useTranslation} from 'react-i18next';
import {useLanguage} from '../../contexts/LanguageContext';
import {styles} from './styles';
import {IFilters} from '../../interfaces';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import PlacesAutocomplete from '../PlacesAutocomplete';

interface Props {
  isModalVisible: boolean;
  handleApply: (filter: IFilters) => void;
  onBackdropPress: () => void;
  onCrossPress: () => void;
}

const Filter: React.FC<Props> = ({
  isModalVisible,
  handleApply,
  onBackdropPress,
  onCrossPress,
}) => {
  const {t} = useTranslation();
  const {selectedLanguage} = useLanguage();
  const [filters, setFilters] = useState<IFilters>();

  const validationSchema = Yup.object().shape({
    radius: Yup.number()
      .typeError(t('Radius must be a number'))
      .positive(t('Radius must be positive'))
      .required(t('Radius is required')),
    city: Yup.string().required(t('City is required')),
  });

  const formik = useFormik({
    initialValues: {
      radius: '',
      city: '',
    },
    validationSchema,
    validateOnMount: true,
    onSubmit: values => {
      const updatedFilters = {
        ...filters,
        radius: parseInt(values.radius),
        location: {
          ...filters?.location,
          city: values.city,
        },
      };

      handleApply(updatedFilters);
    },
  });

  const setLocationDetail = (placeDetail: GooglePlaceDetail) => {
    try {
      if (placeDetail) {
        const locality = placeDetail?.address_components.find(city =>
          city.types.includes('locality'),
        );
        const locationPayload = {
          city: locality?.long_name || '',
          place_id: placeDetail.place_id,
          latitude: placeDetail.geometry.location.lat,
          longitude: placeDetail.geometry.location.lng,
        };
        setFilters({...filters, location: locationPayload});
        formik.setFieldValue('city', locality?.long_name || '');
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  return (
    <Modal
      isVisible={isModalVisible}
      style={styles.modalStyle}
      onBackdropPress={onBackdropPress}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.container}>
          <SafeAreaView />
          <View style={styles.header}>
            <View style={{width: 24}} />
            <Text style={[appStyles.title2, {fontFamily: FONTS.Bold}]}>
              {t('Filter')}
            </Text>
            <ButtonwithIcon icon={<Cross />} onPress={onCrossPress} />
          </View>

          <FormInput
            label={t('Near to you')}
            placeholder={t('0 Km')}
            placeholderTextColor={COLORS.placeHolder}
            onChangeText={formik.handleChange('radius')}
            onBlur={formik.handleBlur('radius')}
            value={formik.values.radius}
            containerStyle={{marginTop: 10}}
            keyboardType="number-pad"
            error={formik.touched.radius && formik.errors.radius}
          />
          {/* <Text style={styles.labelText}>{t('Search city')}</Text>
          <GooglePlacesAutocomplete
            minLength={1}
            placeholder={t('Search')}
            query={{
              key: Config.GOOGLE_MAPS_API_KEY,
              language: selectedLanguage,
              types: '(cities)',
            }}
            styles={{
              container: styles.searchContainer,
              textInputContainer: styles.textInput,
            }}
            onPress={(data, details = null) => {
              if (details) {
                setLocationDetail(details);
              }
            }}
            onFail={error => {
              console.log('Error:', error);
            }}
            onNotFound={() => {}}
            onTimeout={() =>
              console.warn('google places autocomplete: request timeout')
            }
            textInputProps={{}}
            enablePoweredByContainer={false}
            predefinedPlaces={[]}
            autoFillOnNotFound={false}
            currentLocation={false}
            currentLocationLabel="Current location"
            debounce={0}
            disableScroll={false}
            enableHighAccuracyLocation={true}
            fetchDetails
            timeout={20000}
            isNewPlacesAPI={false}
            fields="*"
          /> */}

          <PlacesAutocomplete
            apiKey={Config.GOOGLE_MAPS_API_KEY}
            onPlaceSelected={(place, details) => {
              if (details) {
                setLocationDetail(details);
              }
            }}
            fetchDetails={true}
            countryCode="be"
            textInputProps={{placeholder: t('Search')}}
            containerStyle={{marginTop: 10}}
            label={t('Search city')}
          />
        </View>
      </TouchableWithoutFeedback>
      <View
        style={{
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: 32,
          marginHorizontal: 20,
        }}>
        <TextButton
          label={t('Apply')}
          containerStyle={{}}
          onPress={formik.handleSubmit}
          disabled={!formik.isValid}
        />
      </View>
    </Modal>
  );
};

export default Filter;
