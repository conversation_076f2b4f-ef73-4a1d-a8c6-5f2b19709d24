import {
  StyleSheet,
  Text,
  TextInput,
  View,
  NativeSyntheticEvent,
  TextInputFocusEventData,
  Keyboard,
  InputAccessoryView,
  Button,
  Platform,
} from 'react-native';
import React from 'react';
import {COLORS, SIZES} from '../../../themes/themes';
import {moderateScale, verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';
import {AppText} from '../../../components';

interface Props {
  onChangeText?: (text: string) => void;
  onBlur?: (e: NativeSyntheticEvent<TextInputFocusEventData>) => void;
  isPassword?: boolean;
  placeholder: string;
  placeholderTextColor: string;
  label: string;
  value: string;
  onSubmitEditing?: any;
  blurOnSubmit?: any;
  showError: boolean;
  errorText?: string;
}

const AboutMe: React.FC<Props> = ({
  onChangeText,
  onBlur,
  placeholder,
  placeholderTextColor,
  label,
  value,
  onSubmitEditing,
  blurOnSubmit,
  showError,
  errorText,
  ...rest
}) => {
  const inputAccessoryViewID = 'AboutMeID';
  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.innerContainer}>
        <TextInput
          style={{flex: 1}}
          placeholder={placeholder}
          placeholderTextColor={placeholderTextColor}
          onChangeText={onChangeText}
          onBlur={onBlur}
          multiline={true}
          value={value}
          onSubmitEditing={onSubmitEditing}
          blurOnSubmit={blurOnSubmit}
          textAlignVertical="top"
          inputAccessoryViewID={inputAccessoryViewID}
          {...rest}
        />
      </View>
      <AppText error={errorText} visible={showError} />
      {Platform.OS === 'ios' ? (
        <InputAccessoryView nativeID={inputAccessoryViewID}>
          <View style={styles.accessory}>
            <Button title="Done" onPress={() => Keyboard.dismiss()} />
          </View>
        </InputAccessoryView>
      ) : null}
    </View>
  );
};

export default AboutMe;

const styles = StyleSheet.create({
  accessory: {
    backgroundColor: '#f1f1f1',
    padding: 8,
    alignItems: 'flex-end',
  },
  container: {
    marginTop: SIZES.DeviceHeight > 760 ? 0 : 12,
  },
  innerContainer: {
    height: verticalScale(130),
    backgroundColor: COLORS.white,
    borderColor: COLORS.lightGray,
    borderWidth: 1,
    marginTop: 8,
    paddingHorizontal: 12,
    borderRadius: 5,
  },
  label: {
    fontSize: moderateScale(14),
    color: COLORS.Bastille,
    fontFamily: FONTS.Medium,
    marginTop: verticalScale(6),
  },
});
