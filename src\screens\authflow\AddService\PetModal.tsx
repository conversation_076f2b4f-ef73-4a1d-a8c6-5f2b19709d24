import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import Modal from 'react-native-modal';
import {COLORS} from '../../../themes/themes';
import {useTranslation} from 'react-i18next';

interface PetData {
  id: number;
  name: string;
}

interface Props {
  isModalVisible: boolean;
  setModalVisible: (isModalVisible: boolean) => void;
  data: PetData[];
  setPetname: (petName: string) => void;
}

const PetModal: React.FC<Props> = ({
  setModalVisible,
  isModalVisible,
  data,
  setPetname,
}) => {
  const {t} = useTranslation();
  return (
    <Modal
      isVisible={isModalVisible}
      onBackdropPress={() => setModalVisible(false)}>
      <View style={styles.container}>
        {data?.map((item, index) => {
          return (
            <TouchableOpacity
              key={item.id}
              style={styles.innerContainer}
              onPress={() => {
                setPetname(item.name);
                setModalVisible(false);
              }}>
              <Text>{t(item.name)}</Text>

              {index != 4 && <View style={styles.divider} />}
            </TouchableOpacity>
          );
        })}
      </View>
    </Modal>
  );
};

export default PetModal;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingVertical: 12,
  },
  innerContainer: {
    paddingHorizontal: 12,
  },
  divider: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginVertical: 12,
  },
});
