import React, {useEffect, useState} from 'react';
import {
  Text,
  View,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import Header from '../PetOwnerHome/Header';
import {ButtonwithIcon, Filter, PetCard} from '../../../components';
import {EmptyState, Filter2} from '../../../assets/svgIcons';
import firestore from '@react-native-firebase/firestore';
import {SitterBottomStackParamList} from '../../../navigation/PetSitterBottomTabs';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {styles} from './styles';
import {COLORS} from '../../../themes/themes';
import {useTranslation} from 'react-i18next';
import {IFilters} from '../../../interfaces';
import {getGeoQuery} from '../../../helpers/query-helper';
import * as geofirestore from 'geofirestore';
import {IBooking} from '../../../interfaces';
import {useAppSelector} from '../../../store/Store';

import {
  InterstitialAdComponent,
  BannerAdComponent,
} from '../../../components/Admob';

type Props = NativeStackScreenProps<
  SitterBottomStackParamList,
  'PetSitterHome'
>;

const PAGE_SIZE = 10;

const PetSitterHome: React.FC<Props> = ({navigation}) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [bookings, setBookings] = useState<IBooking[]>([]);
  const [filterApplied, setFilterApplied] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [displayedBookings, setDisplayedBookings] = useState<IBooking[]>([]);

  const {t} = useTranslation();
  const user = useAppSelector(state => state.user);

  const fetchBookings = async (isInitialLoad = false) => {
    try {
      if (loadingMore || !isInitialLoad) return;

      const firestoreApp = firestore();
      const GeoFirestore = geofirestore.initializeApp(firestoreApp);
      const geocollection = GeoFirestore.collection('Bookings');
      setIsLoading(true);
      // setLoadingMore(true);
      const query = getGeoQuery(
        geocollection,
        user?.coordinates?.latitude || 0,
        user.coordinates?.longitude || 0,
        100,
      );
      const snapshot = await query.get();
      const bookingsDataPromises = snapshot.docs.map(async (doc: any) => {
        const userData = await firestore()
          .collection('Users')
          .doc(doc.data().userId)
          .get();
        const petDoc = await firestore()
          .collection('Pets')
          .doc(doc.data().petId)
          .get();

        return {
          ...doc.data(),
          key: doc.id,
          petData: petDoc.data(),
          userData: userData.data(),
        } as IBooking;
      });

      let bookingsData = await Promise.all(bookingsDataPromises);
      bookingsData = bookingsData.filter(
        booking => booking.status === 'pending' && !booking.userData.isBlocked,
      );

      setBookings(bookingsData);
      // setDisplayedBookings(bookingsData.slice(0, PAGE_SIZE));
    } catch (error) {
      console.log('Error fetching owners:', error);
    } finally {
      setIsLoading(false);
      setLoadingMore(false);
    }
  };

  // const loadMoreBookings = () => {
  //   if (loadingMore) return;
  //   setLoadingMore(true);
  //   const nextPage = currentPage + 1;
  //   const startIndex = (nextPage - 1) * PAGE_SIZE;
  //   const endIndex = startIndex + PAGE_SIZE;
  //   setDisplayedBookings([
  //     ...displayedBookings,
  //     ...bookings.slice(startIndex, endIndex),
  //   ]);
  //   setCurrentPage(nextPage);
  //   setLoadingMore(false);
  // };

  useEffect(() => {
    fetchBookings(true);
  }, []);

  const handleApplyFilter = async (filters: IFilters) => {
    setIsLoading(true);
    setFilterApplied(true);
    setModalVisible(false);
    const firestoreApp = firestore();
    const GeoFirestore = geofirestore.initializeApp(firestoreApp);
    const geocollection = GeoFirestore.collection('Bookings');
    const query = getGeoQuery(
      geocollection,
      filters.location?.latitude || 0,
      filters.location?.longitude || 0,
      filters.radius || 0,
    ).limit(10);

    try {
      const snapshot = await query.get();
      const filteredBookingsPromises = snapshot.docs.map(async (doc: any) => {
        const userData = await firestore()
          .collection('Users')
          .doc(doc.data().userId)
          .get();
        const petDoc = await firestore()
          .collection('Pets')
          .doc(doc.data().petId)
          .get();

        return {
          ...doc.data(),
          key: doc.id,
          petData: petDoc.data(),
          userData: userData.data(),
        } as IBooking;
      });

      let filteredBookings = await Promise.all(filteredBookingsPromises);
      filteredBookings = filteredBookings.filter(
        booking => booking.status === 'pending' && !booking.userData.isBlocked,
      );

      setBookings(filteredBookings);
    } catch (error) {
      console.log('Error filtering data', error);
    } finally {
      setIsLoading(false);
    }
  };
  const handleCancelFilter = () => {
    setFilterApplied(false);
    fetchBookings(true);
    // setCurrentPage(1);
  };

  const heading = filterApplied ? t('Filtered results') : t('Local bookings');

  return (
    <SafeAreaView style={styles.container}>
      <BannerAdComponent />
      <View style={styles.innerContainer}>
        <InterstitialAdComponent />
        <Header onPress={() => navigation.navigate('Notification')} />

        <View style={styles.searchBarContainer}>
          <View />
          <ButtonwithIcon
            icon={<Filter2 />}
            containerStyle={styles.filterButton}
            onPress={() => setModalVisible(true)}
          />
        </View>

        <View style={styles.headingContainer}>
          <Text style={styles.heading}>{heading}</Text>
          {filterApplied ? (
            <TouchableOpacity
              style={{marginTop: 20}}
              hitSlop={5}
              onPress={handleCancelFilter}>
              <Text style={styles.cancelText}>{t('Cancel filter')}</Text>
            </TouchableOpacity>
          ) : null}
        </View>
        <View style={styles.petContainer}>
          <FlatList
            showsVerticalScrollIndicator={false}
            data={bookings}
            keyExtractor={item => item.key}
            contentContainerStyle={{paddingBottom: 150}}
            renderItem={({item}) => {
              return (
                <PetCard
                  key={item.key}
                  image={item.petData?.picture}
                  name={item.petData?.name}
                  location={item.userData?.city ? item.userData?.city : ''}
                  rating={item.userData?.rating ? item.userData?.rating : 0}
                  onPress={() =>
                    navigation.navigate('PetDetails', {id: item.key})
                  }
                  containerStyle={styles.petCardContainer}
                  petOwnerPicture={
                    item.userData?.profilePicture
                      ? item.userData?.profilePicture
                      : ''
                  }
                  OwnerName={
                    item.userData?.firstName ? item.userData?.firstName : ''
                  }
                />
              );
            }}
            // onMomentumScrollBegin={() => setHasScrollBegin(false)}
            // onEndReached={loadMoreBookings}
            // onEndReachedThreshold={0.5}
            ListFooterComponent={
              loadingMore ? (
                <ActivityIndicator size="small" color={COLORS.primary} />
              ) : null
            }
            ListEmptyComponent={() =>
              isLoading ? (
                <ActivityIndicator
                  size={'large'}
                  style={{marginTop: 100}}
                  color={COLORS.primary}
                />
              ) : (
                <View style={{alignItems: 'center', marginTop: 50}}>
                  <EmptyState />
                  <Text style={styles.emptyText}>{t('No bookings found')}</Text>
                  <Text style={styles.emptyDesc}>
                    {t('It seems there are no bookings added yet')}
                  </Text>
                </View>
              )
            }
          />
        </View>
      </View>
      <Filter
        isModalVisible={isModalVisible}
        handleApply={handleApplyFilter}
        onBackdropPress={() => setModalVisible(false)}
        onCrossPress={() => setModalVisible(false)}
      />
    </SafeAreaView>
  );
};

export default PetSitterHome;
