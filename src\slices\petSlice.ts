import {createSlice} from '@reduxjs/toolkit';
import {IPet} from '../interfaces/IPet';

const initialState = {
  petsData: [] as Array<IPet>,
};

export const petSlice = createSlice({
  name: 'pets',
  initialState,
  reducers: {
    addPet: (state, {payload}) => {
      state.petsData = [...state.petsData, payload];
    },
    removePet: (state, {payload}) => {
      state.petsData = state.petsData.filter(pet => pet.uid !== payload);
    },
  },
});

export const {addPet} = petSlice.actions;

export default petSlice;
