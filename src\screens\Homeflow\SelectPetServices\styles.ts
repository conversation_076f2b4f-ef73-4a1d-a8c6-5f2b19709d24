import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {width, height} from 'react-native-dimension';
import {moderateScale, verticalScale} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    // paddingBottom: 40,
  },
  paragraph: {
    fontSize: moderateScale(14),
    color: COLORS.Bastille,
    marginBottom: verticalScale(6),
    fontFamily: FONTS.Medium,
    paddingTop: 16,
  },
  imageContainer: {
    width: 110,
    height: 110,
    borderRadius: 110 / 2,
    alignSelf: 'center',
    marginTop: 14,
  },
  imageStyles: {
    width: '100%',
    height: '100%',
    borderRadius: 110,
  },
  cameraIcon: {
    position: 'absolute',
    right: 12,
    bottom: 0,
  },
  icon: {
    width: 24,
    height: 24,
  },
  tickImage: {
    height: height(20),
    width: width(40),
    justifyContent: 'center',
    alignItems: 'center',
  },
  tickImageContainer: {
    flex: 1,
    backgroundColor: COLORS.white,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  innerContainer: {
    flexDirection: 'row',
    marginTop: 14,
    alignItems: 'flex-end',
  },
  addPet: {
    fontFamily: FONTS.SemiBold,
    fontSize: 20,
    color: COLORS.primary,
    padding: 10,
  },
  divider: {
    flexDirection: 'row',
    width: '100%',
    borderColor: COLORS.Philippine_Gray,
    borderWidth: 0.5,
    marginVertical: 10,
    marginTop: 20,
  },
  crossText: {
    fontFamily: FONTS.SemiBold,
    color: COLORS.Philippine_Gray,
    fontSize: 16,
  },
  crossContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  text: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.Bastille,
  },
  modalContainer: {
    // flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    // marginTop: 26,
  },
  petName: {
    height: 40,
    width: 40,
    borderRadius: 100,
    marginRight: 14,
  },
});
