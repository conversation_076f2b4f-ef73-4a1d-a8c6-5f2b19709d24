import {Text, View, ViewStyle} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {COLORS} from '../../themes/themes';
import TextButton from '../Buttons/TextButton';
import {Rating} from 'react-native-ratings';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';

interface Props {
  isModalVisible: boolean;
  onBackdropPress: () => void;
  handleConfirm: () => void;
  onCancelPress: () => void;
  redLabel: string;
  title: string;
  paragraph: string;
  isRating?: boolean;
  onRatingCompleted?: (rating: number) => void;
  isLoading?: boolean;
}

const AlertModal: React.FC<Props> = ({
  isModalVisible,
  onBackdropPress,
  onCancelPress,
  handleConfirm,
  redLabel,
  title,
  paragraph,
  isRating,
  onRatingCompleted,
  isLoading,
}) => {
  const {t} = useTranslation();

  const ratingCompleted = (newRating: number) => {
    if (onRatingCompleted) onRatingCompleted(newRating);
  };
  const background = isRating ? COLORS.primary : '#EB1212';
  const buttonColor = !isLoading ? background : COLORS.Philippine_Gray;
  return (
    <Modal isVisible={isModalVisible} onBackdropPress={onBackdropPress}>
      <View style={styles.container}>
        <Text style={styles.title}>{title}</Text>
        <View style={styles.divider} />
        <Text style={styles.paragraph}>{paragraph} </Text>
        {isRating ? (
          <Rating
            type="star"
            imageSize={30}
            onFinishRating={ratingCompleted}
            minValue={0}
            startingValue={0}
            style={{marginTop: 20, marginBottom: 10}}
          />
        ) : null}
        <View style={styles.modalButtonContainer}>
          <TextButton
            label={t('Cancel')}
            labelStyle={{color: COLORS.Bastille}}
            containerStyle={
              [styles.modalButton, {backgroundColor: COLORS.white}] as ViewStyle
            }
            onPress={onCancelPress}
          />
          <TextButton
            label={redLabel}
            containerStyle={
              [styles.modalButton, {backgroundColor: buttonColor}] as ViewStyle
            }
            onPress={handleConfirm}
            isLoading={isLoading}
            disabled={isLoading}
          />
        </View>
      </View>
    </Modal>
  );
};

export default AlertModal;
