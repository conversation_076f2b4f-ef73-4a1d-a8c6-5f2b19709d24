import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF',
  },
  innerContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
    marginBottom: 90,
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 24,
  },
  filterButton: {
    width: 48,
    height: 48,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 5,
  },
  heading: {
    fontSize: 16,
    fontFamily: FONTS.Bold,
    lineHeight: 20,
    color: '#2C2C2E',
    marginTop: 20,
  },
  PetContainer: {
    paddingTop: 16,
    paddingBottom: 16,
  },
  headingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  emptyText: {
    fontFamily: FONTS.SemiBold,
    marginTop: 27,
    fontSize: 10,
    color: '#939090',
  },
  cancelText: {
    color: COLORS.primary,
    fontFamily: FONTS.SemiBold,
    fontSize: 12,
  },
  petContainer: {
    // flex: 1,
    paddingTop: 16,
    // paddingBottom: 16,
  },
  petCardContainer: {
    width: '95%',
    marginBottom: 16,
    marginLeft: 8,
  },
  emptyDesc: {
    fontFamily: FONTS.Medium,
    color: '#A0A0A0',
    fontSize: 9,
    marginTop: 4,
  },
});
