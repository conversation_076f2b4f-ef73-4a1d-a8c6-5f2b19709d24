import {COLORS} from './themes';
import {StyleSheet} from 'react-native';
import {FONTS} from './fonts';
import {moderateScale, verticalScale} from 'react-native-size-matters';

export default StyleSheet.create({
  title: {
    fontSize: verticalScale(18),
    color: COLORS.Bastille,
    fontFamily: FONTS.Bold,
  },
  paragraph: {
    fontSize: verticalScale(14),
    color: COLORS.spanishgray,
    fontFamily: FONTS.Medium,
  },
  socialButtonContainer: {
    height: verticalScale(50),
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 5,
  },
  title2: {
    fontSize: moderateScale(16),
    fontFamily: FONTS.SemiBold,
    color: COLORS.Bastille,
  },
  card: {
    backgroundColor: COLORS.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    borderRadius: 20,
    paddingBottom: 24,
    marginTop: verticalScale(28),
    marginHorizontal: 3,
  },
  innerCard: {
    backgroundColor: COLORS.primary,
    height: verticalScale(70),
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  largeTitle: {
    fontSize: verticalScale(26),
    fontFamily: FONTS.Bold,
    color: COLORS.white,
    textTransform: 'uppercase',
  },
  hitSlop: {
    left: 20,
    right: 30,
    top: 20,
    bottom: 20,
  },
});
