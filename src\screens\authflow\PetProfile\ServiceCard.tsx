import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {COLORS} from '../../../themes/themes';
import {Plus2} from '../../../assets/svgIcons';
import {ButtonwithIcon} from '../../../components';
import {FONTS} from '../../../themes/fonts';

interface Props {
  serviceName: string;
  onPress: () => void;
}

const ServiceCard: React.FC<Props> = ({serviceName, onPress}) => {
  return (
    <View style={styles.container}>
      <View style={styles.innerContainer}>
        <Text style={styles.serviceName}>{serviceName}</Text>
        <View style={styles.priceContainer}>
          <ButtonwithIcon icon={<Plus2 />} onPress={onPress} />
        </View>
      </View>
      <View style={styles.divider}></View>
    </View>
  );
};

export default ServiceCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  divider: {
    height: 1,
    width: '100%',
    backgroundColor: '#EEEEEE',
    marginVertical: 12,
  },
  priceContainer: {
    flexDirection: 'row',
  },
  priceText: {
    marginRight: 12,
  },
  serviceName: {color: COLORS.black, fontSize: 12, fontFamily: FONTS.Medium},
});
