import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingBottom: verticalScale(80),
    alignItems: 'center',
  },
  underlineStyleBase: {
    width: 60,
    height: 48,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },
  underlineStyleHighLighted: {
    width: 60,
    height: 48,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  optView: {
    width: '90%',
    height: 48,
    marginTop: 20,
    alignSelf: 'center',
  },
});
