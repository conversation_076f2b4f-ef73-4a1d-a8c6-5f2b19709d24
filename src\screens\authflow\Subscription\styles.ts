import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {moderateScale, verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  innerContainer: {
    paddingTop: verticalScale(16),
    paddingHorizontal: 24,
  },
  title: {
    fontSize: moderateScale(24),
    fontFamily: FONTS.Bold,
    color: COLORS.black,
  },
  card: {
    backgroundColor: COLORS.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    borderRadius: 20,
    paddingBottom: 24,
    marginTop: 28,
  },
  innerCard: {
    backgroundColor: COLORS.primary,
    height: verticalScale(70),
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  subTitle: {
    fontSize: moderateScale(20),
    fontFamily: FONTS.Bold,
    color: COLORS.white,
  },
  imageContainer: {
    width: verticalScale(24),
    height: verticalScale(24),
  },
  checkCard: {
    paddingTop: verticalScale(30),
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    fontSize: moderateScale(16),
    fontFamily: FONTS.Medium,
    color: '#8A8A8A',
    paddingLeft: 10,
  },
  priceText: {
    paddingTop: verticalScale(50),
    textAlign: 'center',
    fontSize: moderateScale(28),
    color: COLORS.primary,
    fontFamily: FONTS.Medium,
  },
  perYearText: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    textAlign: 'center',
    color: COLORS.primary,
  },
  button: {
    marginTop: verticalScale(42),
  },
});
