import {SafeAreaView, Text, TouchableOpacity, View} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import {
  French,
  German,
  NetherlandFlag,
  TickIcon,
  UnitedKingdom,
} from '../../../assets/svgIcons';
import {verticalScale} from 'react-native-size-matters';
import {useTranslation} from 'react-i18next';
import i18n from '../../../i18n/i18n';
import {useLanguage} from '../../../contexts/LanguageContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {TextButton} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {useAppSelector} from '../../../store/Store';
import {AuthContext} from '../../../../App';
import {styles} from './styles';

type Props = NativeStackScreenProps<AuthStackParamList, 'SelectLanguage'>;

const SelectLanguage: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  const {selectedLanguage, setLanguage} = useLanguage();
  const {firstLaunch} = useContext(AuthContext);

  const [languageInitial, setLanguageInitial] = useState(selectedLanguage);
  const user = useAppSelector(state => state.user);

  const saveSelectedLanguage = async (language: 'en' | 'nl' | 'fr' | 'de') => {
    try {
      await AsyncStorage.setItem('selectedLanguage', language);
      setLanguageInitial(language);
    } catch (error) {
      console.error('Error saving selected language to AsyncStorage:', error);
    }
  };

  useEffect(() => {
    setLanguageInitial(selectedLanguage);
  }, [selectedLanguage]);

  const handleLanguageSelect = (language: 'en' | 'nl' | 'fr' | 'de') => {
    setLanguageInitial(language);
    saveSelectedLanguage(language);
  };

  const handleContinue = async () => {
    await AsyncStorage.setItem('languageData', 'false');
    setLanguage(languageInitial);
    i18n.changeLanguage(languageInitial);
    if (firstLaunch) {
      navigation.navigate('Onboard');
    } else {
      navigation.goBack();
    }
  };
  return (
    <View style={styles.container}>
      <SafeAreaView />

      <View style={styles.innerContainer}>
        <View>
          <Text style={styles.headerText}>{t('Choose Your Language')}</Text>
          <TouchableOpacity
            style={styles.languageContainer}
            onPress={() => handleLanguageSelect('en')}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <UnitedKingdom />
              <Text style={styles.languageText}>{t('English')}</Text>
            </View>
            {languageInitial === 'en' && <TickIcon />}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.languageContainer}
            onPress={() => handleLanguageSelect('nl')}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <NetherlandFlag width="28" height="28" />
              <Text style={styles.languageText}>{t('Dutch')}</Text>
            </View>
            {languageInitial === 'nl' && <TickIcon />}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.languageContainer}
            onPress={() => handleLanguageSelect('fr')}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <French />
              <Text style={styles.languageText}>{t('French')}</Text>
            </View>
            {languageInitial === 'fr' && <TickIcon />}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.languageContainer}
            onPress={() => handleLanguageSelect('de')}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <German width="28" height="28" />
              <Text style={styles.languageText}>{t('Deutsch')}</Text>
            </View>
            {languageInitial === 'de' && <TickIcon />}
          </TouchableOpacity>
        </View>
        <TextButton
          onPress={handleContinue}
          label={t('Continue')}
          containerStyle={{marginBottom: verticalScale(30)}}
        />
      </View>
    </View>
  );
};

export default SelectLanguage;
