import {StyleSheet, View} from 'react-native';
import React, {useEffect, useRef} from 'react';
import {ButtonwithIcon} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import {COLORS} from '../../../themes/themes';
import appStyles from '../../../themes/appStyles';
import {FONTS} from '../../../themes/fonts';
import {verticalScale} from 'react-native-size-matters';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import {WebView} from 'react-native-webview';
import {PetSitterParamList} from '../../../navigation/PetSitterHomeStack';
import axios from 'axios';
import firestore from '@react-native-firebase/firestore';
import {useAppSelector} from '../../../store/Store';
import Toast from 'react-native-toast-message';
import {t} from 'i18next';
import {serverBaseUrl} from '../../../constants';

type PetSitterProps = NativeStackScreenProps<
  PetSitterParamList,
  'PaymentProfile'
>;
type Props = PetSitterProps;

const PaymentProfile: React.FC<Props> = ({navigation, route}) => {
  const user = useAppSelector(state => state.user);

  const uri = route.params?.uri || 'petsit://';
  const webviewRef = useRef(null);

  async function getAccessToken(userId: string | undefined) {
    try {
      const snapshot = await firestore().collection('Users').doc(userId).get();

      if (snapshot.exists()) {
        const data = snapshot.data();
        return data?.access_token || null;
      } else {
        console.warn('No token found for user:', userId);
        return null;
      }
    } catch (error) {
      console.error('Error fetching access token:', error);
      throw error;
    }
  }

  const handleNavigationChange = async (navState: any) => {
    try {
      console.log('navState', navState);
      if (navState.url.includes('oauthCallback')) {
        const url = new URL(navState.url);
        const code = url.searchParams.get('code');

        // Send code to backend for token exchange
        console.log('Auth code:', code);
        /*
      ==== This is stored in Firebase === Through cloud function ===
      */

        // get access_token from firebase
        const accessToken = await getAccessToken(user.uid);
        if (!accessToken) {
          throw new Error('No access token found');
        } else {
          // console.log('Access token:', accessToken);
        }
        // ------------------------- lets check the status of the account by calling CloudFunction of Mollie/me API
        const response = await axios.get(
          `${serverBaseUrl}/getOnboardingStatus`,
          {
            params: {
              access_token: accessToken,
            },
          },
        );
        console.log('Onboarding status:', response.data);
        // Show Toast about account status
        Toast.show({
          type: 'success',
          text1: t('Payment Profile Status'),
          text2: response.data.status,
        });

        // -------- Finally Go Back
        navigation.getParent()?.goBack();
      }
    } catch (error) {
      console.error('Error handling navigation change:', error);
    }
  };
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);

  return (
    <View style={styles.container}>
      <WebView
        source={{uri}}
        style={{flex: 1}}
        ref={webviewRef}
        onNavigationStateChange={handleNavigationChange}
      />
    </View>
  );
};

export default PaymentProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    // paddingTop: verticalScale(8),
    // paddingHorizontal: 24,
  },
});
