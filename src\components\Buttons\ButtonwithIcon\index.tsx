import {StyleSheet, TouchableOpacity, ViewStyle} from 'react-native';
import React from 'react';

interface Props {
  icon: React.ReactNode;
  containerStyle?: ViewStyle;
  onPress?: () => void;
  hitSlop?: object;
}

const ButtonwithIcon: React.FC<Props> = ({
  onPress,
  icon,
  containerStyle,
  hitSlop,
}) => {
  return (
    <TouchableOpacity
      hitSlop={{left: 20, right: 20, bottom: 15, top: 15}}
      onPress={onPress}
      style={[styles.container, containerStyle]}>
      {icon}
    </TouchableOpacity>
  );
};

export default ButtonwithIcon;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  hitSlop: {left: 20, right: 20, bottom: 15, top: 15},
});
