import {useDispatch} from 'react-redux';
import firestore from '@react-native-firebase/firestore';
import {setUser} from '../slices/userSlice';
import {addPet} from '../slices/petSlice';
import {IUser} from '../interfaces';

export default () => {
  const dispatch = useDispatch();

  const updateReduxData = async (uid: string) => {
    try {
      if (uid) {
        const userDoc = await firestore().collection('Users').doc(uid).get();
        const user = userDoc.data();

        const isOwner = user?.userType == 'petOwner' ? 'petOwner' : 'petSitter';

        const payload: IUser = {
          firstName: user?.firstName,
          surName: user?.surName,
          email: user?.email,
          phoneNumber: user?.phoneNumber,
          address: user?.address,
          city: user?.city,
          country: user?.country,
          dateOfBirth: user?.dateOfBirth?.toString(),
          about: user?.about || '',
          idFrontSide: user?.idFrontSide,
          idBackSide: user?.idBackSide,
          userType: isOwner,
          countryCode: user?.countryCode,
          profilePicture: user?.profilePicture,
          uid: uid,
          fcmToken: user?.fcmToken || '',
          coordinates: user?.coordinates,
          accountId: user?.accountId || '',
          profileStatus: user?.profileStatus,
          // customerId: user?.customerId || '',
        };

        dispatch(setUser(payload));
        const petData = await firestore()
          .collection('Pets')
          .where('userId', '==', uid)
          .get();
        const pet = petData.docs.map(doc => ({...doc.data(), key: doc.id}));

        dispatch(addPet(pet));
      }
    } catch (error) {
      console.error('Error updating Redux data:', error);
    }
  };

  return {
    updateReduxData,
  };
};
