import {verticalScale} from 'react-native-size-matters';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  imageProp: {
    width: '100%',
    height: verticalScale(145),
    justifyContent: 'center',
    alignSelf: 'center',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  heading: {
    fontSize: verticalScale(18),
    fontFamily: FONTS.Bold,
    lineHeight: 24,
    color: '#2C2C2E',
    marginLeft: 24,
    marginTop: verticalScale(8),
  },
  rowContainer: {
    flexDirection: 'row',

    marginHorizontal: 8,
    marginTop: 10,
  },
  nameText: {
    fontSize: 14,
    fontFamily: FONTS.SemiBold,
    lineHeight: 17,
    color: '#2C2C2E',
  },
  ratingContainer: {
    flexDirection: 'row',
  },
  ratingText: {
    fontSize: 10,
    fontFamily: FONTS.Medium,
    color: '#2C2C2E',
  },
  textStyle: {
    fontSize: 10,
    fontFamily: FONTS.Medium,
    color: '#989898',
    marginLeft: 2,
    lineHeight: 12,
  },
  icons: {
    width: 12,
    height: 12,
  },
  textViewStyle: {
    flexDirection: 'row',
    marginHorizontal: 10,
    marginTop: 8,
  },
  buttonContainer: {
    justifyContent: 'space-between',
    marginBottom: 14,
    marginTop: 18,
  },
  cancelButton: {
    width: '47%',
    backgroundColor: COLORS.white,
    borderColor: COLORS.primary,
    borderWidth: 1,
    color: COLORS.primary,
  },
  messageButton: {
    width: '47%',
  },
  innerContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.12,
    shadowRadius: 4,
    elevation: 2,
    marginHorizontal: 20,
    marginTop: 24,
    width: '100%',
    alignSelf: 'center',
    paddingBottom: 10,
  },
  emptyText: {
    fontFamily: FONTS.SemiBold,
    marginTop: 30,
    alignSelf: 'center',
    color: COLORS.Philippine_Gray,
  },
  tabButton: {
    borderColor: COLORS.primary,
    borderWidth: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 5,
    marginRight: 20,
    height: 42,
  },
  tabText: {
    color: COLORS.primary,
    fontFamily: FONTS.SemiBold,
    fontSize: 14,
    lineHeight: 17,
  },
  tabContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  messageButtonStyle: {
    marginTop: 28,
    marginHorizontal: 10,
    marginBottom: 14,
    width: '95%',
    alignSelf: 'center',
  },
  profilePicture: {
    height: 30,
    width: 30,
    borderRadius: 100,
    marginRight: 6,
  },
  loader: {alignSelf: 'center', marginTop: 100},
  cityContainer: {flexDirection: 'row', marginTop: 3},
  flatListContainer: {paddingHorizontal: 24, paddingBottom: 50},
});
