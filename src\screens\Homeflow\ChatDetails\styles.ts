import {Platform, StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
    paddingBottom:
      Platform.OS === 'ios' ? verticalScale(30) : verticalScale(14),
  },
  leftTextStyle: {
    color: '#474747',
    fontFamily: FONTS.Medium,
  },
  rightTextStyle: {
    color: '#474747',
    fontFamily: FONTS.Medium,
  },
  containerStyle: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  leftTimeTextStyle: {
    color: 'rgba(0, 0, 0, 0.46)',
    fontSize: 10,
    textAlign: 'left', // or position: 'right',
    fontFamily: FONTS.Medium,
    position: 'absolute',
    bottom: -22,
  },
  rightTimeTextStyle: {
    color: 'rgba(0, 0, 0, 0.46)',
    fontSize: 10,
    textAlign: 'right', // or position: 'right',
    fontFamily: FONTS.Medium,
    position: 'absolute',
    bottom: -22,
    left: -22,
  },
  rightWrapperStyle: {
    backgroundColor: '#F3F3F3',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 0,
    marginBottom: 32,
    paddingHorizontal: 10,
    paddingVertical: 3,
  },
  leftWrapperStyle: {
    backgroundColor: 'rgba(255, 140, 14, 0.07)',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
    borderBottomLeftRadius: 0,
    marginBottom: 32,
    paddingHorizontal: 10,
    paddingVertical: 3,
    marginLeft: -50,
  },
  rightAction: {
    position: 'absolute',
    bottom: -2,
    right: -65,
    borderWidth: 1,
    borderColor: '#E8E6EA',
    borderRadius: 6,
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inputToolbarContainer: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderTopWidth: 1,
    borderColor: '#E8E6EA',
    borderTopColor: '#E8E6EA',
    borderRadius: 6,
    width: '82%',
    // marginBottom: Platform.OS === 'android' ? 20 : 0,
  },
  actionsContainer: {
    alignItems: 'center',
    position: 'absolute',
    right: -40,
    bottom: 10,
  },
  sendText: {
    color: COLORS.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  outerButtonContainer: {
    backgroundColor: COLORS.white,
    padding: 12,
    borderRadius: 16,
    width: '80%',
    alignSelf: 'center',
  },

  button: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    backgroundColor: COLORS.white,
    borderRadius: 40,
  },
  buttonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: verticalScale(12),
    justifyContent: 'space-evenly',
    paddingHorizontal: verticalScale(12),
  },
  innerButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.black,
    paddingBottom: 16,
  },
  text: {
    fontSize: 12,
    fontFamily: FONTS.Medium,
    color: COLORS.black,
    marginTop: verticalScale(8),
  },
  textFooterChat: {
    color: '#FFFFFF',
    fontSize: 12,
  },
  buttonFooterChatImg: {
    backgroundColor: '#000',
    borderRadius: 155,
    padding: 5,
  },
  chatFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: '#F0F0F0',
    marginBottom: 10,
  },
  emptyChat: {
    textAlign: 'center',
    fontFamily: FONTS.SemiBold,
    color: COLORS.Philippine_Gray,
    marginTop: 200,
  },
  footerText: {
    fontFamily: FONTS.SemiBold,
    fontSize: 14,
    color: COLORS.Philippine_Gray,
    textAlign: 'center',
  },
  cameraIcon: {width: 24, height: 24},
});
