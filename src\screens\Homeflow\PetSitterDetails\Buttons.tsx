import {StyleSheet, View, ViewStyle} from 'react-native';
import React from 'react';
import {COLORS} from '../../../themes/themes';
import {TextButton} from '../../../components';
import {useTranslation} from 'react-i18next';

interface Props {
  selectedButton: string;
  onAboutPress: () => void;
  onReviewPress: () => void;
  onServicesPress?: () => void;
  isServicesVisible?: boolean;
  isReviewsVisible?: boolean;
}

const Buttons: React.FC<Props> = ({
  selectedButton,
  onAboutPress,
  onReviewPress,
  onServicesPress,
  isServicesVisible = false,
  isReviewsVisible = false,
}) => {
  const {t} = useTranslation();
  return (
    <View style={styles.buttonContainer}>
      {isServicesVisible && (
        <TextButton
          label={t('Services')}
          labelStyle={{
            color:
              selectedButton === 'Services' ? COLORS.white : COLORS.primary,
          }}
          containerStyle={
            [
              styles.button,
              selectedButton === 'Services' && styles.selectedButton,
            ] as ViewStyle
          }
          onPress={onServicesPress}
        />
      )}
      <TextButton
        label={t('About')}
        labelStyle={{
          color: selectedButton == 'About' ? COLORS.white : COLORS.primary,
        }}
        containerStyle={
          [
            styles.button,
            selectedButton === 'About' && styles.selectedButton,
          ] as ViewStyle
        }
        onPress={onAboutPress}
      />
      {isReviewsVisible && (
        <TextButton
          label={t('Reviews')}
          labelStyle={{
            color: selectedButton === 'Reviews' ? COLORS.white : COLORS.primary,
          }}
          containerStyle={
            [
              styles.button,
              selectedButton === 'Reviews' && styles.selectedButton,
            ] as ViewStyle
          }
          onPress={onReviewPress}
        />
      )}
    </View>
  );
};

export default Buttons;

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 24,
  },
  button: {
    width: '47%',
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },
  selectedButton: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
});
