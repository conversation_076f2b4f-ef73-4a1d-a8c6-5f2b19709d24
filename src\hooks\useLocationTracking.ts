import {useEffect} from 'react';
import Geolocation from 'react-native-geolocation-service';
import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';
import Toast from 'react-native-toast-message';

type Location = {
  latitude: number;
  longitude: number;
};

type UseLocationTrackingProps = {
  onLocationChange: (location: Location) => void;
};

const useLocationTracking = ({onLocationChange}: UseLocationTrackingProps) => {
  useEffect(() => {
    const getLocation = async () => {
      try {
        const permissionStatus = await check(
          PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
        );

        if (permissionStatus === RESULTS.GRANTED) {
          getCurrentPosition();
        } else if (permissionStatus === RESULTS.DENIED) {
          const requestResult = await request(
            PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
          );

          if (requestResult === RESULTS.GRANTED) {
            getCurrentPosition();
          } else {
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: 'Sorry, could not get location access',
            });
          }
        }
      } catch (error) {
        console.log('Error checking or requesting location permission:', error);
      }
    };

    const getCurrentPosition = () => {
      Geolocation.getCurrentPosition(
        (position: Geolocation.Position) => {
          onLocationChange({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
        },
        (error: Geolocation.PositionError) => {
          console.log('Error getting current position:', error);
        },
        {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
      );
    };

    getLocation();
  }, [onLocationChange]);
};

export default useLocationTracking;
