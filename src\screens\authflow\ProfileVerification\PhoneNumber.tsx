import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  NativeSyntheticEvent,
  TextInputFocusEventData,
} from 'react-native';
import React, {useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {ArrowDown} from '../../../assets/svgIcons';
import CountryPicker, {
  CountryCode,
  Country,
} from 'react-native-country-picker-modal';
import {moderateScale, verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

interface Props {
  onChangeText?: (text: string) => void;
  onBlur?: (e: NativeSyntheticEvent<TextInputFocusEventData>) => void;
  isPassword?: boolean;
  placeholder: string;
  placeholderTextColor: string;
  label: string;
  onCountryCodeChange: (countryCode: string) => void;
  userCountryCode?: string;
  selectedCode?: CountryCode;
}

const PhoneNumber: React.FC<Props> = ({
  onChangeText,
  onBlur,
  placeholder,
  placeholderTextColor,
  label,
  onCountryCodeChange,
  userCountryCode,
  selectedCode,
  ...rest
}) => {
  const initialCallingCode = selectedCode || '1';
  const [callingCode, setCallingCode] = useState<string>(initialCallingCode);
  const [visible, setVisible] = useState(false);
  const [countryCodes, setCountryCode] = useState<CountryCode>('AD');

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.innerContainer}>
        <TouchableOpacity
          style={styles.codePicker}
          onPress={() => setVisible(true)}>
          <Text
            style={[
              styles.label,
              {color: callingCode ? COLORS.black : COLORS.placeHolder},
            ]}>
            {callingCode}
          </Text>
          <ArrowDown />
          <CountryPicker
            withFlag={true}
            withFlagButton={false}
            withFilter
            countryCode={countryCodes}
            withAlphaFilter={true}
            withCurrencyButton={false}
            withCallingCode={true}
            containerButtonStyle={{backgroundColor: 'white'}}
            onSelect={(country: Country) => {
              const {cca2, callingCode} = country;
              setCallingCode(callingCode[0]);
              onCountryCodeChange(callingCode[0]);
              setCountryCode(cca2);
            }}
            visible={visible}
            onClose={() => setVisible(false)}
          />
        </TouchableOpacity>
        <View style={styles.phoneNumber}>
          <TextInput
            placeholder={placeholder}
            placeholderTextColor={placeholderTextColor}
            onChangeText={onChangeText}
            onBlur={onBlur}
            returnKeyType="done"
            keyboardType="phone-pad"
            style={{color: COLORS.black}}
            {...rest}
          />
        </View>
      </View>
    </View>
  );
};

export default PhoneNumber;

const styles = StyleSheet.create({
  container: {
    marginTop: 6,
  },
  label: {
    fontSize: moderateScale(14),
    color: COLORS.Bastille,
    marginBottom: verticalScale(6),
    fontFamily: FONTS.Medium,
    marginTop: 6,
    paddingHorizontal: 2,
  },
  innerContainer: {
    flexDirection: 'row',
    marginTop: 6,
  },
  codePicker: {
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    height: 48,
    // width: 70,
    marginRight: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    borderRadius: 5,
  },
  phoneNumber: {
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    backgroundColor: COLORS.white,
    height: 48,
    flex: 1,
    justifyContent: 'center',
    paddingLeft: 8,
    borderRadius: 5,
  },
});
