import {Image, Platform, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';

import {Settings, Chats, PetSitterHome} from '../../screens/Homeflow';
import {Home2, Setting, Message2} from '../../assets/images';
import {COLORS} from '../../themes/themes';
import {useTranslation} from 'react-i18next';
import useChat from '../../hooks/useChat';
import {FONTS} from '../../themes/fonts';
import {IConversationBooking} from '../../interfaces/IBooking';

export type SitterBottomStackParamList = {
  PetSitterHome: undefined;
  Chats: undefined;
  Settings: undefined;
  Notification: undefined;
  PetDetails: {id: string};
  ChatDetails: {
    recipientId: string;
    serviceDone: boolean;
    booking?: IConversationBooking;
  };
  TermofUse: undefined;
  PrivacyPolicy: undefined;
  MyPets: undefined;
  PetProfile: undefined;
  AddService: undefined;
  MyReviews: undefined;
  EmailVerification: undefined;
  CodeVerification: {screen: string};
  ResetPassword: undefined;
  SuccessFeedback: {
    isPassword: boolean;
    isAccountCreated: boolean;
    isProfileCreated: boolean;
  };
  ContactUs: undefined;
  DeleteAccount: undefined;
};

const BottomStack = createBottomTabNavigator<SitterBottomStackParamList>();

const PetSitterBottomTab = () => {
  const {t} = useTranslation();
  const {unreadConversationsCount} = useChat();

  interface Props {
    icon: React.ReactElement;
  }

  const renderTabBarIcon: React.FC<Props> = ({icon}) => {
    return icon;
  };
  return (
    <BottomStack.Navigator
      screenOptions={{
        headerShown: false,
        headerShadowVisible: false,
        tabBarStyle: styles.tabBarStyle,
        tabBarHideOnKeyboard: true,
      }}>
      <BottomStack.Screen
        name="PetSitterHome"
        component={PetSitterHome}
        options={{
          tabBarIcon: ({focused}) =>
            renderTabBarIcon({
              icon: (
                <Image
                  resizeMode="contain"
                  source={Home2}
                  style={[
                    styles.icon,
                    {tintColor: focused ? COLORS.primary : COLORS.gray85},
                  ]}
                />
              ),
            }),
          tabBarActiveTintColor: COLORS.primary,
          tabBarInactiveTintColor: COLORS.gray85,
          tabBarLabel: t('Home'),
        }}
      />
      <BottomStack.Screen
        name="Chats"
        component={Chats}
        options={{
          tabBarIcon: ({focused}) =>
            renderTabBarIcon({
              icon: (
                <View>
                  {unreadConversationsCount > 0 ? (
                    <View style={styles.numberContainer}>
                      <Text style={styles.numberText}>
                        {unreadConversationsCount}
                      </Text>
                    </View>
                  ) : null}
                  <Image
                    resizeMode="contain"
                    source={Message2}
                    style={[
                      styles.icon,
                      {tintColor: focused ? COLORS.primary : COLORS.gray85},
                    ]}
                  />
                </View>
              ),
            }),
          tabBarLabel: t('Messages'),
          tabBarActiveTintColor: COLORS.primary,
          tabBarInactiveTintColor: COLORS.gray85,
        }}
      />
      <BottomStack.Screen
        name="Settings"
        component={Settings}
        options={{
          tabBarIcon: ({focused}) =>
            renderTabBarIcon({
              icon: (
                <Image
                  resizeMode="contain"
                  source={Setting}
                  style={[
                    styles.icon,
                    {tintColor: focused ? COLORS.primary : COLORS.gray85},
                  ]}
                />
              ),
            }),
          tabBarActiveTintColor: COLORS.primary,
          tabBarInactiveTintColor: COLORS.gray85,
          tabBarLabel: t('Settings'),
        }}
      />
    </BottomStack.Navigator>
  );
};

export default PetSitterBottomTab;

const styles = StyleSheet.create({
  icon: {
    width: 24,
    height: 24,
  },
  tabBarStyle: {
    height: 76,
    marginHorizontal: 29,
    borderRadius: 42,
    marginBottom: Platform.OS === 'android' ? 10 : 26,
    paddingBottom: 8,
    borderTopColor: COLORS.white,
    position: 'absolute',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  numberText: {
    fontFamily: FONTS.Medium,
    color: COLORS.white,
    alignSelf: 'center',
    fontSize: 12,
  },
  numberContainer: {
    position: 'absolute',
    right: -8,
    top: -8,
    backgroundColor: COLORS.primary,
    borderRadius: 100,
    width: 20,
    height: 20,
    justifyContent: 'center',
    zIndex: 100,
  },
});
