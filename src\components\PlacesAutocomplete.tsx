import React, {useState, useCallback} from 'react';
import {
  View,
  TextInput,
  FlatList,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInputProps,
} from 'react-native';
import axios from 'axios';
import debounce from 'lodash.debounce';
import {GooglePlaceDetail} from 'react-native-google-places-autocomplete';
import FormInput from './FormInput';
import {ViewStyle} from 'react-native-size-matters';

interface Prediction {
  description: string;
  place_id: string;
}

interface PlaceDetails {
  result: {
    name: string;
    formatted_address: string;
    geometry: {
      location: {
        lat: number;
        lng: number;
      };
    };
    place_id: string;
    [key: string]: any;
  };
  status: string;
}

interface Props {
  apiKey: string;
  onPlaceSelected: (
    prediction: Prediction,
    details?: GooglePlaceDetail,
  ) => void;
  countryCode?: string;
  fetchDetails?: boolean;
  containerStyle?: ViewStyle;
  label?: string;
  textInputProps?: TextInputProps;
}

const PlacesAutocomplete: React.FC<Props> = ({
  apiKey,
  onPlaceSelected,
  countryCode,
  fetchDetails = false,
  containerStyle,
  label,
  textInputProps,
}) => {
  const [input, setInput] = useState('');
  const [predictions, setPredictions] = useState<Prediction[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchPredictions = async (text: string) => {
    console.log('Fetch predictiosn', text);

    if (!text) {
      setPredictions([]);
      return;
    }

    try {
      setLoading(true);
      const response = await axios.get(
        'https://maps.googleapis.com/maps/api/place/autocomplete/json',
        {
          params: {
            input: text,
            key: apiKey,
            components: countryCode ? `country:${countryCode}` : 'be',
            types: 'geocode',
          },
        },
      );

      if (response.data.status === 'OK') {
        setPredictions(response.data.predictions);
      } else {
        setPredictions([]);
        throw new Error(
          response.data.error_message || 'Unknown error in search',
        );
      }
    } catch (error) {
      console.error('Autocomplete error:', error);
      setPredictions([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchPlaceDetails = async (
    placeId: string,
  ): Promise<GooglePlaceDetail | undefined> => {
    try {
      const res = await axios.get(
        'https://maps.googleapis.com/maps/api/place/details/json',
        {
          params: {
            place_id: placeId,
            key: apiKey,
            fields: [
              'address_component',
              'adr_address',
              'formatted_address',
              'geometry',
              'icon',
              'id',
              'name',
              'place_id',
              'plus_code',
              'reference',
              'scope',
              'types',
              'url',
              'utc_offset',
              'vicinity',
            ].join(','),
          },
        },
      );

      if (res.data.status !== 'OK') {
        console.warn('Place details fetch failed:', res.data.status);
        return undefined;
      }

      const r = res.data.result;

      const mapped: GooglePlaceDetail = {
        ...r,
        addressComponents: r.address_components,
        adrFormatAddress: r.adr_address,
        formattedAddress: r.formatted_address,
        location: {
          lat: r.geometry?.location?.lat,
          lng: r.geometry?.location?.lng,
        },
      };

      return mapped;
    } catch (err) {
      console.error('Error fetching place details:', err);
      return undefined;
    }
  };

  const debouncedFetch = useCallback(debounce(fetchPredictions, 500), []);

  const handleChange = (text: string) => {
    setInput(text);
    debouncedFetch(text);
  };

  const handleSelect = async (place: Prediction) => {
    setInput(place.description);
    setPredictions([]);

    if (fetchDetails) {
      const details = await fetchPlaceDetails(place.place_id);
      onPlaceSelected(place, details);
    } else {
      onPlaceSelected(place);
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <FormInput
        value={input}
        onChangeText={handleChange}
        // style={styles.input}
        label={label}
        containerStyle={{marginTop: 0}}
        {...textInputProps}
      />

      {predictions.length > 0 && (
        <FlatList
          data={predictions}
          keyExtractor={item => item.place_id}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={() => handleSelect(item)}
              style={styles.item}>
              <Text>{item.description}</Text>
            </TouchableOpacity>
          )}
          style={styles.dropdown}
          keyboardShouldPersistTaps="handled"
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  input: {
    height: 40,
    borderWidth: 1,
    borderColor: '#999',
    paddingHorizontal: 10,
    borderRadius: 5,
    backgroundColor: '#fff',
  },
  dropdown: {
    backgroundColor: '#fff',
    borderColor: '#ccc',
    borderWidth: 1,
    borderTopWidth: 0,
    maxHeight: 200,
  },
  item: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
});

export default PlacesAutocomplete;
