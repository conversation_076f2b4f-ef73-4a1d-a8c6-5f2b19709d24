import {StyleSheet, View} from 'react-native';
import React, {useEffect} from 'react';
import {ActionFeedBack} from '../../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../../navigation/AuthStack';
import {COLORS} from '../../../../themes/themes';
import {SuccesState2} from '../../../../assets/images';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<
  AuthStackParamList,
  'PetOwnerAccountFeedback'
>;

const PetOwnerAccountFeedback: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();

  useEffect(() => {
    const timeout = setTimeout(() => {
      navigation.navigate('PetProfile', {});
    }, 1000);

    return () => clearTimeout(timeout);
  }, []);

  return (
    <View style={styles.container}>
      <ActionFeedBack
        image={SuccesState2}
        title={t('Account Created')}
        paragraph={t(
          'Your account is created. A verification has sent to your email address.',
        )}
        buttonTitle={t('Go to Email')}
        showActionButton
        showSkipButton
        isWhite
        onSkipPress={() => navigation.navigate('PetProfile', {})}
      />
    </View>
  );
};

export default PetOwnerAccountFeedback;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingBottom: 80,
  },
});
