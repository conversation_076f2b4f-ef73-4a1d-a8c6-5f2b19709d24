import {StyleSheet} from 'react-native';
import {scale} from 'react-native-size-matters';
import {COLORS} from '../../themes/themes';
import {FONTS} from '../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    borderRadius: 10,
    width: scale(144),
    backgroundColor: COLORS.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
    marginBottom: 5,
    marginHorizontal: 2,
  },
  imageContainer: {
    width: '100%',
    height: 140,
    overflow: 'hidden',
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
  },
  innerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 8,
    paddingHorizontal: 8,
  },
  label: {
    fontSize: 12,
    color: COLORS.Bastille,
    fontFamily: FONTS.Medium,
    marginLeft: 2,
  },
  name: {
    fontSize: 14,
    color: COLORS.Bastille,
  },
  icon: {
    width: 8,
    height: 8,
  },
  label2: {
    fontSize: 10,
    fontFamily: FONTS.Medium,
    color: '#989898',
    marginLeft: 4,
  },
  infoContainer: {paddingTop: 6, paddingBottom: 14},
  activityIndicator: {
    position: 'absolute',
    top: 70,
  },
  locationContainer: {flexDirection: 'row', marginTop: 4},
  ownerContainer: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginRight: 4,
  },
  ownerImage: {height: 24, width: 24, borderRadius: 100, marginRight: 5},
  ownerName: {marginTop: 5, paddingBottom: 10},
});
