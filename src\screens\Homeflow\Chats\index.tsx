import {
  ActivityIndicator,
  FlatList,
  Platform,
  SafeAreaView,
  Text,
  View,
} from 'react-native';
import React, {useEffect} from 'react';
import {COLORS} from '../../../themes/themes';
import ChatCard from './ChatCard';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {OwnerBottomStackParamList} from '../../../navigation/PetOwnerBottomTabs';
import {verticalScale} from 'react-native-size-matters';
import useChat from '../../../hooks/useChat';
import {IChat, IUser} from '../../../interfaces';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';
import moment from 'moment';

import {ChatEmptyState} from '../../../assets/svgIcons';
import {BannerAdComponent} from '../../../components/Admob';

type Props = NativeStackScreenProps<OwnerBottomStackParamList, 'Chats'>;

const Chats: React.FC<Props> = ({navigation}) => {
  const {getConversations, conversations, isConversationLoading} = useChat();
  const {t} = useTranslation();

  useEffect(() => {
    getConversations();
  }, []);

  const handleChatPress = (recipientData?: IUser, item?: IChat) => {
    if (recipientData)
      navigation.navigate('ChatDetails', {
        recipientId: recipientData.uid,
        serviceDone: item?.serviceDone || false,
        booking: item?.booking,
      });
  };

  return (
    <SafeAreaView style={styles.container}>
      <BannerAdComponent />
      <View style={styles.innerContainer}>
        <Text style={styles.title}>{t('Messages')}</Text>

        {isConversationLoading ? (
          <ActivityIndicator
            style={{marginTop: 200}}
            color={COLORS.primary}
            size={'large'}
          />
        ) : (
          <FlatList
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              paddingBottom:
                Platform.OS === 'android'
                  ? verticalScale(158)
                  : verticalScale(100),
            }}
            data={conversations}
            renderItem={({item}) => {
              return (
                <ChatCard
                  item={item}
                  handleChatPress={handleChatPress}
                  message={item.lastMessage.text}
                  time={moment(item.lastMessage.createdAt.toDate()).format(
                    'hh:mm a',
                  )}
                />
              );
            }}
            ListEmptyComponent={() => (
              <View style={{alignItems: 'center', marginTop: 80}}>
                <ChatEmptyState />
                <Text style={styles.emptyText}>{t('No Conversation')}</Text>
                <Text style={styles.emptyDesc}>
                  {t('There are no chats in your feed')}
                </Text>
              </View>
            )}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default Chats;
