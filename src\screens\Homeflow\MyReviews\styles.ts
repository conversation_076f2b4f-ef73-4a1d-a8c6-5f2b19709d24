import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {verticalScale} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 24,
  },
  label: {
    fontSize: 10,
    color: COLORS.Bastille,
    fontFamily: FONTS.Medium,
    marginLeft: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 20,
  },
  contentContainerStyle: {
    marginTop: verticalScale(22),
    paddingBottom: verticalScale(60),
  },
  emptyText: {
    marginTop: 30,
    fontFamily: FONTS.SemiBold,
    fontSize: 14,
    textAlign: 'center',
    color: COLORS.Philippine_Gray,
  },
});
