import {StyleSheet} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  innerContainer: {
    paddingHorizontal: 24,
    paddingTop: 16,
    flex: 1,
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 24,
    paddingBottom: 10,
  },
  filterButton: {
    width: 48,
    height: 48,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 5,
  },
  PetContainer: {
    flex: 1,
  },

  noResultText: {
    fontFamily: FONTS.SemiBold,
    textAlign: 'center',
    marginTop: 20,
    fontSize: 12,
  },
  cancelFilter: {
    color: COLORS.primary,
    fontFamily: FONTS.SemiBold,
    fontSize: 14,
  },
  cancelFilterContainer: {
    marginTop: 20,
    alignItems: 'flex-end',
  },
  headerContainer: {
    marginTop: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileImage: {
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    marginRight: 10,
    alignItems: 'center',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  userName: {
    fontSize: 10,
    color: '#595959',
    fontFamily: FONTS.Medium,
    marginLeft: 4,
    width: '70%',
  },
  bellIconContainer: {
    width: 30,
    height: 30,
    backgroundColor: COLORS.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
    borderRadius: 30 / 2,
  },
  notificationContainer: {
    position: 'absolute',
    right: -8,
    top: -8,
    backgroundColor: COLORS.primary,
    borderRadius: 100,
    width: 20,
    height: 20,
    justifyContent: 'center',
  },
  notifcationNumber: {
    fontFamily: FONTS.Medium,
    color: COLORS.white,
    alignSelf: 'center',
    fontSize: 12,
  },
  emptyText: {
    fontFamily: FONTS.SemiBold,
    marginTop: 27,
    fontSize: 10,
    color: '#939090',
  },
  emptyDesc: {
    fontFamily: FONTS.Medium,
    color: '#A0A0A0',
    fontSize: 9,
    marginTop: 4,
  },
});
