import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {moderateScale, verticalScale} from 'react-native-size-matters';
import {SIZES} from '../../themes/themes';
import {useTranslation} from 'react-i18next';

const Divider = () => {
  const {t} = useTranslation();

  return (
    <View style={styles.dividerContainer}>
      <View style={styles.line} />
      <Text style={styles.dividerText}>{t('or')}</Text>
      <View style={styles.line} />
    </View>
  );
};

export default Divider;

const styles = StyleSheet.create({
  dividerContainer: {
    flexDirection: 'row',
    paddingVertical:
      SIZES.DeviceHeight > 760 ? verticalScale(16) : verticalScale(10),
    alignItems: 'center',
  },
  line: {
    height: 1,
    flex: 1,
    backgroundColor: '#C1C1C1',
  },
  dividerText: {
    fontSize: moderateScale(16),
    color: '#C1C1C1',
    paddingHorizontal: 8,
  },
});
