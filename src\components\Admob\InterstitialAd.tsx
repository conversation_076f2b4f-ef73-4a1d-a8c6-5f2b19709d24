import React, {useEffect} from 'react';
import {Platform, View} from 'react-native';
import {InterstitialAd, AdEventType} from 'react-native-google-mobile-ads';
const interstitialAd = InterstitialAd.createForAdRequest(
  Platform.OS === 'android'
    ? 'ca-app-pub-3942822984828389/9437398512'
    : 'ca-app-pub-3942822984828389/6476435824',
  {
    keywords: ['animals', 'pets'],
  },
);
const InterstitialAdComponent = () => {
  useEffect(() => {
    const unsubscribe = interstitialAd.addAdEventListener(
      AdEventType.LOADED,
      () => {
        interstitialAd.show();
      },
    );
    interstitialAd.load();
    return () => {
      unsubscribe();
    };
  }, []);
  return <View></View>;
};
export default InterstitialAdComponent;
