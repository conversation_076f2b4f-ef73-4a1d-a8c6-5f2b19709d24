import {StyleSheet} from 'react-native';
import {COLORS, SIZES} from '../../themes/themes';
import {verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop:
      SIZES.DeviceHeight > 760 ? verticalScale(170) : verticalScale(120),
    alignItems: 'center',
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  imageContainer: {
    width: verticalScale(240),
    height: verticalScale(240),
  },
  innerContainer: {
    alignItems: 'center',
  },
  buttonsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  skipButton: {
    marginTop: 24,
  },
  buttonLabel: {
    fontSize: 14,
    fontFamily: FONTS.Medium,
    color: COLORS.primary,
  },
});
