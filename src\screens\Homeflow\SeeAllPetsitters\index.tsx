import {
  ActivityIndicator,
  FlatList,
  SafeAreaView,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import firestore from '@react-native-firebase/firestore';
import {ButtonwithIcon, PetCard} from '../../../components';
import {COLORS} from '../../../themes/themes';
import appStyles from '../../../themes/appStyles';
import {BackArrow} from '../../../assets/svgIcons';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {OwnerBottomStackParamList} from '../../../navigation/PetOwnerBottomTabs';
import {styles} from './styles';
import {IUser} from '../../../interfaces';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<
  OwnerBottomStackParamList,
  'PetOwnerSeeAll'
>;

const PetOwnerSeeAll: React.FC<Props> = ({navigation}) => {
  const [petSitters, setPetSitters] = useState<IUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const {t} = useTranslation();
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);

  useEffect(() => {
    const fetchPetSitters = async () => {
      try {
        const querySnapshot = await firestore()
          .collection('Users')
          .where('userType', '==', 'petSitter')
          .get();

        const petSittersData = querySnapshot.docs.map(
          doc =>
            ({
              ...doc.data(),
              uid: doc.id,
            } as IUser),
        );
        setPetSitters(petSittersData);
      } catch (error) {
        console.log('Error fetching non-owners:', error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchPetSitters();
  }, []);
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.innerContainer}>
        <Text style={[appStyles.title2, {marginBottom: 20}]}>
          {t('All pet sitters')}
        </Text>
        {isLoading ? (
          <ActivityIndicator
            size="large"
            color={COLORS.primary}
            style={{marginTop: 30}}
          />
        ) : (
          <FlatList
            showsVerticalScrollIndicator={false}
            columnWrapperStyle={{justifyContent: 'space-between'}}
            data={petSitters}
            numColumns={2}
            contentContainerStyle={{paddingBottom: 40}}
            renderItem={({item}) => {
              return (
                <PetCard
                  image={item.profilePicture}
                  name={item.firstName + ' ' + item.surName || ''}
                  location={item.city}
                  experience={item.experience}
                  rating={item.rating}
                  onPress={() =>
                    navigation.navigate('PetSitterDetails', {petSitter: item})
                  }
                  containerStyle={{marginBottom: 16}}
                />
              );
            }}
            ListEmptyComponent={
              <Text style={styles.noResultText}>
                {t('No pet sitters Found')}
              </Text>
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default PetOwnerSeeAll;
