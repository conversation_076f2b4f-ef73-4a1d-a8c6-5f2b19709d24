import {StyleSheet, View, ViewStyle} from 'react-native';
import React from 'react';
import {COLORS} from '../../../themes/themes';
import {TextButton} from '../../../components';
import {useTranslation} from 'react-i18next';

interface Props {
  selectedButton: string;
  onNewPress: () => void;
  onHistoryPress: () => void;
  onServicesPress?: () => void;
  isServicesVisible?: boolean;
  isReviewsVisible?: boolean;
}

const Buttons: React.FC<Props> = ({
  selectedButton,
  onNewPress,
  onHistoryPress,
}) => {
  const {t} = useTranslation();
  const renderButton = (
    label: string,
    isSelected: boolean,
    onPress: () => void,
  ) => (
    <TextButton
      label={t(label)}
      labelStyle={{color: isSelected ? COLORS.white : COLORS.primary}}
      containerStyle={
        [
          styles.button,
          {backgroundColor: isSelected ? COLORS.primary : COLORS.white},
        ] as ViewStyle
      }
      onPress={onPress}
    />
  );

  return (
    <View style={styles.buttonContainer}>
      {renderButton('New', selectedButton === 'New', onNewPress)}
      {renderButton('History', selectedButton === 'History', onHistoryPress)}
    </View>
  );
};

export default Buttons;

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 24,
  },
  button: {
    width: '47%',
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
});
