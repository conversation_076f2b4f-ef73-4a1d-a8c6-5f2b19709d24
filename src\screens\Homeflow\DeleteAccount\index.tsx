import {ScrollView, Text, TouchableOpacity, View} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import {COLORS} from '../../../themes/themes';
import {
  AppText,
  ButtonwithIcon,
  FormInput,
  TextButton,
} from '../../../components';
import {BackArrow, Check2} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {verticalScale} from 'react-native-size-matters';
import firestore, {firebase} from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import {useTogglePasswordVisibility} from '../../../hooks';
import {AuthContext} from '../../../../App';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {styles} from './styles';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import {useAppSelector} from '../../../store/Store';
import Message from '../ContactUs/Message';
import storage from '@react-native-firebase/storage';

type Props = NativeStackScreenProps<PetOwnerParamList, 'DeleteAccount'>;

const DeleteAccount: React.FC<Props> = ({navigation}) => {
  const [selected, setSelected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const {setUserId} = useContext(AuthContext);
  const {t} = useTranslation();
  const user = useAppSelector(state => state.user);
  const pet = useAppSelector(state => state.pets);

  const validationSchema = Yup.object().shape({
    firstName: Yup.string().required(t('Enter your first name')),
    surName: Yup.string().required(t('Enter your sur name')),
    email: Yup.string()
      .required(t('Enter your email'))
      .email(t('Email must be a valid email')),
    password: Yup.string().required(t('Enter your password')),
    message: Yup.string().required(t('Enter your message')),
    deleteCheck: Yup.boolean().isTrue(),
  });

  const toggleButton = () => {
    setSelected(!selected);
    formik.setFieldValue('deleteCheck', !selected);
  };
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);
  const reauthenticate = () => {
    let user = firebase.auth().currentUser;
    let cred = firebase.auth.EmailAuthProvider.credential(
      user?.email || '',
      formik.values.password || '',
    );
    return user?.reauthenticateWithCredential(cred);
  };
  const deleteUserData = async () => {
    try {
      await firestore().collection('Users').doc(user.uid).delete();
    } catch (error) {
      console.log('Error deleting user data:', error);
    }
  };
  const handleLogout = () => {
    setTimeout(() => {
      setUserId('');
      AsyncStorage.removeItem('UserId');
    }, 1000);
  };
  const deleteAllUserPets = async () => {
    try {
      const querySnapshot = await firestore()
        .collection('Pets')
        .where('userId', '==', user.uid)
        .get();

      const batch = firestore().batch();
      querySnapshot.forEach(documentSnapshot => {
        batch.delete(documentSnapshot.ref);
      });
      return batch.commit();
    } catch (error) {
      console.log('Error deleting user pets:', error);
    }
  };
  const deleteConversations = async () => {
    try {
      const querySnapshot = await firestore()
        .collection('Conversations')
        .where('members', 'array-contains', user.uid)
        .get();

      const batch = firestore().batch();
      querySnapshot.forEach(documentSnapshot => {
        batch.delete(documentSnapshot.ref);
      });
      return batch.commit();
    } catch (error) {
      console.log('Error deleting conversations:', error);
    }
  };
  const deleteBookings = async () => {
    try {
      const querySnapshot = await firestore()
        .collection('Bookings')
        .where('user.uid', '==', user.uid)
        .get();

      const batch = firestore().batch();
      querySnapshot.forEach(documentSnapshot => {
        batch.delete(documentSnapshot.ref);
      });
      return batch.commit();
    } catch (error) {
      console.log('Error deleting conversations:', error);
    }
  };

  const deleteStoragePicture = async (photoUrl: string) => {
    const filePath = decodeURIComponent(photoUrl.split('/o/')[1].split('?')[0]);
    const fileRef = storage().ref(filePath);
    await fileRef.delete();
  };

  const deleteUserAccount = async () => {
    setIsLoading(true);
    if (user.profilePicture) deleteStoragePicture(user.profilePicture);
    if (user.idBackSide) deleteStoragePicture(user.idBackSide);
    if (user.idFrontSide) deleteStoragePicture(user.idFrontSide);
    const deletePromises = pet.petsData.map(petData => {
      if (petData.picture) deleteStoragePicture(petData.picture);
    });
    // await Promise.all(deletePromises);

    reauthenticate()?.then(() => {
      let user = auth().currentUser;
      {
        user &&
          user
            .delete()
            .then(async () => {
              await firestore().collection('deletedAccounts').doc().set({
                firstName: formik.values.firstName,
                surName: formik.values.surName,
                emailAddress: formik.values.email,
                deleteReason: formik.values.message,
              });
              await deleteAllUserPets();
              await deleteConversations();
              await deleteBookings();
              await deleteUserData();
              handleLogout();
              Toast.show({
                type: 'success',
                text1: t('Success'),
                text2: t('You have deleted your account successfully'),
              });
            })
            .catch(error => {
              console.log('error while deleting user', error);
              Toast.show({
                type: 'error',
                text2: t('Sorry, could not delete your account at the moment'),
              });
            })
            .finally(() => {
              setIsLoading(false);
            });
      }
    });
  };
  const {passwordVisibility, rightIcon, handlePasswordVisibility} =
    useTogglePasswordVisibility();

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      firstName: user.firstName,
      surName: user.surName,
      email: user.email,
      password: '',
      message: '',
      deleteCheck: false,
    },
    onSubmit: deleteUserAccount,
    validateOnMount: true,
    validationSchema: validationSchema,
  });

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: verticalScale(32)}}>
        <Text style={appStyles.title}>{t('Delete Account')}</Text>
        <View style={{marginTop: 24}}>
          <FormInput
            label={t('First Name')}
            placeholder={t('First Name')}
            placeholderTextColor={COLORS.placeHolder}
            onChangeText={formik.handleChange('firstName')}
            onBlur={() => formik.setFieldTouched('firstName')}
            value={user.firstName}
            editable={false}
          />
          <AppText
            error={formik.errors.firstName}
            visible={!!formik.touched.firstName}
          />
          <FormInput
            label={t('Surname')}
            placeholder={t('Surname')}
            placeholderTextColor={COLORS.placeHolder}
            onChangeText={formik.handleChange('surName')}
            onBlur={() => formik.setFieldTouched('surName')}
            value={user.surName}
            editable={false}
            containerStyle={styles.margin}
          />
          <AppText
            error={formik.errors.surName}
            visible={!!formik.touched.surName}
          />

          <FormInput
            label={t('Email Address')}
            placeholder={t('Enter your email')}
            placeholderTextColor={COLORS.placeHolder}
            containerStyle={styles.margin}
            value={user.email}
            editable={false}
          />
          <AppText
            error={formik.errors.email}
            visible={!!formik.touched.email}
          />
          <FormInput
            label={t('Password')}
            placeholder={t('Min. 8 characters')}
            placeholderTextColor={COLORS.placeHolder}
            containerStyle={styles.passwordInputFeild}
            secureTextEntry={passwordVisibility}
            rightIcon={rightIcon}
            onRightIconPress={handlePasswordVisibility}
            isPassword
            onChangeText={formik.handleChange('password')}
            onBlur={() => formik.setFieldTouched('password')}
          />
          <AppText
            error={formik.errors.password}
            visible={!!formik.touched.password}
          />
          <Message
            label={t('Reason for deleting')}
            placeholder={t('Type here...')}
            placeholderTextColor={COLORS.placeHolder}
            onChangeText={formik.handleChange('message')}
            onBlur={() => formik.setFieldTouched('message')}
            value={formik.values.message}
          />
          <AppText
            error={formik.errors.message}
            visible={!!formik.touched.message}
          />
          <TouchableOpacity style={styles.privacyPolicy} onPress={toggleButton}>
            <View>
              {selected ? <Check2 /> : <View style={styles.emptyView} />}
            </View>
            <Text style={styles.text}>{t('I want to delete my account.')}</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.footer}>
          <TextButton
            disabled={!formik.isValid || isLoading}
            label={t('Submit')}
            onPress={formik.handleSubmit}
            isLoading={isLoading}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default DeleteAccount;
