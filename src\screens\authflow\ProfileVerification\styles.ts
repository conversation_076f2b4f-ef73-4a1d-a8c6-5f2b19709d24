import {StyleSheet, Dimensions} from 'react-native';
import {COLORS, SIZES} from '../../../themes/themes';
import {FONTS} from '../../../themes/fonts';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';

const WIDTH = Dimensions.get('window').width;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  innerContainer: {
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.black,
    paddingBottom: 16,
  },
  margin: {
    marginTop: 14,
  },
  privacyPolicyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  privacyPolicyLabel: {
    fontSize: 14,
    color: COLORS.black,
  },
  idCardContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginTop: SIZES.DeviceHeight > 760 ? 0 : 8,
    alignItems: 'flex-start',
    marginVertical: 10,
    padding: 10,
  },
  label: {
    fontSize: 14,
    color: '#2C2C2E',
    fontFamily: FONTS.Medium,
    paddingTop: 12,
  },

  emptyView: {
    width: 18,
    height: 18,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  privacyPolicy: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(16),
  },
  image: {
    width: WIDTH * 0.4,
    height: verticalScale(110),
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.Philippine_Gray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  outerButtonContainer: {
    backgroundColor: COLORS.white,
    padding: 12,
    borderRadius: 16,
    width: '80%',
    alignSelf: 'center',
  },

  button: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    backgroundColor: COLORS.white,
    borderRadius: 40,
  },
  buttonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: verticalScale(12),
    justifyContent: 'space-evenly',
    paddingHorizontal: verticalScale(12),
  },
  innerButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: 12,
    fontFamily: FONTS.Medium,
    color: COLORS.black,
    marginTop: verticalScale(8),
  },
  imageContainer: {
    width: 110,
    height: 110,
    borderRadius: 110 / 2,
    alignSelf: 'center',
    marginTop: 14,
  },
  imageStyles: {
    width: '100%',
    height: '100%',
    borderRadius: 110,
  },
  cameraIcon: {
    position: 'absolute',
    right: 12,
    bottom: 0,
  },
  icon: {
    width: 24,
    height: 24,
  },
  labelText: {
    fontSize: moderateScale(14),
    color: COLORS.Bastille,
    marginBottom: verticalScale(6),
    fontFamily: FONTS.Medium,
    marginTop: 14,
  },
  searchContainer: {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: COLORS.lightGray,
    // height: verticalScale(48),
    paddingHorizontal: scale(12),
    alignItems: 'center',
    justifyContent: 'center',
  },
  uploadText: {
    fontFamily: FONTS.Medium,
    fontSize: 12,
    color: COLORS.Philippine_Gray,
  },
  uploadImage: {height: 35, width: 35, marginTop: 20},
  uploadTitle: {fontFamily: FONTS.Medium, fontSize: 16},
});
