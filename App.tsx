import 'react-native-gesture-handler';
import {
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {createContext, useContext, useEffect, useState} from 'react';
import SystemNavigationBar from 'react-native-system-navigation-bar';
import {COLORS} from './src/themes/themes';
import Navigation from './src/navigation';
import {MenuProvider} from 'react-native-popup-menu';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SplashScreen from 'react-native-splash-screen';
import Splash from './src/screens/authflow/SplashScreen';
import Toast from 'react-native-toast-message';
import {Provider, useDispatch} from 'react-redux';
import {store, useAppSelector} from './src/store/Store';
import {requestTrackingPermission} from 'react-native-tracking-transparency';

// import {addPet} from './src/slices/petSlice';
// import {StripeProvider} from '@stripe/stripe-react-native';
import {LanguageProvider} from './src/contexts/LanguageContext';
import {LocaleConfig} from 'react-native-calendars';
import mobileAds from 'react-native-google-mobile-ads';
import {check, request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import firestore from '@react-native-firebase/firestore';
import useSession from './src/hooks/useSession';
// import {GoogleSignin} from '@react-native-google-signin/google-signin';

interface AuthContextProps {
  isOwner: boolean;
  setIsOwner: (isOwner: boolean) => void;
  userId: string;
  setUserId: (userId: string) => void;
  firstLaunch: boolean;
  setFirstLaunch: (firstLaunch: boolean) => void;
  onlineStatus: any;
  setOnlineStatus: any;
}

export const AuthContext = createContext<AuthContextProps>({
  isOwner: false,
  setIsOwner: () => null,
  userId: '',
  setUserId: () => null,
  firstLaunch: true,
  setFirstLaunch: () => null,
  onlineStatus: {},
  setOnlineStatus: () => null,
});

LocaleConfig.locales.en = {
  monthNames: [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ],
  monthNamesShort: [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],
  dayNames: [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ],
  dayNamesShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
};
LocaleConfig.locales.de = {
  monthNames: [
    'Januar',
    'Februar',
    'März',
    'April',
    'Mai',
    'Juni',
    'Juli',
    'August',
    'September',
    'Oktober',
    'November',
    'Dezember',
  ],
  monthNamesShort: [
    'Jan',
    'Feb',
    'Mär',
    'Apr',
    'Mai',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Okt',
    'Nov',
    'Dez',
  ],
  dayNames: [
    'Sonntag',
    'Montag',
    'Dienstag',
    'Mittwoch',
    'Donnerstag',
    'Freitag',
    'Samstag',
  ],
  dayNamesShort: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
};
LocaleConfig.locales.fr = {
  monthNames: [
    'Janvier',
    'Février',
    'Mars',
    'Avril',
    'Mai',
    'Juin',
    'Juillet',
    'Août',
    'Septembre',
    'Octobre',
    'Novembre',
    'Décembre',
  ],
  monthNamesShort: [
    'Janv.',
    'Févr.',
    'Mars',
    'Avril',
    'Mai',
    'Juin',
    'Juil.',
    'Août',
    'Sept.',
    'Oct.',
    'Nov.',
    'Déc.',
  ],
  dayNames: [
    'Dimanche',
    'Lundi',
    'Mardi',
    'Mercredi',
    'Jeudi',
    'Vendredi',
    'Samedi',
  ],
  dayNamesShort: ['Dim.', 'Lun.', 'Mar.', 'Mer.', 'Jeu.', 'Ven.', 'Sam.'],
};

LocaleConfig.locales.nl = {
  monthNames: [
    'Januari',
    'Februari',
    'Maart',
    'April',
    'Mei',
    'Juni',
    'Juli',
    'Augustus',
    'September',
    'Oktober',
    'November',
    'December',
  ],
  monthNamesShort: [
    'Jan.',
    'Feb.',
    'Mrt.',
    'Apr.',
    'Mei',
    'Jun.',
    'Jul.',
    'Aug.',
    'Sep.',
    'Okt.',
    'Nov.',
    'Dec.',
  ],
  dayNames: [
    'Zondag',
    'Maandag',
    'Dinsdag',
    'Woensdag',
    'Donderdag',
    'Vrijdag',
    'Zaterdag',
  ],
  dayNamesShort: ['Zo.', 'Ma.', 'Di.', 'Wo.', 'Do.', 'Vr.', 'Za.'],
};

const App = () => {
  const [isOwner, setIsOwner] = useState<boolean>(false);
  const [userId, setUserId] = useState<string>('');
  const [firstLaunch, setFirstLaunch] = useState<boolean>(true);
  const [loadingUser, setLoadingUser] = useState<boolean>(true);
  const [onlineStatus, setOnlineStatus] = useState(null);
  useEffect(() => {
    SplashScreen.hide();
  }, []);
  const checkAppPermissions = async () => {
    const result = await check(PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY);

    console.log('Results', result);
    // if (result === RESULTS.DENIED) {
    // The permission has not been requested, so request it.
    await request(PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY);
    // }

    mobileAds()
      .initialize()
      .then(adapterStatuses => {
        console.log('Adapter statuses:', adapterStatuses);
      })
      .catch(error => {
        console.log('Error initializing ads:', error);
      });
  };

  useEffect(() => {
    const initApp = async () => {
      const status = await requestTrackingPermission();

      // Optional: you can check for 'authorized' if needed
      console.log('ATT status:', status);

      // Initialize AdMob only after ATT prompt
      // await mobileAds().initialize();
    };

    initApp();
  }, []);

  useEffect(() => {
    SystemNavigationBar.setNavigationColor(COLORS.white);
    checkAppPermissions();
  }, []);

  useEffect(() => {
    retrieveData();
  }, []);

  useEffect(() => {
    setUserLocally(userId);
  }, [userId]);

  const retrieveData = async () => {
    const currentUserId = await AsyncStorage.getItem('UserId');
    const appData = await AsyncStorage.getItem('appLaunched');

    if (appData === 'true') {
      setFirstLaunch(true);
    } else if (appData === 'false') {
      setFirstLaunch(false);
    }

    if (currentUserId) {
      setUserId(currentUserId);
    }
    setLoadingUser(false);
  };

  const setUserLocally = (id: string) => {
    if (id) {
      AsyncStorage.setItem('UserId', id);
    }
  };

  if (loadingUser)
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size={'large'} color={COLORS.primary} />
      </View>
    );

  return (
    <Provider store={store}>
      <AuthContext.Provider
        value={{
          isOwner,
          setIsOwner,
          userId,
          setUserId,
          firstLaunch,
          setFirstLaunch,
          onlineStatus,
          setOnlineStatus,
        }}>
        <LanguageProvider>
          <StatusBar backgroundColor={COLORS.white} barStyle={'dark-content'} />
          <AppContent />
        </LanguageProvider>
      </AuthContext.Provider>
    </Provider>
  );
};

export default App;

const AppContent = () => {
  const [showAnimation, setShowAnimation] = useState<boolean>(true);
  const {updateReduxData} = useSession();
  const {setUserId} = useContext(AuthContext);

  useEffect(() => {
    // SplashScreen.hide();
    setTimeout(() => {
      setShowAnimation(false);
    }, 3000);
  }, []);

  const loadSession = async () => {
    const uid = await AsyncStorage.getItem('uid');
    if (uid) {
      updateReduxData(uid);
    }
  };

  useEffect(() => {
    loadSession();
  }, []);
  const user = useAppSelector(state => state.user);

  const userBlockStatus = async () => {
    try {
      const uid = await AsyncStorage.getItem('uid');
      if (uid) {
        const userDocRef = firestore().collection('Users').doc(uid);

        userDocRef.onSnapshot(
          async docSnapshot => {
            const userData = docSnapshot.data();
            if (userData?.isBlocked) {
              console.log(userData.isBlocked);

              // const isGoogleSignIn = await GoogleSignin.isSignedIn();
              // if (isGoogleSignIn) {
              //   GoogleSignin.signOut();
              // }
              setUserId('');
              AsyncStorage.removeItem('UserId');
              await firestore()
                .collection('Users')
                .doc(uid)
                .update({fcmToken: ''});
            }
          },
          error => {
            console.log('error signing out when blocked', error);
          },
        );
      }
    } catch (error) {
      console.log('error signing out when blocked', error);
    }
  };

  useEffect(() => {
    userBlockStatus();
  }, [user.uid]);

  return (
    <MenuProvider>
      {showAnimation ? <Splash /> : <Navigation />}
      <Toast />
    </MenuProvider>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
