import {
  FlatList,
  Image,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useLayoutEffect, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import {
  Alert,
  AppText,
  ButtonwithIcon,
  FormInput,
  TextButton,
} from '../../../components';
import ServiceCard from '../../authflow/PetProfile/ServiceCard';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {BackArrow} from '../../../assets/svgIcons';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';
import {ImagePlaceholder2} from '../../../assets/images';
import {COLORS} from '../../../themes/themes';
import ServiceModal from '../../authflow/AddService/ServiceModal';
import {Services} from '../../../constants';
import DropDown from '../EditRequest/DropDown';
import firestore from '@react-native-firebase/firestore';
import {useAppSelector} from '../../../store/Store';
import functions from '@react-native-firebase/functions';
import DatePicker from '../EditRequest/DatePicker';
import Toast from 'react-native-toast-message';
import appStyles from '../../../themes/appStyles';

type Props = NativeStackScreenProps<PetOwnerParamList, 'SelectPetServices'>;
interface DateType {
  startDate: string;
  endDate: string;
}
interface AddServiceForm {
  serviceName: string[];
  price: string;
  date: DateType;
}

const SelectPetServices: React.FC<Props> = ({route, navigation}) => {
  const {item, sitter} = route.params;

  const [isLoading, setIsLoading] = useState(false);
  const [isServiceModalVisible, setServiceModalVisible] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);

  const {t} = useTranslation();
  const user = useAppSelector(state => state.user);
  const validationSchema = Yup.object().shape({
    serviceName: Yup.array().required(t('Enter required services')),
    price: Yup.string().required(t('Enter prices')),
    date: Yup.object()
      .shape({
        startDate: Yup.string().required(),
        endDate: Yup.string().required(),
      })
      .required(t('Enter date')),
  });

  useLayoutEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: '',
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, [navigation]);

  const handleSendRequest = async () => {
    try {
      setIsLoading(true);
      const bookingPayload = {
        petId: item.id,
        userId: user.uid,
        sitterId: sitter.uid,
        services: formik.values.serviceName,
        price: formik.values.price,
        status: 'pending',
        bookingDate: formik.values.date,
        createdAt: new Date(),
        updatedAt: new Date(),
        coordinates: user.coordinates,
      };

      const payload = {
        senderId: user.uid,
        senderName: user.firstName,
        receiverId: sitter.uid,
        receiverToken: sitter?.fcmToken,
        createdAt: new Date(),
        updatedAt: new Date(),
        booking: bookingPayload,
      };

      await functions().httpsCallable('sendInterestedNotifications')(payload);

      await firestore()
        .collection('Bookings')
        .add(bookingPayload)
        .then(async booking => {
          await firestore()
            .collection('Requests')
            .add({
              bookingId: booking.id,
              senderId: user.uid,
              receiverId: sitter.uid,
            })
            .then(() => {
              setModalVisible(true);
              setTimeout(() => {
                navigation.popToTop();
              }, 2000);
            });
        });
    } catch (error) {
      console.log('Error sending request', error);
      Toast.show({
        type: 'error',
        text2: 'Error sending request',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<AddServiceForm>({
    initialValues: {
      serviceName: [],
      price: '',
      date: {startDate: null, endDate: null},
    },
    onSubmit: handleSendRequest,
    validateOnMount: true,
    validationSchema: validationSchema,
  });

  const handleRemoveService = (removingService: string) => {
    const updatedServices = formik.values.serviceName.filter(
      item => item != removingService,
    );
    formik.setFieldValue('serviceName', updatedServices);
  };
  const renderService = ({item}: {item: string}) => (
    <ServiceCard serviceName={item} onPress={() => handleRemoveService(item)} />
  );

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <ScrollView
        contentContainerStyle={{flexGrow: 1}}
        showsVerticalScrollIndicator={false}>
        <View style={{flex: 1, justifyContent: 'space-between'}}>
          <View>
            <View style={styles.crossContainer}>
              <Text style={styles.paragraph}>{t('Pet Profile Picture')}</Text>
            </View>
            <View style={styles.imageContainer}>
              <Image
                source={item.picture ? {uri: item.picture} : ImagePlaceholder2}
                style={styles.imageStyles}
              />
            </View>

            <View style={{paddingTop: 12}}>
              <FormInput
                placeholder={t('Pet name')}
                placeholderTextColor={COLORS.placeHolder}
                label={t('Pet Name')}
                value={item.name || ''}
                editable={false}
              />

              <View style={styles.innerContainer}>
                <FormInput
                  placeholder={t('Pet Age')}
                  placeholderTextColor={COLORS.placeHolder}
                  label={t('Age')}
                  containerStyle={{width: '48%'}}
                  value={item.age?.toString()}
                  editable={false}
                />
                <View style={{width: '4%'}} />
                <FormInput
                  placeholder={t('Age Unit')}
                  placeholderTextColor={COLORS.placeHolder}
                  label={t('Age')}
                  containerStyle={{width: '48%'}}
                  value={item.ageUnit}
                  editable={false}
                />
              </View>
              <DatePicker
                date={formik.values.date}
                setSelectedDate={(startDate, endDate) => {
                  formik.setFieldValue('date', {
                    startDate: startDate,
                    endDate: endDate,
                  });
                }}
              />
              <AppText
                error={formik.errors.date?.startDate}
                visible={!!formik.touched.date}
              />
              <FormInput
                placeholder={t('Price')}
                placeholderTextColor={COLORS.placeHolder}
                label={t('Price')}
                containerStyle={{width: '100%', marginTop: 16}}
                onChangeText={formik.handleChange('price')}
                onBlur={formik.handleBlur('price')}
                value={formik.values.price}
                keyboardType="numeric"
              />
              <AppText
                error={formik.errors.price}
                visible={!!formik.touched.price}
              />
              <DropDown
                label={t('Service')}
                placeholder={t('Add services')}
                color={COLORS.placeHolder}
                onPress={() => setServiceModalVisible(true)}
              />
              <AppText
                error={formik.errors.serviceName?.toString()}
                visible={!!formik.touched.serviceName}
              />
              {formik.values.serviceName ? (
                <FlatList
                  data={formik.values.serviceName}
                  renderItem={renderService}
                  contentContainerStyle={{marginTop: 16}}
                />
              ) : null}
            </View>
          </View>
          <TextButton
            label={t('Send Request')}
            onPress={formik.handleSubmit}
            isLoading={isLoading}
            disabled={!formik.isValid || isLoading}
            containerStyle={{marginBottom: 40}}
          />
        </View>
      </ScrollView>

      <ServiceModal
        isModalVisible={isServiceModalVisible}
        setModalVisible={setServiceModalVisible}
        data={Services}
        setServiceName={value => {
          if (!formik.values.serviceName.includes(value)) {
            formik.setFieldValue('serviceName', [
              ...formik.values.serviceName,
              value,
            ]);
          }
        }}
      />
      <Alert
        onBackdropPress={() => setModalVisible(false)}
        isModalVisible={isModalVisible}
        title={t('Notification Sent')}
        paragraph={t(
          'Notification of your interest has been sent to the pet sitter.',
        )}
      />
    </View>
  );
};

export default SelectPetServices;
