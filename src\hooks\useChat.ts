import firestore, {
  FirebaseFirestoreTypes,
} from '@react-native-firebase/firestore';
import {useEffect, useState} from 'react';
import {GiftedChat, IMessage} from 'react-native-gifted-chat';
import {IChat} from '../interfaces';
import {useAppSelector} from '../store/Store';
import {IConversationBooking} from '../interfaces/IBooking';

export default () => {
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [conversations, setConversations] = useState<IChat[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConversationLoading, setIsConversationLoading] = useState(false);
  const [unreadConversationsCount, setUnreadConversationsCount] = useState(0);

  const user = useAppSelector(state => state.user);
  const getConversationId = (recipientId: string) =>
    recipientId > user.uid
      ? `${user.uid}-${recipientId}`
      : `${recipientId}-${user.uid}`;

  // const onSend = async (
  //   messages: IMessage[],
  //   recipientId: string,
  //   booking?: IConversationBooking,
  // ) => {
  //   const msg = messages[0];

  //   const myMessage = {
  //     ...msg,
  //     sentBy: user.uid,
  //     sentTo: recipientId,
  //     createdAt: new Date(),
  //     read: false,
  //   };

  //   setMessages(previousMessages =>
  //     GiftedChat.append(previousMessages, myMessage),
  //   );

  //   const uid =
  //     recipientId > user.uid
  //       ? user.uid + '-' + recipientId
  //       : recipientId + '-' + user.uid;

  //   firestore()
  //     .collection('Conversations')
  //     .doc(uid)
  //     .set({
  //       members: [user.uid, recipientId],
  //       lastMessage: myMessage,
  //       booking: booking,
  //     })
  //     .then(() => {
  //       firestore()
  //         .collection('Conversations')
  //         .doc(uid)
  //         .collection('messages')
  //         .add(myMessage);
  //     });
  // };

  const onSend = async (
    messages: IMessage[],
    recipientId: string,
    booking?: IConversationBooking,
  ) => {
    const message = messages[0];
    const conversationId = getConversationId(recipientId);
    const messageData = {
      ...message,
      sentBy: user.uid,
      sentTo: recipientId,
      createdAt: new Date(),
      // read: false,
    };

    setMessages(prevMessages => GiftedChat.append(prevMessages, messageData));

    const conversationRef = firestore()
      .collection('Conversations')
      .doc(conversationId);

    await firestore().runTransaction(async transaction => {
      const conversationDoc = await transaction.get(conversationRef);
      const currentUnreadCount = conversationDoc.data()?.unreadCount || {};

      const updatedUnreadCount = {
        ...currentUnreadCount,
        [recipientId]: (currentUnreadCount[recipientId] || 0) + 1,
      };

      transaction.set(conversationRef, {
        members: [user.uid, recipientId],
        lastMessage: messageData,
        booking,
        unreadCount: updatedUnreadCount,
      });
      transaction.set(
        conversationRef.collection('messages').doc(),
        messageData,
      );
    });
  };

  // const getAllMessages = async (recipientId: string) => {
  //   setIsLoading(true);
  //   const uid =
  //     recipientId > user.uid
  //       ? user.uid + '-' + recipientId
  //       : recipientId + '-' + user.uid;

  //   const query = firestore()
  //     .collection('Conversations')
  //     .doc(uid)
  //     .collection('messages')
  //     .orderBy('createdAt', 'desc');

  //   query.onSnapshot(snapShot => {
  //     const allMsg = snapShot.docs.map(docSnap => {
  //       return {
  //         ...docSnap.data(),
  //         createdAt: docSnap.data().createdAt.toDate(),
  //       };
  //     });
  //     setMessages(allMsg);
  //     setIsLoading(false);
  //   });
  // };

  const getAllMessages = async (recipientId: string) => {
    setIsLoading(true);
    const conversationId = getConversationId(recipientId);

    const unsubscribe = firestore()
      .collection('Conversations')
      .doc(conversationId)
      .collection('messages')
      .orderBy('createdAt', 'desc')
      .onSnapshot(snapshot => {
        const fetchedMessages = snapshot.docs.map(doc => ({
          ...doc.data(),
          createdAt: doc.data().createdAt.toDate(),
        }));
        setMessages(fetchedMessages);
        setIsLoading(false);
      });

    return unsubscribe;
  };

  // const getConversations = async () => {
  //   setIsConversationLoading(true);

  //   const query = firestore()
  //     .collection('Conversations')
  //     .where('members', 'array-contains', user.uid)
  //     .orderBy('lastMessage.createdAt', 'desc');

  //   const unsubscribe = query.onSnapshot(
  //     async snapShot => {
  //       let accumulator: IChat[] = [];
  //       let unreadConversationsCount = 0;

  //       const batchGetUnreadMessages = snapShot.docs.map(chat =>
  //         firestore()
  //           .collection('Conversations')
  //           .doc(chat.id)
  //           .collection('messages')
  //           .where('sentBy', '!=', user.uid)
  //           .where('read', '==', false)
  //           .get(),
  //       );

  //       try {
  //         const unreadMessagesResults = await Promise.all(
  //           batchGetUnreadMessages,
  //         );

  //         unreadMessagesResults.forEach((unReadMessagesQuery, index) => {
  //           const unReadMessagesCount = unReadMessagesQuery.docs.length;
  //           const chat = snapShot.docs[index];

  //           if (unReadMessagesCount > 0) {
  //             unreadConversationsCount += 1;
  //           }

  //           accumulator.push({
  //             ...chat.data(),
  //             key: chat.id,
  //             unReadMessages: unReadMessagesCount,
  //           } as IChat);
  //         });

  //         setConversations(accumulator);
  //         setUnreadConversationsCount(unreadConversationsCount);
  //       } catch (error) {
  //         console.log('Error fetching unread messages:', error);
  //       } finally {
  //         setIsConversationLoading(false);
  //       }
  //     },
  //     error => {
  //       console.log('error------', error);
  //       setIsConversationLoading(false);
  //     },
  //   );

  //   return unsubscribe;
  // };

  const getConversations = async () => {
    setIsConversationLoading(true);
    const query = firestore()
      .collection('Conversations')
      .where('members', 'array-contains', user.uid)
      .orderBy('lastMessage.createdAt', 'desc');

    const unsubscribe = query.onSnapshot(snapshot => {
      const fetchedConversations: IChat[] = [];
      let totalUnreadCount = 0;
      if (snapshot?.docs) {
        snapshot.forEach(doc => {
          const data = doc.data();
          const unreadCount = data.unreadCount?.[user.uid] || 0;
          totalUnreadCount += unreadCount;
          fetchedConversations.push({
            ...data,
            key: doc.id,
            unReadMessages: unreadCount,
          } as IChat);
        });
      }

      setConversations(fetchedConversations);
      setUnreadConversationsCount(totalUnreadCount);
      setIsConversationLoading(false);
    });

    return unsubscribe;
  };

  const markReadMessages = async (recipientId: string) => {
    const conversationId = getConversationId(recipientId);
    const batch = firestore().batch();

    // const unreadMessagesSnapshot = await firestore()
    //   .collection('Conversations')
    //   .doc(conversationId)
    //   .collection('messages')
    //   .where('sentBy', '!=', user.uid)
    //   .where('read', '==', false)
    //   .get();

    // unreadMessagesSnapshot.docs.forEach(doc => {
    //   batch.update(doc.ref, {read: true});
    // });

    const conversationRef = firestore()
      .collection('Conversations')
      .doc(conversationId);
    const conversationDoc = await conversationRef.get();

    const lastMessage = conversationDoc.data()?.lastMessage;
    const currentUnreadCount = conversationDoc.data()?.unreadCount || {};

    if (lastMessage && lastMessage.sentBy !== user.uid) {
      const updatedUnreadCount = {
        ...currentUnreadCount,
        [user.uid]: 0,
      };

      batch.update(conversationRef, {
        // 'lastMessage.read': true,
        unreadCount: updatedUnreadCount,
      });
    }

    await batch.commit();
  };

  useEffect(() => {
    const unsubscribe = firestore()
      .collection('Conversations')
      .where('members', 'array-contains', user.uid)
      .onSnapshot(snapshot => {
        let unreadConversationsCount = 0;

        snapshot.forEach(doc => {
          const data = doc.data();
          const userUnreadCount = data.unreadCount?.[user.uid] || 0;
          if (userUnreadCount > 0) {
            unreadConversationsCount += 1;
          }
        });
        setUnreadConversationsCount(unreadConversationsCount);
      });

    return () => unsubscribe();
  }, [user.uid]);

  // const markReadMessages = async (recipientId: string) => {
  //   try {
  //     const uid =
  //       recipientId > user.uid
  //         ? user.uid + '-' + recipientId
  //         : recipientId + '-' + user.uid;

  //     const batch = firestore().batch();

  //     const unreadMessagesRef = await firestore()
  //       .collection('Conversations')
  //       .doc(uid)
  //       .collection('messages')
  //       .where('sentBy', '!=', user.uid)
  //       .where('read', '==', false)
  //       .get();

  //     unreadMessagesRef.forEach(doc => {
  //       batch.update(doc.ref, {read: true});
  //     });

  //     const conversationDoc = await firestore()
  //       .collection('Conversations')
  //       .doc(uid)
  //       .get();
  //     const lastMessage = conversationDoc.data()?.lastMessage;

  //     if (lastMessage && lastMessage.sentBy !== user.uid) {
  //       firestore().collection('Conversations').doc(uid).update({
  //         'lastMessage.read': true,
  //       });
  //       await batch.commit();
  //     }
  //   } catch (error) {
  //     console.log('Error updating read messages', error);
  //   }
  // };

  // const handleUnreadConversations = async () => {
  //   firestore()
  //     .collection('Conversations')
  //     .where('members', 'array-contains', user.uid)
  //     .onSnapshot(querySnapshot => {
  //       let unreadCount = 0;

  //       querySnapshot.forEach(doc => {
  //         const data = doc.data();

  //         if (
  //           data.lastMessage &&
  //           data.lastMessage.read === false &&
  //           data.lastMessage.sentBy !== user.uid
  //         ) {
  //           unreadCount += 1;
  //         }
  //       });

  //       setUnreadConversationsCount(unreadCount);
  //     });
  // };

  return {
    onSend,
    messages,
    getAllMessages,
    getConversations,
    conversations,
    isLoading,
    isConversationLoading,
    markReadMessages,
    unreadConversationsCount,
    // handleUnreadConversations,
  };
};
