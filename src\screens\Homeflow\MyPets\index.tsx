import React, {useEffect, useLayoutEffect, useState} from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  View,
  ScrollView,
} from 'react-native';
import {COLORS} from '../../../themes/themes';
import {
  AlertModal,
  ButtonwithIcon,
  MyPetCard,
  TextButtonwithIcon,
} from '../../../components';
import {BackArrow, Plus} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {FONTS} from '../../../themes/fonts';
import {PetOwnerParamList} from '../../../navigation/PetOwnerHomeStack';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {verticalScale} from 'react-native-size-matters';
import firestore from '@react-native-firebase/firestore';
import {IPet} from '../../../interfaces';
import {useAppSelector} from '../../../store/Store';
import {useTranslation} from 'react-i18next';

type Props = NativeStackScreenProps<PetOwnerParamList, 'MyPets'>;

const MyPets: React.FC<Props> = ({navigation}) => {
  const [isModalVisible, setModalVisible] = useState<boolean>(false);
  const [pets, setPets] = useState<IPet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [itemId, setItemID] = useState<string>();
  const user = useAppSelector(state => state.user);
  const {t} = useTranslation();

  const fetchPetData = () => {
    try {
      const petData = firestore()
        .collection('Pets')
        .where('userId', '==', user.uid);
      petData.onSnapshot(
        snapshot => {
          const _petData: IPet[] = [];
          snapshot.forEach(result => {
            _petData.push({
              ...result.data(),
              id: result.id,
            } as IPet);
          });
          setPets(_petData);
        },
        error => {
          console.log('error', error);
        },
      );
    } catch (error) {
      console.log('Error fetching pet data:', error);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchPetData();
  }, []);

  const handleDeletePress = async () => {
    try {
      await firestore()
        .collection('Pets')
        .doc(itemId)
        .delete()
        .then(() => {
          setModalVisible(false);
          fetchPetData();
        });
    } catch (error) {
      console.log('Error deleting pet:', error);
    }
  };

  useLayoutEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
          hitSlop={appStyles.hitSlop}
        />
      ),
    });
  }, []);

  const onEditPress = (item: IPet) => {
    setTimeout(() => {
      navigation.navigate('PetProfile2', {uid: user.uid, petData: item});
    }, 500);
  };

  return (
    <View style={styles.container}>
      <View style={styles.innerContainer}>
        <ScrollView
          contentContainerStyle={{paddingBottom: 60}}
          showsVerticalScrollIndicator={false}>
          <Text style={[appStyles.title, {fontSize: 18}]}>{t('My Pet')}</Text>

          {isLoading ? (
            <ActivityIndicator
              size={'large'}
              color={COLORS.primary}
              style={{alignSelf: 'center', marginTop: 50}}
            />
          ) : (
            <>
              {pets?.length === 0 ? (
                <Text style={styles.noPetText}>
                  {t('No pets found for this user.')}
                </Text>
              ) : (
                pets.map(item => {
                  return (
                    <MyPetCard
                      key={item?.id}
                      onDeletePress={() => {
                        setItemID(item?.id);
                        setModalVisible(true);
                      }}
                      onEditPress={() => {
                        onEditPress(item);
                      }}
                      isTopRating={false}
                      petName={item?.name}
                      city={user?.city}
                      imageURI={user?.profilePicture || ''}
                      petPicture={item?.picture}
                      onProfilePress={() => navigation.navigate('EditProfile')}
                      onPetPress={() =>
                        navigation.navigate('PetProfileView', {item: item})
                      }
                    />
                  );
                })
              )}
            </>
          )}
        </ScrollView>
        <TextButtonwithIcon
          leftIcon={<Plus />}
          label={t('Add New Pet')}
          containerStyle={styles.buttonContainer}
          labelStyle={styles.labelStyle}
          onPress={() => navigation.navigate('PetProfile2', {uid: user.uid})}
        />
      </View>

      <AlertModal
        isModalVisible={isModalVisible}
        onBackdropPress={() => setModalVisible(false)}
        onCancelPress={() => setModalVisible(false)}
        handleConfirm={handleDeletePress}
        redLabel={t('Delete')}
        title={t('Delete Pet')}
        paragraph={t('You are attempting to delete your pet’s profile.')}
      />
    </View>
  );
};

export default MyPets;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 24,
    paddingTop: verticalScale(8),
    justifyContent: 'space-between',
    paddingBottom: 50,
  },
  innerContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  buttonContainer: {
    width: '100%',
    height: verticalScale(50),
    backgroundColor: '#FFF1E2',
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 5,
  },
  labelStyle: {
    fontSize: 16,
    fontFamily: FONTS.SemiBold,
    color: COLORS.primary,
  },
  noPetText: {
    alignSelf: 'center',
    marginTop: 50,
    fontSize: 16,
    fontFamily: FONTS.SemiBold,
    color: COLORS.Philippine_Gray,
  },
});
