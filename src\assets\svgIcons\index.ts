import BackArrow from './BackArrow.svg';
import Eye from './Eye.svg';
import Eye_off from './Eye_off.svg';
import Facebook from './Facebook.svg';
import Google from './Google.svg';
import ArrowDown from './ArrowDown.svg';
import Visa from './Visa.svg';
import Mastercard from './Mastercard.svg';
import BanContent from './BanContent.svg';
import Plus from './Plus.svg';
import Plus2 from './Plus2.svg';
import DropDown from './DropDown.svg';
import Pencil from './Pencil.svg';
import Magnify from './Magnify.svg';
import Filter2 from './Filter2.svg';
import Star from './Star.svg';
import Bag from './Bag.svg';
import Location from './Location.svg';
import Bell from './Bell.svg';
import Cross from './Cross.svg';
import User from './User.svg';
import Report from './Report.svg';
import Voice from './Voice.svg';
import Stickers from './Stickers.svg';
import Edit from './Edit.svg';
import Pet from './Pet.svg';
import Request from './Request.svg';
import Check2 from './Check2.svg';
import Verticaldot from './Verticaldot.svg';
import Magnify2 from './Magnify2.svg';
import Calenderfill from './Calenderfill.svg';
import ChevronLeft from './ChevronLeft.svg';
import ChevronRight from './ChevronRight.svg';
import Attachment from './Attachment.svg';
import TickIcon from './TickIcon.svg';
import UnitedKingdom from './UnitedKingdom.svg';
import French from './French.svg';
import German from './German.svg';
import Belgian from './Belgian.svg';
import NetherlandFlag from './NetherlandFlag.svg';
import EmptyState from './EmptyState.svg';
import ChatEmptyState from './ChatEmptyState.svg';

export {
  BackArrow,
  Eye,
  Eye_off,
  Facebook,
  Google,
  ArrowDown,
  Visa,
  Mastercard,
  BanContent,
  Plus,
  Plus2,
  DropDown,
  Pencil,
  Magnify,
  Magnify2,
  Filter2,
  Star,
  Bag,
  Location,
  Bell,
  Cross,
  User,
  Report,
  Voice,
  Stickers,
  Edit,
  Pet,
  Request,
  Check2,
  Verticaldot,
  Calenderfill,
  ChevronLeft,
  ChevronRight,
  Attachment,
  TickIcon,
  UnitedKingdom,
  French,
  German,
  Belgian,
  NetherlandFlag,
  EmptyState,
  ChatEmptyState,
};
