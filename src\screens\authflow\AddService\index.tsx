import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {AppText, ButtonwithIcon, TextButton} from '../../../components';
import {BackArrow} from '../../../assets/svgIcons';
import {COLORS} from '../../../themes/themes';
import appStyles from '../../../themes/appStyles';
import {FONTS} from '../../../themes/fonts';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import DropDown from './DropDown';
import {PetLists, Services} from '../../../constants';
import PetModal from './PetModal';
import ServiceModal from './ServiceModal';
import {useTranslation} from 'react-i18next';
import {IService} from '../../../interfaces/IPet';
type Props = NativeStackScreenProps<AuthStackParamList, 'AddAuthService'>;

interface AddServiceForm {
  pet: string;
  name: string;
}

const validationSchema = Yup.object().shape({
  pet: Yup.string().required().label('Pet'),
  name: Yup.string().required().label('Service'),
});

const AddService: React.FC<Props> = ({navigation, route}) => {
  const [petName, setPetname] = useState<string>('');
  const [serviceName, setServiceName] = useState<string>('');
  const [isPetModalVisible, setPetModalVisible] = useState(false);
  const [isServiceModalVisible, setServiceModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  useEffect(() => {
    formik.setFieldValue('pet', petName);
  }, [petName]);
  useEffect(() => {
    formik.setFieldValue('name', serviceName);
  }, [serviceName]);

  const onSubmitHandler = (service: string) => {
    try {
      setIsLoading(true);
      route.params?.addservice(service);
      setTimeout(() => {
        navigation.goBack();
      }, 500);
    } catch (error) {
      console.log('Error submitting service:', error);
    } finally {
      setIsLoading(false);
    }
  };
  const formik = useFormik<AddServiceForm>({
    initialValues: {
      pet: '',
      name: '',
    },
    onSubmit: v => onSubmitHandler(v.name),
    validateOnMount: true,
    validationSchema: validationSchema,
  });

  return (
    <KeyboardAwareScrollView contentContainerStyle={{flex: 1}}>
      <View style={styles.container}>
        <View>
          <Text style={appStyles.title}>{t('Add Service')}</Text>
          <Text style={[appStyles.paragraph, {paddingTop: 10}]}>
            {t('Add your pet service.')}
          </Text>
          <View style={{marginTop: 16}}>
            <DropDown
              label={t('Pet')}
              placeholder={petName ? petName : t('pet')}
              color={formik.values.pet ? '#000' : COLORS.placeHolder}
              onPress={() => setPetModalVisible(true)}
            />
            <AppText error={formik.errors.pet} visible={!!formik.touched.pet} />
            <DropDown
              label={t('Service')}
              placeholder={serviceName ? serviceName : t('Service')}
              color={formik.values.name ? '#000' : COLORS.placeHolder}
              onPress={() => setServiceModalVisible(true)}
            />
            <AppText
              error={formik.errors.name}
              visible={!!formik.touched.name}
            />
          </View>
        </View>
        <TextButton
          label={t('Add Service')}
          onPress={formik.handleSubmit}
          isLoading={isLoading}
          disabled={isLoading || !formik.isValid}
        />
        <PetModal
          isModalVisible={isPetModalVisible}
          setModalVisible={setPetModalVisible}
          data={PetLists}
          setPetname={setPetname}
        />
        <ServiceModal
          isModalVisible={isServiceModalVisible}
          setModalVisible={setServiceModalVisible}
          data={Services}
          setServiceName={setServiceName}
        />
      </View>
    </KeyboardAwareScrollView>
  );
};

export default AddService;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingBottom: 80,
  },
  title: {
    fontSize: 16,
    fontFamily: FONTS.SemiBold,
    color: COLORS.Bastille,
    paddingTop: 18,
  },
});
