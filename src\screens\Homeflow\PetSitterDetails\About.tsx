import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {COLORS} from '../../../themes/themes';
import appStyles from '../../../themes/appStyles';
import {FONTS} from '../../../themes/fonts';

import {useTranslation} from 'react-i18next';

interface Props {
  paragraph: string;
}
const About: React.FC<Props> = ({paragraph}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <Text style={appStyles.title2}>{t('About')}</Text>
      {paragraph ? (
        <Text style={styles.paragraph}>{paragraph}</Text>
      ) : (
        <Text style={styles.paragraph}>{t('No description')}</Text>
      )}
    </View>
  );
};

export default About;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    marginTop: 24,
  },
  paragraph: {
    fontSize: 14,
    color: COLORS.Philippine_Gray,
    fontFamily: FONTS.Medium,
    paddingTop: 12,
  },
  button: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    alignItems: 'center',
    padding: 14,
    borderRadius: 12,
  },
  innerContainer: {
    height: 100,
    backgroundColor: COLORS.white,
    borderColor: COLORS.lightGray,
    borderWidth: 1,
    marginTop: 8,
    padding: 12,
    width: '100%',
  },
  label: {
    fontSize: 14,
    color: COLORS.Bastille,
    textAlign: 'left',
    fontFamily: FONTS.Medium,
  },
  modalButton: {
    width: '48%',
  },
  modalButtonContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 18,
  },
});
