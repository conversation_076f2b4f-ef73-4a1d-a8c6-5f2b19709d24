import {Keyboard, Text, TouchableWithoutFeedback, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {BackArrow} from '../../../assets/svgIcons';
import appStyles from '../../../themes/appStyles';
import {ButtonwithIcon, TextButton} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {styles} from './styles';
import functions from '@react-native-firebase/functions';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import Toast from 'react-native-toast-message';
import firestore from '@react-native-firebase/firestore';
import {useTranslation} from 'react-i18next';
import OtpInput from '../../../components/OtpInput';

type Props = NativeStackScreenProps<AuthStackParamList, 'CodeVerification'>;

const CodeVerification: React.FC<Props> = ({navigation, route}) => {
  const [isLoading, setIsLoading] = useState(false);
  const {uid, phoneNumber} = route.params;
  const {t} = useTranslation();

  const validationSchema = Yup.object().shape({
    verificationCode: Yup.string().required(t('Verification code is required')),
  });

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <ButtonwithIcon
          icon={<BackArrow />}
          onPress={() => navigation.goBack()}
        />
      ),
    });
  }, []);

  const sendOtp = async () => {
    try {
      await functions().httpsCallable('sendOtpCode')({
        phoneNumber: phoneNumber,
      });
      Toast.show({
        type: 'info',
        text2: 'Otp sent',
      });
    } catch (error) {
      console.log('Error in sending otp>>', error);
    }
  };

  useEffect(() => {
    sendOtp();
  }, []);

  const handleCodeVerification = async () => {
    try {
      setIsLoading(true);
      const res = await functions().httpsCallable('verifyOTP')({
        phoneNumber: phoneNumber,
        OTPCode: parseInt(formik.values.verificationCode),
      });

      if (res.data == 'approved') {
        firestore().collection('Users').doc(uid).update({
          isVerified: true,
        });
        navigation.navigate('UploadProfile', {uid: uid});
      } else {
        Toast.show({
          text1: t('Error'),
          text2: t('Verification code is invalid'),
          type: 'error',
        });
      }
    } catch (error) {
      console.error('Verification failed:', error);
      Toast.show({
        text1: t('Error'),
        text2: t('Something went wrong!'),
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };
  const formik = useFormik({
    initialValues: {
      verificationCode: '',
    },
    onSubmit: handleCodeVerification,
    validateOnMount: true,
    validationSchema: validationSchema,
  });

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <View style={styles.container}>
        <View>
          <Text style={appStyles.title}>{t('Verification code')}</Text>
          <Text style={[appStyles.paragraph, {marginTop: 16}]}>
            {t('Enter verification code sent to your entered phone number.')}{' '}
            {phoneNumber}
          </Text>
          {/* <OTPInputView
            style={styles.optView}
            pinCount={4}
            autoFocusOnLoad={false}
            keyboardAppearance="dark"
            codeInputFieldStyle={styles.underlineStyleBase}
            codeInputHighlightStyle={styles.underlineStyleHighLighted}
            onCodeFilled={code => {
              formik.handleChange('verificationCode')(code);
            }}
          /> */}
          <OtpInput
            length={4}
            onComplete={otp => {
              console.log('Otp', otp);
              formik.handleChange('verificationCode')(otp);

              // setOtp(otp);
            }}
          />
          {formik.touched.verificationCode &&
            formik.errors.verificationCode && (
              <Text style={{color: 'red', marginTop: 5}}>
                {formik.errors.verificationCode}
              </Text>
            )}

          <Text style={styles.OtpResend}>
            {t(`Didn't recieve code?`)}{' '}
            <Text style={styles.otpPrimary} onPress={sendOtp}>
              {t('Resend Code')}
            </Text>
          </Text>
        </View>
        <TextButton
          label={t('Verify')}
          onPress={formik.handleSubmit}
          isShadow
          isLoading={isLoading}
          disabled={isLoading}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default CodeVerification;
