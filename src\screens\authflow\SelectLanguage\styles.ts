import {StyleSheet} from 'react-native';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';
import {FONTS} from '../../../themes/fonts';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF',
  },
  innerContainer: {
    flex: 1,
    paddingHorizontal: scale(24),
    justifyContent: 'space-between',
  },
  headerText: {
    textAlign: 'center',
    fontSize: moderateScale(16),
    fontFamily: FONTS.Bold,
    color: '#2C2C2E',
    marginTop: verticalScale(20),
    marginBottom: verticalScale(34),
  },
  languageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: verticalScale(14),
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
    marginTop: verticalScale(14),
    justifyContent: 'space-between',
  },
  languageText: {
    marginLeft: scale(8),
    fontFamily: FONTS.Medium,
    fontSize: 14,
    color: '#2C2C2E',
  },
});
