import {
  TextInput,
  TouchableOpacity,
  View,
  TextInputProps,
  ViewStyle,
  TextStyle,
  KeyboardTypeOptions,
  NativeSyntheticEvent,
  TextInputFocusEventData,
  Text,
} from 'react-native';
import React from 'react';
import {Eye, Eye_off} from '../../assets/svgIcons';
import {styles} from './styles';
import {COLORS} from '../../themes/themes';

type Props = TextInputProps & {
  label?: string | undefined;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  rightIcon?: string;
  placeholder?: string;
  placeholderTextColor?: string;
  keyboardType?: KeyboardTypeOptions;
  secureTextEntry?: boolean;
  onRightIconPress?: () => void;
  onChangeText?: (text: string) => void;
  onBlur?: (e: NativeSyntheticEvent<TextInputFocusEventData>) => void;
  isPassword?: boolean;
  innerContainer?: ViewStyle;
  editable?: boolean;
};

const FormInput: React.FC<Props> = ({
  placeholder,
  placeholderTextColor,
  onChangeText,
  value,
  secureTextEntry,
  keyboardType,
  containerStyle,
  onBlur,
  multiline,
  onRightIconPress,
  label,
  rightIcon,
  isPassword,
  innerContainer,
  labelStyle,
  editable = true,
  ...rest
}) => {
  return (
    <View style={[containerStyle]}>
      <Text style={[styles.label, labelStyle]}>{label}</Text>
      <View style={[styles.innerContainer, innerContainer]}>
        <TextInput
          placeholder={placeholder}
          placeholderTextColor={placeholderTextColor}
          onChangeText={onChangeText}
          value={value}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          multiline={multiline}
          style={[
            styles.textInputStyles,
            {
              color: editable ? COLORS.black : COLORS.Philippine_Gray,
            },
          ]}
          onBlur={onBlur}
          editable={editable}
          {...rest}
        />

        {isPassword ? (
          <TouchableOpacity
            onPress={onRightIconPress}
            hitSlop={{
              left: 5,
              right: 5,
              top: 15,
              bottom: 15,
            }}>
            {rightIcon == 'eye' ? <Eye /> : <Eye_off />}
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default FormInput;
