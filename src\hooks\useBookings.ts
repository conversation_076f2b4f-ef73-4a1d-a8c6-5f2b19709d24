import {useEffect, useState} from 'react';
import firestore from '@react-native-firebase/firestore';
import {useAppSelector} from '../store/Store';
import {IRequest} from '../interfaces/IRequest';

const useBookings = () => {
  const getBookingRequests = async (
    condition: 'senderId' | 'receiverId',
    uid: string,
  ) => {
    const querySnapshot = await firestore()
      .collection('Requests')
      .where(condition, '==', uid)
      .get();

    const bookingsPromises = querySnapshot.docs.map(async doc => {
      const bookingDoc = await firestore()
        .collection('Bookings')
        .doc(doc.data().bookingId)
        .get();
      if (!bookingDoc.exists) return null;

      const bookingData = bookingDoc.data();
      const userDoc = await firestore()
        .collection('Users')
        .doc(bookingData?.userId)
        .get();
      const petDoc = await firestore()
        .collection('Pets')
        .doc(bookingData?.petId)
        .get();

      return {
        ...doc.data(),
        id: doc.id,
        bookingData,
        bookingId: bookingDoc.id,
        petData: petDoc.data(),
        userData: userDoc.data(),
      } as IRequest;
    });

    const bookingsData = await Promise.all(bookingsPromises);
    return bookingsData.filter(
      (booking): booking is IRequest => booking !== null,
    );
  };

  return {getBookingRequests};
};

export default useBookings;
