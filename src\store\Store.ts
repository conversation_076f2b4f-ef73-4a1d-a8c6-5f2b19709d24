import {configureStore} from '@reduxjs/toolkit';
import userSlice from '../slices/userSlice';
import petSlice from '../slices/petSlice';
import {TypedUseSelectorHook, useSelector, useDispatch} from 'react-redux';
import petOwnerSlice from '../slices/petOwnerSlice';

export const store = configureStore({
  reducer: {
    user: userSlice.reducer,
    pets: petSlice.reducer,
    petOwner: petOwnerSlice.reducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
export type AppDispatch = typeof store.dispatch;
export const useAppDispatch: () => AppDispatch = useDispatch;
